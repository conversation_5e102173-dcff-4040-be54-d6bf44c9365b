<x-filament::page>
    <form wire:submit.prevent="save">
        {{ $this->form }}

        @if (count($this->toOffboard))
            <x-filament::card class="mb-6 border-red-300 bg-red-50">
                <div class="font-bold text-red-700 flex items-center gap-2 mb-2">
                    <x-filament::icon name="heroicon-o-exclamation-triangle" class="w-5 h-5" />
                    Crew to Offboard
                </div>
                <ul class="list-disc ml-5 text-red-600">
                    @foreach ($this->toOffboard as $crew)
                        <li>
                            {{ $crew->display_name }} ({{ $crew->unique_crew_id }})
                            <span class="text-xs">No longer meets requirements for any position in this Ship/Mark</span>
                        </li>
                    @endforeach
                </ul>
            </x-filament::card>
        @endif

        <div class="mt-6 flex justify-end">
            <x-filament::button type="submit" color="primary">
                Save Assignments
            </x-filament::button>
        </div>
    </form>
</x-filament::page>
