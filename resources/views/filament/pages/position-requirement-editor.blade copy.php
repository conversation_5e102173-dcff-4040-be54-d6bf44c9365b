{{-- <x-filament::page>
    <form wire:submit.prevent>
        {{ $form }}

        @if ($position)
            <div class="mt-8">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-bold">
                        Position: {{ $position->ship->name }} / {{ $position->shipMark->name }} /
                        {{ $position->designation->name }}
                    </h3>
                    <x-filament::button type="button" wire:click="addRequirementGroup">
                        Add Group
                    </x-filament::button>
                </div>
                <div class="space-y-8">


                    @foreach ($groups as $groupIndex => $group)
                        <div class="bg-gray-100 rounded-xl p-4 mb-4">

                            <div class="bg-white rounded-xl shadow p-4 border">
                                <div class="flex items-center justify-between mb-2">
                                        <input type="text"
                                    class="text-lg font-bold bg-transparent border-b border-blue-300 focus:border-blue-600 focus:outline-none"
                                    wire:model.lazy="groups.{{ $groupIndex }}.name"
                                    wire:change="saveGroupName({{ $group['id'] }}, $event.target.value)"
                                    style="width: 75%;" />
                                    <x-filament::button color="danger" size="sm"
                                        wire:click="deleteRequirementGroup({{ $group['id'] }})">
                                        Delete Group
                                    </x-filament::button>
                                </div>
                                <div class="text-gray-500 mb-3">{{ $group['description'] }}</div>
                                <div class="flex flex-wrap gap-4">
                                    @foreach ($group['sets'] as $set)
                                        <div class="bg-gray-50 rounded-lg p-3 border min-w-[260px]">
                                            <div class="flex items-center justify-between mb-1">
                                                {{-- Editable Set Name --}}
                                                <input type="text"
                                                    class="text-blue-900 font-medium bg-transparent border-b border-blue-200 focus:border-blue-500 focus:outline-none"
                                                    value="{{ $set['name'] }}"
                                                    wire:model.lazy="groups.{{ $loop->parent->index }}.sets.{{ $loop->index }}.name"
                                                    wire:change="saveSetName({{ $set['id'] }}, $event.target.value)"
                                                    style="width: 80%;" />

                                                <x-filament::button color="danger" size="xs"
                                                    wire:click="deleteSet({{ $set['id'] }})">✕</x-filament::button>
                                            </div>
                                            {{-- Logic type and min required --}}
                                            <div class="flex items-center mb-2 gap-2 text-xs">
                                                <select
                                                    wire:model.lazy="groups.{{ $loop->parent->index }}.sets.{{ $loop->index }}.logic_type"
                                                    wire:change="saveSetLogicType({{ $set['id'] }}, $event.target.value)"
                                                    class="rounded border-gray-200 text-xs">
                                                    <option value="ALL">ALL</option>
                                                    <option value="ANY">ANY</option>
                                                </select>
                                                @if ($set['logic_type'] === 'ANY')
                                                    <input type="number" min="1" placeholder="Min Required"
                                                        class="border px-1 py-0.5 rounded text-xs w-14"
                                                        wire:model.lazy="groups.{{ $loop->parent->index }}.sets.{{ $loop->index }}.min_required"
                                                        wire:change="saveSetMinRequired({{ $set['id'] }}, $event.target.value)" />
                                                @endif
                                            </div>
                                            {{-- Certificates List --}}
                                            <div>
                                                <ul class="space-y-1">
                                                    @foreach ($set['certificates'] as $cert)
                                                        <li class="flex items-center gap-2">
                                                            <span
                                                                class="inline-block px-2 py-0.5 rounded bg-blue-100 text-blue-900 text-xs">
                                                                {{ $cert['certificate_name'] }}
                                                            </span>
                                                            <x-filament::button color="danger" size="xs"
                                                                wire:click="deleteCertificate({{ $cert['id'] }})">✕</x-filament::button>
                                                        </li>
                                                    @endforeach
                                                </ul>
                                                {{-- Add certificate to set, exclude already added --}}
                                                <form
                                                    wire:submit.prevent="addCertificateToSet({{ $set['id'] }}, $event.target.certificate_type_id.value)">
                                                    <div class="flex mt-2 gap-2">
                                                        <select
                                                            wire:model.defer="newCertificateTypeId.{{ $set['id'] }}"
                                                            class="border rounded px-2 py-1 flex-1">
                                                            <option value="">Add certificate…</option>
                                                            @php
                                                                $addedIds = collect($set['certificates'])
                                                                    ->pluck('certificate_type_id')
                                                                    ->all();
                                                            @endphp
                                                            @foreach ($allCertificateTypes as $id => $name)
                                                                @if (!in_array($id, $addedIds))
                                                                    <option value="{{ $id }}">
                                                                        {{ $name }}</option>
                                                                @endif
                                                            @endforeach
                                                        </select>
                                                        <x-filament::button color="primary" size="xs"
                                                            type="button"
                                                            wire:click="addCertificateToSet({{ $set['id'] }})">
                                                            Add
                                                        </x-filament::button>
                                                    </div>
                                                </form>
                                            </div>
                                        </div>
                                    @endforeach
                                    {{-- Add set to group --}}
                                    <div class="flex flex-col justify-center items-center">
                                        <x-filament::button color="info"
                                            wire:click="addSetToGroup({{ $group['id'] }})">
                                            + Add Set
                                        </x-filament::button>
                                    </div>
                                </div>
                            </div>
                    @endforeach
                </div>
            </div>
        @endif
    </form>
</x-filament::page> --}}
