<x-filament::widget>
    <x-filament::card>
        <div class="flex flex-col gap-4">
            <div class="flex items-center justify-between">
                <h2 class="text-xl font-bold tracking-tight">ROB Metrics — Current Status</h2>
                <x-filament::badge color="primary">
                    {{ now()->format('d M Y, H:i') }}
                </x-filament::badge>
            </div>
            <div class="grid gap-4 sm:grid-cols-2 md:grid-cols-3">
                @foreach ($this->getMetrics() as $metric)
                    <div class="flex flex-col gap-2 p-4 rounded-xl border bg-white shadow-sm">
                        {{-- Header: Icon + Asset Name --}}
                        <div class="flex items-center gap-2">
                            @php
                                $asset = strtolower($metric['asset']);
                            @endphp
                            @if(Str::contains($asset, 'fuel'))
                                <x-heroicon-o-fire class="w-5 h-5 text-danger-500" />
                            @elseif(Str::contains($asset, 'engine'))
                                <x-heroicon-o-cog class="w-5 h-5 text-primary-500" />
                            @elseif(Str::contains($asset, 'generator') || Str::contains($asset, 'dg'))
                                <x-heroicon-o-cpu-chip class="w-5 h-5 text-info-500" />
                            @else
                                <x-heroicon-o-clipboard-document-list class="w-5 h-5 text-secondary-400" />
                            @endif
                            <span class="font-medium text-gray-800">{{ $metric['asset'] }}</span>
                        </div>

                        {{-- Metric Type --}}
                        <div class="flex items-center gap-2">
                            <span class="font-semibold text-gray-600">{{ $metric['metric'] }}</span>
                            @if($metric['unit'])
                                <x-filament::badge color="gray">{{ $metric['unit'] }}</x-filament::badge>
                            @endif
                        </div>

                        {{-- MAIN: Current Level  --}}
                        <div class="flex items-baseline gap-2 my-1">
                            <span class="text-3xl font-bold text-primary-700">
                                {{ $metric['current_level'] ?? $metric['current'] ?? '-' }}
                            </span>
                            @if($metric['unit'])
                                <span class="text-base text-gray-500">{{ $metric['unit'] }}</span>
                            @endif
                        </div>

                        {{-- Status Badges --}}
                        <div class="flex flex-wrap items-baseline gap-2">
                            @if(isset($metric['synced']) && !$metric['synced'])
                                <x-filament::badge color="warning" icon="heroicon-o-exclamation-triangle">
                                    Needs Log
                                </x-filament::badge>
                            @elseif(isset($metric['synced']) && $metric['synced'])
                                <x-filament::badge color="success" icon="heroicon-o-check">
                                    Up-to-date
                                </x-filament::badge>
                            @endif
                            {{-- How recent is the data? --}}
                            @if($metric['last_reading'])
                                @php
                                    $diff = now()->diffInMinutes($metric['last_reading']);
                                    $recent = $diff < 60 ? 'success' : ($diff < 1440 ? 'warning' : 'danger');
                                @endphp
                                <x-filament::badge color="{{ $recent }}">
                                    {{ $metric['last_reading']->diffForHumans() }}
                                </x-filament::badge>
                            @else
                                <x-filament::badge color="gray">No Data</x-filament::badge>
                            @endif
                        </div>

                        {{-- LAST LOG READING AT THE BOTTOM --}}
                        <div class="text-xs text-gray-400">
                            <span>
                                Last log:
                                <b>{{ $metric['last_log_value'] ?? '-' }} {{ $metric['unit'] ?? '' }}</b>
                                <span class="ml-1">
                                    @if($metric['last_reading'])
                                        ({{ $metric['last_reading']->format('d M, H:i') }})
                                    @endif
                                </span>
                            </span>
                        </div>

                        {{-- Sync message if needed --}}
                        @if(isset($metric['sync_message']) && !$metric['synced'])
                            <div class="text-xs text-warning-700">{{ $metric['sync_message'] }}</div>
                        @endif
                    </div>
                @endforeach
            </div>
        </div>
    </x-filament::card>
</x-filament::widget>
