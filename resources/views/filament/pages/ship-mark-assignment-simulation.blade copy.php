<x-filament::page>
    <form wire:submit.prevent="confirmAssignment">
        {{ $this->form }}

        @if($positionSlots)
            <div class="mt-8">
                <h3 class="text-lg font-bold mb-4">Position Assignment & Suggestions</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    @foreach($positionSlots as $slotKey => $slot)
                        <div class="p-4 bg-gray-50 rounded shadow">
                            <div class="font-semibold">{{ $slot['designation_name'] }} ({{ $slot['slot_num'] }})</div>
                            <div class="mt-2">
                                @php
                                    $suggestions = $crewSuggestions[$slotKey] ?? [];
                                    $selected = $selectedAssignments[$slotKey] ?? null;
                                @endphp

                                @if(count($suggestions) === 0)
                                    <div class="text-red-600 font-bold">
                                        No suitable crew found! Please review requirements.
                                    </div>
                                @elseif(count($suggestions) <= 2)
                                    @foreach($suggestions as $crew)
                                        <label class="flex items-center">
                                            <input type="radio"
                                                wire:model="selectedAssignments.{{ $slotKey }}"
                                                value="{{ $crew->id }}"
                                                @if(in_array($crew->id, $selectedAssignments) && $selectedAssignments[$slotKey] !== $crew->id) disabled @endif
                                            >
                                             <span class="ml-2">{{ $crew->display_name }} ({{ $crew->unique_crew_id ?? '' }})</span>
                                        </label>
                                    @endforeach
                                @else
                                    <select wire:model="selectedAssignments.{{ $slotKey }}"
                                            class="w-full mt-1 border rounded">
                                        <option value="">-- Select Crew --</option>
                                        @foreach($suggestions as $crew)
                                            <option value="{{ $crew->id }}">
                                                {{ $crew->display_name }} ({{ $crew->unique_crew_id ?? '' }})
                                            </option>
                                        @endforeach
                                    </select>
                                @endif
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        @endif

        @if($offboardCrew && count($offboardCrew) > 0)
            <div class="mt-6 p-4 bg-red-50 border-l-4 border-red-400">
                <div class="font-bold text-red-700 mb-2">Offboard Crew</div>
                @foreach($offboardCrew as $crew)
                    <div>  {{ $crew->display_name }} ({{ $crew->unique_crew_id ?? '' }}) — Cannot be assigned to any position!</div>
                @endforeach
            </div>
        @endif

        <div class="mt-6">
            <x-filament::button type="submit" color="success">
                Confirm Assignments
            </x-filament::button>
        </div>
    </form>
</x-filament::page>
