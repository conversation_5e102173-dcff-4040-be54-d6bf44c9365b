@php
   $collection = $column->getName();
    $mediaItems = method_exists($record, 'getMedia') ? $record->getMedia($collection) : collect();


    $getFileType = function($media) {
        $mime = $media->mime_type;
        if (str_starts_with($mime, 'image/')) return 'image';
        if ($mime === 'application/pdf') return 'pdf';
        if (str_starts_with($mime, 'video/')) return 'video';
        if (str_starts_with($mime, 'audio/')) return 'audio';
        return 'other';
    };

@endphp

@if($mediaItems->isNotEmpty())
    @foreach($mediaItems as $media)
        <button
            type="button"
            wire:click="$dispatch('open-modal', {
                modalId: 'media-preview-{{ $media->id }}'
            })"
            style="border:none;background:none;padding:0;cursor:pointer;"
            title="Preview"
        >
            {{-- Thumbnail preview --}}
            @php $type = getFileType($media); @endphp
            @if($type === 'image')
                <img src="{{ $media->getUrl('thumb') ?: $media->getUrl() }}" style="width:40px; height:40px; object-fit:cover; border-radius:8px;" />
            @elseif($type === 'pdf')
                <span title="PDF" style="display:inline-block;width:40px;height:40px;background:#eee; border-radius:8px; display:flex; align-items:center; justify-content:center; font-size:24px;">📄</span>
            @elseif($type === 'video')
                <span title="Video" style="display:inline-block;width:40px;height:40px;background:#eee; border-radius:8px; display:flex; align-items:center; justify-content:center; font-size:24px;">🎥</span>
            @elseif($type === 'audio')
                <span title="Audio" style="display:inline-block;width:40px;height:40px;background:#eee; border-radius:8px; display:flex; align-items:center; justify-content:center; font-size:24px;">🎵</span>
            @else
                <span title="File" style="display:inline-block;width:40px;height:40px;background:#eee; border-radius:8px; display:flex; align-items:center; justify-content:center; font-size:24px;">📁</span>
            @endif
        </button>

        <x-filament::modal id="media-preview-{{ $media->id }}">
            <div class="p-4 text-center">
                {{-- Full preview --}}
                @if($type === 'image')
                    <img src="{{ $media->getUrl() }}" alt="Preview" style="max-width:100%;max-height:60vh;object-fit:contain;" />
                @elseif($type === 'pdf')
                    <iframe src="{{ $media->getUrl() }}" style="width:80vw;height:80vh;" frameborder="0"></iframe>
                    <div class="mt-2">
                        <a href="{{ $media->getUrl() }}" download class="text-blue-600 underline">Download PDF</a>
                    </div>
                @elseif($type === 'video')
                    <video controls style="max-width:100%;max-height:60vh;">
                        <source src="{{ $media->getUrl() }}" type="{{ $media->mime_type }}">
                        Your browser does not support the video tag.
                    </video>
                @elseif($type === 'audio')
                    <audio controls style="width:100%;">
                        <source src="{{ $media->getUrl() }}" type="{{ $media->mime_type }}">
                        Your browser does not support the audio element.
                    </audio>
                @else
                    <div class="mb-2">No preview available for this file type.</div>
                    <a href="{{ $media->getUrl() }}" download class="text-blue-600 underline">Download File</a>
                @endif

                {{-- Title/Description (if used) --}}
                <div class="mt-2 text-gray-700">
                    <strong>{{ $media->getCustomProperty('title') }}</strong><br>
                    {{ $media->getCustomProperty('description') }}
                </div>
            </div>
        </x-filament::modal>
    @endforeach
@endif
