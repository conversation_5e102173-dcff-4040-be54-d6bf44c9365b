<?php return array (
  'livewireComponents' => 
  array (
    'app.filament.resources.asset-category-resource.pages.create-asset-category' => 'App\\Filament\\Resources\\AssetCategoryResource\\Pages\\CreateAssetCategory',
    'app.filament.resources.asset-category-resource.pages.edit-asset-category' => 'App\\Filament\\Resources\\AssetCategoryResource\\Pages\\EditAssetCategory',
    'app.filament.resources.asset-category-resource.pages.list-asset-categories' => 'App\\Filament\\Resources\\AssetCategoryResource\\Pages\\ListAssetCategories',
    'app.filament.resources.asset-checklist-master-resource.pages.create-asset-checklist-master' => 'App\\Filament\\Resources\\AssetChecklistMasterResource\\Pages\\CreateAssetChecklistMaster',
    'app.filament.resources.asset-checklist-master-resource.pages.edit-asset-checklist-master' => 'App\\Filament\\Resources\\AssetChecklistMasterResource\\Pages\\EditAssetChecklistMaster',
    'app.filament.resources.asset-checklist-master-resource.pages.list-asset-checklist-masters' => 'App\\Filament\\Resources\\AssetChecklistMasterResource\\Pages\\ListAssetChecklistMasters',
    'app.filament.resources.asset-document-master-resource.pages.create-asset-document-master' => 'App\\Filament\\Resources\\AssetDocumentMasterResource\\Pages\\CreateAssetDocumentMaster',
    'app.filament.resources.asset-document-master-resource.pages.edit-asset-document-master' => 'App\\Filament\\Resources\\AssetDocumentMasterResource\\Pages\\EditAssetDocumentMaster',
    'app.filament.resources.asset-document-master-resource.pages.list-asset-document-masters' => 'App\\Filament\\Resources\\AssetDocumentMasterResource\\Pages\\ListAssetDocumentMasters',
    'app.filament.resources.asset-lifecycle-log-resource.pages.create-asset-lifecycle-log' => 'App\\Filament\\Resources\\AssetLifecycleLogResource\\Pages\\CreateAssetLifecycleLog',
    'app.filament.resources.asset-lifecycle-log-resource.pages.edit-asset-lifecycle-log' => 'App\\Filament\\Resources\\AssetLifecycleLogResource\\Pages\\EditAssetLifecycleLog',
    'app.filament.resources.asset-lifecycle-log-resource.pages.list-asset-lifecycle-logs' => 'App\\Filament\\Resources\\AssetLifecycleLogResource\\Pages\\ListAssetLifecycleLogs',
    'app.filament.resources.asset-log-metric-resource.pages.create-asset-log-metric' => 'App\\Filament\\Resources\\AssetLogMetricResource\\Pages\\CreateAssetLogMetric',
    'app.filament.resources.asset-log-metric-resource.pages.daily-log-entry' => 'App\\Filament\\Resources\\AssetLogMetricResource\\Pages\\DailyLogEntry',
    'app.filament.resources.asset-log-metric-resource.pages.edit-asset-log-metric' => 'App\\Filament\\Resources\\AssetLogMetricResource\\Pages\\EditAssetLogMetric',
    'app.filament.resources.asset-log-metric-resource.pages.list-asset-log-metrics' => 'App\\Filament\\Resources\\AssetLogMetricResource\\Pages\\ListAssetLogMetrics',
    'app.filament.resources.asset-movement-resource.pages.create-asset-movement' => 'App\\Filament\\Resources\\AssetMovementResource\\Pages\\CreateAssetMovement',
    'app.filament.resources.asset-movement-resource.pages.edit-asset-movement' => 'App\\Filament\\Resources\\AssetMovementResource\\Pages\\EditAssetMovement',
    'app.filament.resources.asset-movement-resource.pages.list-asset-movements' => 'App\\Filament\\Resources\\AssetMovementResource\\Pages\\ListAssetMovements',
    'app.filament.resources.asset-resource.pages.create-asset' => 'App\\Filament\\Resources\\AssetResource\\Pages\\CreateAsset',
    'app.filament.resources.asset-resource.pages.edit-asset' => 'App\\Filament\\Resources\\AssetResource\\Pages\\EditAsset',
    'app.filament.resources.asset-resource.pages.list-assets' => 'App\\Filament\\Resources\\AssetResource\\Pages\\ListAssets',
    'app.filament.resources.asset-spec-key-resource.pages.create-asset-spec-key' => 'App\\Filament\\Resources\\AssetSpecKeyResource\\Pages\\CreateAssetSpecKey',
    'app.filament.resources.asset-spec-key-resource.pages.edit-asset-spec-key' => 'App\\Filament\\Resources\\AssetSpecKeyResource\\Pages\\EditAssetSpecKey',
    'app.filament.resources.asset-spec-key-resource.pages.list-asset-spec-keys' => 'App\\Filament\\Resources\\AssetSpecKeyResource\\Pages\\ListAssetSpecKeys',
    'app.filament.resources.asset-transaction-resource.pages.create-asset-transaction' => 'App\\Filament\\Resources\\AssetTransactionResource\\Pages\\CreateAssetTransaction',
    'app.filament.resources.asset-transaction-resource.pages.edit-asset-transaction' => 'App\\Filament\\Resources\\AssetTransactionResource\\Pages\\EditAssetTransaction',
    'app.filament.resources.asset-transaction-resource.pages.list-asset-transactions' => 'App\\Filament\\Resources\\AssetTransactionResource\\Pages\\ListAssetTransactions',
    'app.filament.resources.authority-level-resource.pages.create-authority-level' => 'App\\Filament\\Resources\\AuthorityLevelResource\\Pages\\CreateAuthorityLevel',
    'app.filament.resources.authority-level-resource.pages.edit-authority-level' => 'App\\Filament\\Resources\\AuthorityLevelResource\\Pages\\EditAuthorityLevel',
    'app.filament.resources.authority-level-resource.pages.list-authority-levels' => 'App\\Filament\\Resources\\AuthorityLevelResource\\Pages\\ListAuthorityLevels',
    'app.filament.resources.book-resource.pages.create-book' => 'App\\Filament\\Resources\\BookResource\\Pages\\CreateBook',
    'app.filament.resources.book-resource.pages.edit-book' => 'App\\Filament\\Resources\\BookResource\\Pages\\EditBook',
    'app.filament.resources.book-resource.pages.list-books' => 'App\\Filament\\Resources\\BookResource\\Pages\\ListBooks',
    'app.filament.resources.book-resource.pages.view-book' => 'App\\Filament\\Resources\\BookResource\\Pages\\ViewBook',
    'app.filament.resources.certificate-master-resource.pages.create-certificate-master' => 'App\\Filament\\Resources\\CertificateMasterResource\\Pages\\CreateCertificateMaster',
    'app.filament.resources.certificate-master-resource.pages.edit-certificate-master' => 'App\\Filament\\Resources\\CertificateMasterResource\\Pages\\EditCertificateMaster',
    'app.filament.resources.certificate-master-resource.pages.list-certificate-masters' => 'App\\Filament\\Resources\\CertificateMasterResource\\Pages\\ListCertificateMasters',
    'app.filament.resources.certificate-resource.pages.create-certificate' => 'App\\Filament\\Resources\\CertificateResource\\Pages\\CreateCertificate',
    'app.filament.resources.certificate-resource.pages.edit-certificate' => 'App\\Filament\\Resources\\CertificateResource\\Pages\\EditCertificate',
    'app.filament.resources.certificate-resource.pages.list-certificates' => 'App\\Filament\\Resources\\CertificateResource\\Pages\\ListCertificates',
    'app.filament.resources.classification-society-resource.pages.create-classification-society' => 'App\\Filament\\Resources\\ClassificationSocietyResource\\Pages\\CreateClassificationSociety',
    'app.filament.resources.classification-society-resource.pages.edit-classification-society' => 'App\\Filament\\Resources\\ClassificationSocietyResource\\Pages\\EditClassificationSociety',
    'app.filament.resources.classification-society-resource.pages.list-classification-societies' => 'App\\Filament\\Resources\\ClassificationSocietyResource\\Pages\\ListClassificationSocieties',
    'app.filament.resources.company-resource.pages.create-company' => 'App\\Filament\\Resources\\CompanyResource\\Pages\\CreateCompany',
    'app.filament.resources.company-resource.pages.edit-company' => 'App\\Filament\\Resources\\CompanyResource\\Pages\\EditCompany',
    'app.filament.resources.company-resource.pages.list-companies' => 'App\\Filament\\Resources\\CompanyResource\\Pages\\ListCompanies',
    'app.filament.resources.country-resource.pages.create-country' => 'App\\Filament\\Resources\\CountryResource\\Pages\\CreateCountry',
    'app.filament.resources.country-resource.pages.edit-country' => 'App\\Filament\\Resources\\CountryResource\\Pages\\EditCountry',
    'app.filament.resources.country-resource.pages.list-countries' => 'App\\Filament\\Resources\\CountryResource\\Pages\\ListCountries',
    'app.filament.resources.crew-assignment-resource.pages.create-crew-assignment' => 'App\\Filament\\Resources\\CrewAssignmentResource\\Pages\\CreateCrewAssignment',
    'app.filament.resources.crew-assignment-resource.pages.edit-crew-assignment' => 'App\\Filament\\Resources\\CrewAssignmentResource\\Pages\\EditCrewAssignment',
    'app.filament.resources.crew-assignment-resource.pages.list-crew-assignments' => 'App\\Filament\\Resources\\CrewAssignmentResource\\Pages\\ListCrewAssignments',
    'app.filament.resources.crew-certificate-requirement-resource.pages.create-crew-certificate-requirement' => 'App\\Filament\\Resources\\CrewCertificateRequirementResource\\Pages\\CreateCrewCertificateRequirement',
    'app.filament.resources.crew-certificate-requirement-resource.pages.edit-crew-certificate-requirement' => 'App\\Filament\\Resources\\CrewCertificateRequirementResource\\Pages\\EditCrewCertificateRequirement',
    'app.filament.resources.crew-certificate-requirement-resource.pages.list-crew-certificate-requirements' => 'App\\Filament\\Resources\\CrewCertificateRequirementResource\\Pages\\ListCrewCertificateRequirements',
    'app.filament.resources.crew-certificate-requirement-set-resource.pages.create-crew-certificate-requirement-set' => 'App\\Filament\\Resources\\CrewCertificateRequirementSetResource\\Pages\\CreateCrewCertificateRequirementSet',
    'app.filament.resources.crew-certificate-requirement-set-resource.pages.edit-crew-certificate-requirement-set' => 'App\\Filament\\Resources\\CrewCertificateRequirementSetResource\\Pages\\EditCrewCertificateRequirementSet',
    'app.filament.resources.crew-certificate-requirement-set-resource.pages.list-crew-certificate-requirement-sets' => 'App\\Filament\\Resources\\CrewCertificateRequirementSetResource\\Pages\\ListCrewCertificateRequirementSets',
    'app.filament.resources.crew-certificate-resource.pages.create-crew-certificate' => 'App\\Filament\\Resources\\CrewCertificateResource\\Pages\\CreateCrewCertificate',
    'app.filament.resources.crew-certificate-resource.pages.edit-crew-certificate' => 'App\\Filament\\Resources\\CrewCertificateResource\\Pages\\EditCrewCertificate',
    'app.filament.resources.crew-certificate-resource.pages.list-crew-certificates' => 'App\\Filament\\Resources\\CrewCertificateResource\\Pages\\ListCrewCertificates',
    'app.filament.resources.crew-certificate-type-resource.pages.create-crew-certificate-type' => 'App\\Filament\\Resources\\CrewCertificateTypeResource\\Pages\\CreateCrewCertificateType',
    'app.filament.resources.crew-certificate-type-resource.pages.edit-crew-certificate-type' => 'App\\Filament\\Resources\\CrewCertificateTypeResource\\Pages\\EditCrewCertificateType',
    'app.filament.resources.crew-certificate-type-resource.pages.list-crew-certificate-types' => 'App\\Filament\\Resources\\CrewCertificateTypeResource\\Pages\\ListCrewCertificateTypes',
    'app.filament.resources.crew-checklist-master-resource.pages.create-crew-checklist-master' => 'App\\Filament\\Resources\\CrewChecklistMasterResource\\Pages\\CreateCrewChecklistMaster',
    'app.filament.resources.crew-checklist-master-resource.pages.edit-crew-checklist-master' => 'App\\Filament\\Resources\\CrewChecklistMasterResource\\Pages\\EditCrewChecklistMaster',
    'app.filament.resources.crew-checklist-master-resource.pages.list-crew-checklist-masters' => 'App\\Filament\\Resources\\CrewChecklistMasterResource\\Pages\\ListCrewChecklistMasters',
    'app.filament.resources.crew-document-master-resource.pages.create-crew-document-master' => 'App\\Filament\\Resources\\CrewDocumentMasterResource\\Pages\\CreateCrewDocumentMaster',
    'app.filament.resources.crew-document-master-resource.pages.edit-crew-document-master' => 'App\\Filament\\Resources\\CrewDocumentMasterResource\\Pages\\EditCrewDocumentMaster',
    'app.filament.resources.crew-document-master-resource.pages.list-crew-document-masters' => 'App\\Filament\\Resources\\CrewDocumentMasterResource\\Pages\\ListCrewDocumentMasters',
    'app.filament.resources.crew-resource.pages.create-crew' => 'App\\Filament\\Resources\\CrewResource\\Pages\\CreateCrew',
    'app.filament.resources.crew-resource.pages.edit-crew' => 'App\\Filament\\Resources\\CrewResource\\Pages\\EditCrew',
    'app.filament.resources.crew-resource.pages.list-crews' => 'App\\Filament\\Resources\\CrewResource\\Pages\\ListCrews',
    'app.filament.resources.crew-resource.relation-managers.assignments-relation-manager' => 'App\\Filament\\Resources\\CrewResource\\RelationManagers\\AssignmentsRelationManager',
    'app.filament.resources.daily-asset-log-resource.pages.create-daily-asset-log' => 'App\\Filament\\Resources\\DailyAssetLogResource\\Pages\\CreateDailyAssetLog',
    'app.filament.resources.daily-asset-log-resource.pages.edit-daily-asset-log' => 'App\\Filament\\Resources\\DailyAssetLogResource\\Pages\\EditDailyAssetLog',
    'app.filament.resources.daily-asset-log-resource.pages.list-daily-asset-logs' => 'App\\Filament\\Resources\\DailyAssetLogResource\\Pages\\ListDailyAssetLogs',
    'app.filament.resources.daily-asset-log-resource.relation-managers.asset-log-metrics-relation-manager' => 'App\\Filament\\Resources\\DailyAssetLogResource\\RelationManagers\\AssetLogMetricsRelationManager',
    'app.filament.resources.designation-resource.pages.create-designation' => 'App\\Filament\\Resources\\DesignationResource\\Pages\\CreateDesignation',
    'app.filament.resources.designation-resource.pages.edit-designation' => 'App\\Filament\\Resources\\DesignationResource\\Pages\\EditDesignation',
    'app.filament.resources.designation-resource.pages.list-designations' => 'App\\Filament\\Resources\\DesignationResource\\Pages\\ListDesignations',
    'app.filament.resources.location-resource.pages.create-location' => 'App\\Filament\\Resources\\LocationResource\\Pages\\CreateLocation',
    'app.filament.resources.location-resource.pages.edit-location' => 'App\\Filament\\Resources\\LocationResource\\Pages\\EditLocation',
    'app.filament.resources.location-resource.pages.list-locations' => 'App\\Filament\\Resources\\LocationResource\\Pages\\ListLocations',
    'app.filament.resources.location-type-resource.pages.create-location-type' => 'App\\Filament\\Resources\\LocationTypeResource\\Pages\\CreateLocationType',
    'app.filament.resources.location-type-resource.pages.edit-location-type' => 'App\\Filament\\Resources\\LocationTypeResource\\Pages\\EditLocationType',
    'app.filament.resources.location-type-resource.pages.list-location-types' => 'App\\Filament\\Resources\\LocationTypeResource\\Pages\\ListLocationTypes',
    'app.filament.resources.position-requirement-set-assignment-resource.pages.create-position-requirement-set-assignment' => 'App\\Filament\\Resources\\PositionRequirementSetAssignmentResource\\Pages\\CreatePositionRequirementSetAssignment',
    'app.filament.resources.position-requirement-set-assignment-resource.pages.edit-position-requirement-set-assignment' => 'App\\Filament\\Resources\\PositionRequirementSetAssignmentResource\\Pages\\EditPositionRequirementSetAssignment',
    'app.filament.resources.position-requirement-set-assignment-resource.pages.list-position-requirement-set-assignments' => 'App\\Filament\\Resources\\PositionRequirementSetAssignmentResource\\Pages\\ListPositionRequirementSetAssignments',
    'app.filament.resources.position-requirement-set-group-assignment-resource.pages.create-position-requirement-set-group-assignment' => 'App\\Filament\\Resources\\PositionRequirementSetGroupAssignmentResource\\Pages\\CreatePositionRequirementSetGroupAssignment',
    'app.filament.resources.position-requirement-set-group-assignment-resource.pages.edit-position-requirement-set-group-assignment' => 'App\\Filament\\Resources\\PositionRequirementSetGroupAssignmentResource\\Pages\\EditPositionRequirementSetGroupAssignment',
    'app.filament.resources.position-requirement-set-group-assignment-resource.pages.list-position-requirement-set-group-assignments' => 'App\\Filament\\Resources\\PositionRequirementSetGroupAssignmentResource\\Pages\\ListPositionRequirementSetGroupAssignments',
    'app.filament.resources.requirement-set-group-resource.pages.create-requirement-set-group' => 'App\\Filament\\Resources\\RequirementSetGroupResource\\Pages\\CreateRequirementSetGroup',
    'app.filament.resources.requirement-set-group-resource.pages.edit-requirement-set-group' => 'App\\Filament\\Resources\\RequirementSetGroupResource\\Pages\\EditRequirementSetGroup',
    'app.filament.resources.requirement-set-group-resource.pages.list-requirement-set-groups' => 'App\\Filament\\Resources\\RequirementSetGroupResource\\Pages\\ListRequirementSetGroups',
    'app.filament.resources.role-resource.pages.create-role' => 'App\\Filament\\Resources\\RoleResource\\Pages\\CreateRole',
    'app.filament.resources.role-resource.pages.edit-role' => 'App\\Filament\\Resources\\RoleResource\\Pages\\EditRole',
    'app.filament.resources.role-resource.pages.list-roles' => 'App\\Filament\\Resources\\RoleResource\\Pages\\ListRoles',
    'app.filament.resources.role-resource.pages.view-role' => 'App\\Filament\\Resources\\RoleResource\\Pages\\ViewRole',
    'app.filament.resources.ship-asset-checklist-item-metric-resource.pages.create-ship-asset-checklist-item-metric' => 'App\\Filament\\Resources\\ShipAssetChecklistItemMetricResource\\Pages\\CreateShipAssetChecklistItemMetric',
    'app.filament.resources.ship-asset-checklist-item-metric-resource.pages.edit-ship-asset-checklist-item-metric' => 'App\\Filament\\Resources\\ShipAssetChecklistItemMetricResource\\Pages\\EditShipAssetChecklistItemMetric',
    'app.filament.resources.ship-asset-checklist-item-metric-resource.pages.list-ship-asset-checklist-item-metrics' => 'App\\Filament\\Resources\\ShipAssetChecklistItemMetricResource\\Pages\\ListShipAssetChecklistItemMetrics',
    'app.filament.resources.ship-asset-checklist-item-resource.pages.create-ship-asset-checklist-item' => 'App\\Filament\\Resources\\ShipAssetChecklistItemResource\\Pages\\CreateShipAssetChecklistItem',
    'app.filament.resources.ship-asset-checklist-item-resource.pages.edit-ship-asset-checklist-item' => 'App\\Filament\\Resources\\ShipAssetChecklistItemResource\\Pages\\EditShipAssetChecklistItem',
    'app.filament.resources.ship-asset-checklist-item-resource.pages.list-ship-asset-checklist-items' => 'App\\Filament\\Resources\\ShipAssetChecklistItemResource\\Pages\\ListShipAssetChecklistItems',
    'app.filament.resources.ship-asset-checklist-item-resource.relation-managers.checklist-item-metrics-relation-manager' => 'App\\Filament\\Resources\\ShipAssetChecklistItemResource\\RelationManagers\\ChecklistItemMetricsRelationManager',
    'app.filament.resources.ship-asset-metric-resource.pages.create-ship-asset-metric' => 'App\\Filament\\Resources\\ShipAssetMetricResource\\Pages\\CreateShipAssetMetric',
    'app.filament.resources.ship-asset-metric-resource.pages.edit-ship-asset-metric' => 'App\\Filament\\Resources\\ShipAssetMetricResource\\Pages\\EditShipAssetMetric',
    'app.filament.resources.ship-asset-metric-resource.pages.list-ship-asset-metrics' => 'App\\Filament\\Resources\\ShipAssetMetricResource\\Pages\\ListShipAssetMetrics',
    'app.filament.resources.ship-asset-resource.pages.create-ship-asset' => 'App\\Filament\\Resources\\ShipAssetResource\\Pages\\CreateShipAsset',
    'app.filament.resources.ship-asset-resource.pages.edit-ship-asset' => 'App\\Filament\\Resources\\ShipAssetResource\\Pages\\EditShipAsset',
    'app.filament.resources.ship-asset-resource.pages.list-ship-assets' => 'App\\Filament\\Resources\\ShipAssetResource\\Pages\\ListShipAssets',
    'app.filament.resources.ship-asset-resource.relation-managers.checklist-items-relation-manager' => 'App\\Filament\\Resources\\ShipAssetResource\\RelationManagers\\ChecklistItemsRelationManager',
    'app.filament.resources.ship-asset-resource.relation-managers.metrics-relation-manager' => 'App\\Filament\\Resources\\ShipAssetResource\\RelationManagers\\MetricsRelationManager',
    'app.filament.resources.ship-class-resource.pages.create-ship-class' => 'App\\Filament\\Resources\\ShipClassResource\\Pages\\CreateShipClass',
    'app.filament.resources.ship-class-resource.pages.edit-ship-class' => 'App\\Filament\\Resources\\ShipClassResource\\Pages\\EditShipClass',
    'app.filament.resources.ship-class-resource.pages.list-ship-classes' => 'App\\Filament\\Resources\\ShipClassResource\\Pages\\ListShipClasses',
    'app.filament.resources.ship-crew-position-requirement-resource.pages.create-ship-crew-position-requirement' => 'App\\Filament\\Resources\\ShipCrewPositionRequirementResource\\Pages\\CreateShipCrewPositionRequirement',
    'app.filament.resources.ship-crew-position-requirement-resource.pages.edit-ship-crew-position-requirement' => 'App\\Filament\\Resources\\ShipCrewPositionRequirementResource\\Pages\\EditShipCrewPositionRequirement',
    'app.filament.resources.ship-crew-position-requirement-resource.pages.list-ship-crew-position-requirements' => 'App\\Filament\\Resources\\ShipCrewPositionRequirementResource\\Pages\\ListShipCrewPositionRequirements',
    'app.filament.resources.ship-document-master-resource.pages.create-ship-document-master' => 'App\\Filament\\Resources\\ShipDocumentMasterResource\\Pages\\CreateShipDocumentMaster',
    'app.filament.resources.ship-document-master-resource.pages.edit-ship-document-master' => 'App\\Filament\\Resources\\ShipDocumentMasterResource\\Pages\\EditShipDocumentMaster',
    'app.filament.resources.ship-document-master-resource.pages.list-ship-document-masters' => 'App\\Filament\\Resources\\ShipDocumentMasterResource\\Pages\\ListShipDocumentMasters',
    'app.filament.resources.ship-document-resource.pages.create-ship-document' => 'App\\Filament\\Resources\\ShipDocumentResource\\Pages\\CreateShipDocument',
    'app.filament.resources.ship-document-resource.pages.edit-ship-document' => 'App\\Filament\\Resources\\ShipDocumentResource\\Pages\\EditShipDocument',
    'app.filament.resources.ship-document-resource.pages.list-ship-documents' => 'App\\Filament\\Resources\\ShipDocumentResource\\Pages\\ListShipDocuments',
    'app.filament.resources.ship-mark-resource.pages.create-ship-mark' => 'App\\Filament\\Resources\\ShipMarkResource\\Pages\\CreateShipMark',
    'app.filament.resources.ship-mark-resource.pages.edit-ship-mark' => 'App\\Filament\\Resources\\ShipMarkResource\\Pages\\EditShipMark',
    'app.filament.resources.ship-mark-resource.pages.list-ship-marks' => 'App\\Filament\\Resources\\ShipMarkResource\\Pages\\ListShipMarks',
    'app.filament.resources.ship-resource.pages.create-ship' => 'App\\Filament\\Resources\\ShipResource\\Pages\\CreateShip',
    'app.filament.resources.ship-resource.pages.edit-ship' => 'App\\Filament\\Resources\\ShipResource\\Pages\\EditShip',
    'app.filament.resources.ship-resource.pages.list-ships' => 'App\\Filament\\Resources\\ShipResource\\Pages\\ListShips',
    'app.filament.resources.ship-resource.relation-managers.crew-positions-relation-manager' => 'App\\Filament\\Resources\\ShipResource\\RelationManagers\\CrewPositionsRelationManager',
    'app.filament.resources.ship-type-resource.pages.create-ship-type' => 'App\\Filament\\Resources\\ShipTypeResource\\Pages\\CreateShipType',
    'app.filament.resources.ship-type-resource.pages.edit-ship-type' => 'App\\Filament\\Resources\\ShipTypeResource\\Pages\\EditShipType',
    'app.filament.resources.ship-type-resource.pages.list-ship-types' => 'App\\Filament\\Resources\\ShipTypeResource\\Pages\\ListShipTypes',
    'app.filament.resources.specs-resource.relation-managers.specs-relation-manager' => 'App\\Filament\\Resources\\SpecsResource\\RelationManagers\\SpecsRelationManager',
    'app.filament.resources.tag-resource.pages.create-tag' => 'App\\Filament\\Resources\\TagResource\\Pages\\CreateTag',
    'app.filament.resources.tag-resource.pages.edit-tag' => 'App\\Filament\\Resources\\TagResource\\Pages\\EditTag',
    'app.filament.resources.tag-resource.pages.list-tags' => 'App\\Filament\\Resources\\TagResource\\Pages\\ListTags',
    'app.filament.resources.user-resource.pages.create-user' => 'App\\Filament\\Resources\\UserResource\\Pages\\CreateUser',
    'app.filament.resources.user-resource.pages.edit-user' => 'App\\Filament\\Resources\\UserResource\\Pages\\EditUser',
    'app.filament.resources.user-resource.pages.list-users' => 'App\\Filament\\Resources\\UserResource\\Pages\\ListUsers',
    'app.filament.resources.user-resource.pages.view-user' => 'App\\Filament\\Resources\\UserResource\\Pages\\ViewUser',
    'app.filament.pages.assign-authority-permissions' => 'App\\Filament\\Pages\\AssignAuthorityPermissions',
    'app.filament.pages.login' => 'App\\Filament\\Pages\\Login',
    'app.filament.pages.manage-setting' => 'App\\Filament\\Pages\\ManageSetting',
    'app.filament.pages.position-requirement-editor' => 'App\\Filament\\Pages\\PositionRequirementEditor',
    'app.filament.pages.rob-dashboard' => 'App\\Filament\\Pages\\RobDashboard',
    'app.filament.pages.rob-dashboard.widgets.last-rob-entry-widget' => 'App\\Filament\\Pages\\RobDashboard\\Widgets\\LastRobEntryWidget',
    'app.filament.pages.rob-dashboard.widgets.rob-asset-metric-widget' => 'App\\Filament\\Pages\\RobDashboard\\Widgets\\RobAssetMetricWidget',
    'app.filament.pages.rob-entry' => 'App\\Filament\\Pages\\RobEntry',
    'app.filament.pages.ship-mark-assignment-simulation' => 'App\\Filament\\Pages\\ShipMarkAssignmentSimulation',
    'filament.pages.dashboard' => 'Filament\\Pages\\Dashboard',
    'filament.widgets.account-widget' => 'Filament\\Widgets\\AccountWidget',
    'hasnayeen.themes.filament.pages.themes' => 'Hasnayeen\\Themes\\Filament\\Pages\\Themes',
    'jeffgreco13.filament-breezy.pages.my-profile-page' => 'Jeffgreco13\\FilamentBreezy\\Pages\\MyProfilePage',
    'amendozaaguiar.filament-route-statistics.pages.route-statistics' => 'Amendozaaguiar\\FilamentRouteStatistics\\Pages\\RouteStatistics',
    'filament.livewire.database-notifications' => 'Filament\\Livewire\\DatabaseNotifications',
    'filament.pages.auth.edit-profile' => 'Filament\\Pages\\Auth\\EditProfile',
    'filament.livewire.global-search' => 'Filament\\Livewire\\GlobalSearch',
    'filament.livewire.notifications' => 'Filament\\Livewire\\Notifications',
    'filament.pages.auth.email-verification.email-verification-prompt' => 'Filament\\Pages\\Auth\\EmailVerification\\EmailVerificationPrompt',
    'filament.pages.auth.password-reset.request-password-reset' => 'Filament\\Pages\\Auth\\PasswordReset\\RequestPasswordReset',
    'filament.pages.auth.password-reset.reset-password' => 'Filament\\Pages\\Auth\\PasswordReset\\ResetPassword',
    'filament.pages.auth.register' => 'Filament\\Pages\\Auth\\Register',
    'app.filament.relation-managers.certificates-relation-manager' => 'App\\Filament\\RelationManagers\\CertificatesRelationManager',
    'rupadana.api-service.resources.token-resource.pages.list-tokens' => 'Rupadana\\ApiService\\Resources\\TokenResource\\Pages\\ListTokens',
    'rupadana.api-service.resources.token-resource.pages.create-token' => 'Rupadana\\ApiService\\Resources\\TokenResource\\Pages\\CreateToken',
    'tomato-p-h-p.filament-media-manager.resources.folder-resource.pages.list-folders' => 'TomatoPHP\\FilamentMediaManager\\Resources\\FolderResource\\Pages\\ListFolders',
    'tomato-p-h-p.filament-media-manager.resources.media-resource.pages.list-media' => 'TomatoPHP\\FilamentMediaManager\\Resources\\MediaResource\\Pages\\ListMedia',
  ),
  'clusters' => 
  array (
  ),
  'clusteredComponents' => 
  array (
  ),
  'clusterDirectories' => 
  array (
  ),
  'clusterNamespaces' => 
  array (
  ),
  'pages' => 
  array (
    '/var/www/html/laravelproject/marine360/app/Filament/Pages/AssignAuthorityPermissions.php' => 'App\\Filament\\Pages\\AssignAuthorityPermissions',
    '/var/www/html/laravelproject/marine360/app/Filament/Pages/ManageSetting.php' => 'App\\Filament\\Pages\\ManageSetting',
    '/var/www/html/laravelproject/marine360/app/Filament/Pages/PositionRequirementEditor.php' => 'App\\Filament\\Pages\\PositionRequirementEditor',
    '/var/www/html/laravelproject/marine360/app/Filament/Pages/RobDashboard.php' => 'App\\Filament\\Pages\\RobDashboard',
    '/var/www/html/laravelproject/marine360/app/Filament/Pages/RobEntry.php' => 'App\\Filament\\Pages\\RobEntry',
    '/var/www/html/laravelproject/marine360/app/Filament/Pages/ShipMarkAssignmentSimulation.php' => 'App\\Filament\\Pages\\ShipMarkAssignmentSimulation',
    0 => 'Filament\\Pages\\Dashboard',
    1 => 'App\\Filament\\Resources\\AssetLogMetricResource\\Pages\\DailyLogEntry',
    2 => 'Hasnayeen\\Themes\\Filament\\Pages\\Themes',
    3 => 'Jeffgreco13\\FilamentBreezy\\Pages\\MyProfilePage',
    4 => 'Amendozaaguiar\\FilamentRouteStatistics\\Pages\\RouteStatistics',
  ),
  'pageDirectories' => 
  array (
    0 => '/var/www/html/laravelproject/marine360/app/Filament/Pages',
  ),
  'pageNamespaces' => 
  array (
    0 => 'App\\Filament\\Pages',
  ),
  'resources' => 
  array (
    '/var/www/html/laravelproject/marine360/app/Filament/Resources/AssetCategoryResource.php' => 'App\\Filament\\Resources\\AssetCategoryResource',
    '/var/www/html/laravelproject/marine360/app/Filament/Resources/AssetChecklistMasterResource.php' => 'App\\Filament\\Resources\\AssetChecklistMasterResource',
    '/var/www/html/laravelproject/marine360/app/Filament/Resources/AssetDocumentMasterResource.php' => 'App\\Filament\\Resources\\AssetDocumentMasterResource',
    '/var/www/html/laravelproject/marine360/app/Filament/Resources/AssetLifecycleLogResource.php' => 'App\\Filament\\Resources\\AssetLifecycleLogResource',
    '/var/www/html/laravelproject/marine360/app/Filament/Resources/AssetLogMetricResource.php' => 'App\\Filament\\Resources\\AssetLogMetricResource',
    '/var/www/html/laravelproject/marine360/app/Filament/Resources/AssetMovementResource.php' => 'App\\Filament\\Resources\\AssetMovementResource',
    '/var/www/html/laravelproject/marine360/app/Filament/Resources/AssetResource.php' => 'App\\Filament\\Resources\\AssetResource',
    '/var/www/html/laravelproject/marine360/app/Filament/Resources/AssetSpecKeyResource.php' => 'App\\Filament\\Resources\\AssetSpecKeyResource',
    '/var/www/html/laravelproject/marine360/app/Filament/Resources/AssetTransactionResource.php' => 'App\\Filament\\Resources\\AssetTransactionResource',
    '/var/www/html/laravelproject/marine360/app/Filament/Resources/AuthorityLevelResource.php' => 'App\\Filament\\Resources\\AuthorityLevelResource',
    '/var/www/html/laravelproject/marine360/app/Filament/Resources/BookResource.php' => 'App\\Filament\\Resources\\BookResource',
    '/var/www/html/laravelproject/marine360/app/Filament/Resources/CertificateMasterResource.php' => 'App\\Filament\\Resources\\CertificateMasterResource',
    '/var/www/html/laravelproject/marine360/app/Filament/Resources/CertificateResource.php' => 'App\\Filament\\Resources\\CertificateResource',
    '/var/www/html/laravelproject/marine360/app/Filament/Resources/ClassificationSocietyResource.php' => 'App\\Filament\\Resources\\ClassificationSocietyResource',
    '/var/www/html/laravelproject/marine360/app/Filament/Resources/CompanyResource.php' => 'App\\Filament\\Resources\\CompanyResource',
    '/var/www/html/laravelproject/marine360/app/Filament/Resources/CountryResource.php' => 'App\\Filament\\Resources\\CountryResource',
    '/var/www/html/laravelproject/marine360/app/Filament/Resources/CrewAssignmentResource.php' => 'App\\Filament\\Resources\\CrewAssignmentResource',
    '/var/www/html/laravelproject/marine360/app/Filament/Resources/CrewCertificateRequirementResource.php' => 'App\\Filament\\Resources\\CrewCertificateRequirementResource',
    '/var/www/html/laravelproject/marine360/app/Filament/Resources/CrewCertificateRequirementSetResource.php' => 'App\\Filament\\Resources\\CrewCertificateRequirementSetResource',
    '/var/www/html/laravelproject/marine360/app/Filament/Resources/CrewCertificateResource.php' => 'App\\Filament\\Resources\\CrewCertificateResource',
    '/var/www/html/laravelproject/marine360/app/Filament/Resources/CrewCertificateTypeResource.php' => 'App\\Filament\\Resources\\CrewCertificateTypeResource',
    '/var/www/html/laravelproject/marine360/app/Filament/Resources/CrewChecklistMasterResource.php' => 'App\\Filament\\Resources\\CrewChecklistMasterResource',
    '/var/www/html/laravelproject/marine360/app/Filament/Resources/CrewDocumentMasterResource.php' => 'App\\Filament\\Resources\\CrewDocumentMasterResource',
    '/var/www/html/laravelproject/marine360/app/Filament/Resources/CrewResource.php' => 'App\\Filament\\Resources\\CrewResource',
    '/var/www/html/laravelproject/marine360/app/Filament/Resources/DailyAssetLogResource.php' => 'App\\Filament\\Resources\\DailyAssetLogResource',
    '/var/www/html/laravelproject/marine360/app/Filament/Resources/DesignationResource.php' => 'App\\Filament\\Resources\\DesignationResource',
    '/var/www/html/laravelproject/marine360/app/Filament/Resources/LocationResource.php' => 'App\\Filament\\Resources\\LocationResource',
    '/var/www/html/laravelproject/marine360/app/Filament/Resources/LocationTypeResource.php' => 'App\\Filament\\Resources\\LocationTypeResource',
    '/var/www/html/laravelproject/marine360/app/Filament/Resources/PositionRequirementSetAssignmentResource.php' => 'App\\Filament\\Resources\\PositionRequirementSetAssignmentResource',
    '/var/www/html/laravelproject/marine360/app/Filament/Resources/PositionRequirementSetGroupAssignmentResource.php' => 'App\\Filament\\Resources\\PositionRequirementSetGroupAssignmentResource',
    '/var/www/html/laravelproject/marine360/app/Filament/Resources/RequirementSetGroupResource.php' => 'App\\Filament\\Resources\\RequirementSetGroupResource',
    '/var/www/html/laravelproject/marine360/app/Filament/Resources/RoleResource.php' => 'App\\Filament\\Resources\\RoleResource',
    '/var/www/html/laravelproject/marine360/app/Filament/Resources/ShipAssetChecklistItemMetricResource.php' => 'App\\Filament\\Resources\\ShipAssetChecklistItemMetricResource',
    '/var/www/html/laravelproject/marine360/app/Filament/Resources/ShipAssetChecklistItemResource.php' => 'App\\Filament\\Resources\\ShipAssetChecklistItemResource',
    '/var/www/html/laravelproject/marine360/app/Filament/Resources/ShipAssetMetricResource.php' => 'App\\Filament\\Resources\\ShipAssetMetricResource',
    '/var/www/html/laravelproject/marine360/app/Filament/Resources/ShipAssetResource.php' => 'App\\Filament\\Resources\\ShipAssetResource',
    '/var/www/html/laravelproject/marine360/app/Filament/Resources/ShipClassResource.php' => 'App\\Filament\\Resources\\ShipClassResource',
    '/var/www/html/laravelproject/marine360/app/Filament/Resources/ShipCrewPositionRequirementResource.php' => 'App\\Filament\\Resources\\ShipCrewPositionRequirementResource',
    '/var/www/html/laravelproject/marine360/app/Filament/Resources/ShipDocumentMasterResource.php' => 'App\\Filament\\Resources\\ShipDocumentMasterResource',
    '/var/www/html/laravelproject/marine360/app/Filament/Resources/ShipDocumentResource.php' => 'App\\Filament\\Resources\\ShipDocumentResource',
    '/var/www/html/laravelproject/marine360/app/Filament/Resources/ShipMarkResource.php' => 'App\\Filament\\Resources\\ShipMarkResource',
    '/var/www/html/laravelproject/marine360/app/Filament/Resources/ShipResource.php' => 'App\\Filament\\Resources\\ShipResource',
    '/var/www/html/laravelproject/marine360/app/Filament/Resources/ShipTypeResource.php' => 'App\\Filament\\Resources\\ShipTypeResource',
    '/var/www/html/laravelproject/marine360/app/Filament/Resources/TagResource.php' => 'App\\Filament\\Resources\\TagResource',
    '/var/www/html/laravelproject/marine360/app/Filament/Resources/UserResource.php' => 'App\\Filament\\Resources\\UserResource',
    0 => 'Rupadana\\ApiService\\Resources\\TokenResource',
    1 => 'TomatoPHP\\FilamentMediaManager\\Resources\\FolderResource',
    2 => 'TomatoPHP\\FilamentMediaManager\\Resources\\MediaResource',
  ),
  'resourceDirectories' => 
  array (
    0 => '/var/www/html/laravelproject/marine360/app/Filament/Resources',
  ),
  'resourceNamespaces' => 
  array (
    0 => 'App\\Filament\\Resources',
  ),
  'widgets' => 
  array (
    0 => 'Filament\\Widgets\\AccountWidget',
  ),
  'widgetDirectories' => 
  array (
    0 => '/var/www/html/laravelproject/marine360/app/Filament/Widgets',
  ),
  'widgetNamespaces' => 
  array (
    0 => 'App\\Filament\\Widgets',
  ),
);