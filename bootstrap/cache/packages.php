<?php return array (
  'amendozaaguiar/filament-route-statistics' => 
  array (
    'providers' => 
    array (
      0 => 'Amendozaaguiar\\FilamentRouteStatistics\\FilamentRouteStatisticsServiceProvider',
    ),
  ),
  'anourvalar/eloquent-serialize' => 
  array (
    'aliases' => 
    array (
      'EloquentSerialize' => 'AnourValar\\EloquentSerialize\\Facades\\EloquentSerializeFacade',
    ),
  ),
  'barryvdh/laravel-debugbar' => 
  array (
    'aliases' => 
    array (
      'Debugbar' => 'Barryvdh\\Debugbar\\Facades\\Debugbar',
    ),
    'providers' => 
    array (
      0 => 'Barryvdh\\Debugbar\\ServiceProvider',
    ),
  ),
  'bezhansalleh/filament-shield' => 
  array (
    'aliases' => 
    array (
      'FilamentShield' => 'BezhanSalleh\\FilamentShield\\Facades\\FilamentShield',
    ),
    'providers' => 
    array (
      0 => 'BezhanSalleh\\FilamentShield\\FilamentShieldServiceProvider',
    ),
  ),
  'bilfeldt/laravel-correlation-id' => 
  array (
    'providers' => 
    array (
      0 => 'Bilfeldt\\CorrelationId\\CorrelationIdServiceProvider',
    ),
  ),
  'bilfeldt/laravel-request-logger' => 
  array (
    'aliases' => 
    array (
      'RequestLogger' => 'Bilfeldt\\RequestLogger\\RequestLoggerFacade',
    ),
    'providers' => 
    array (
      0 => 'Bilfeldt\\RequestLogger\\RequestLoggerServiceProvider',
    ),
  ),
  'bilfeldt/laravel-route-statistics' => 
  array (
    'providers' => 
    array (
      0 => 'Bilfeldt\\LaravelRouteStatistics\\LaravelRouteStatisticsServiceProvider',
    ),
  ),
  'blade-ui-kit/blade-heroicons' => 
  array (
    'providers' => 
    array (
      0 => 'BladeUI\\Heroicons\\BladeHeroiconsServiceProvider',
    ),
  ),
  'blade-ui-kit/blade-icons' => 
  array (
    'providers' => 
    array (
      0 => 'BladeUI\\Icons\\BladeIconsServiceProvider',
    ),
  ),
  'dedoc/scramble' => 
  array (
    'providers' => 
    array (
      0 => 'Dedoc\\Scramble\\ScrambleServiceProvider',
    ),
  ),
  'dutchcodingcompany/filament-socialite' => 
  array (
    'aliases' => 
    array (
      'FilamentSocialite' => 'DutchCodingCompany\\FilamentSocialite\\Facades\\FilamentSocialite',
    ),
    'providers' => 
    array (
      0 => 'DutchCodingCompany\\FilamentSocialite\\FilamentSocialiteServiceProvider',
    ),
  ),
  'filament/actions' => 
  array (
    'providers' => 
    array (
      0 => 'Filament\\Actions\\ActionsServiceProvider',
    ),
  ),
  'filament/filament' => 
  array (
    'providers' => 
    array (
      0 => 'Filament\\FilamentServiceProvider',
    ),
  ),
  'filament/forms' => 
  array (
    'providers' => 
    array (
      0 => 'Filament\\Forms\\FormsServiceProvider',
    ),
  ),
  'filament/infolists' => 
  array (
    'providers' => 
    array (
      0 => 'Filament\\Infolists\\InfolistsServiceProvider',
    ),
  ),
  'filament/notifications' => 
  array (
    'providers' => 
    array (
      0 => 'Filament\\Notifications\\NotificationsServiceProvider',
    ),
  ),
  'filament/spatie-laravel-settings-plugin' => 
  array (
    'providers' => 
    array (
      0 => 'Filament\\SpatieLaravelSettingsPluginServiceProvider',
    ),
  ),
  'filament/support' => 
  array (
    'providers' => 
    array (
      0 => 'Filament\\Support\\SupportServiceProvider',
    ),
  ),
  'filament/tables' => 
  array (
    'providers' => 
    array (
      0 => 'Filament\\Tables\\TablesServiceProvider',
    ),
  ),
  'filament/widgets' => 
  array (
    'providers' => 
    array (
      0 => 'Filament\\Widgets\\WidgetsServiceProvider',
    ),
  ),
  'hasnayeen/themes' => 
  array (
    'providers' => 
    array (
      0 => 'Hasnayeen\\Themes\\ThemesServiceProvider',
    ),
  ),
  'jeffgreco13/filament-breezy' => 
  array (
    'aliases' => 
    array (
      'FilamentBreezy' => 'Jeffgreco13\\FilamentBreezy\\Facades\\FilamentBreezy',
    ),
    'providers' => 
    array (
      0 => 'Jeffgreco13\\FilamentBreezy\\FilamentBreezyServiceProvider',
    ),
  ),
  'jenssegers/agent' => 
  array (
    'aliases' => 
    array (
      'Agent' => 'Jenssegers\\Agent\\Facades\\Agent',
    ),
    'providers' => 
    array (
      0 => 'Jenssegers\\Agent\\AgentServiceProvider',
    ),
  ),
  'kirschbaum-development/eloquent-power-joins' => 
  array (
    'providers' => 
    array (
      0 => 'Kirschbaum\\PowerJoins\\PowerJoinsServiceProvider',
    ),
  ),
  'lab404/laravel-impersonate' => 
  array (
    'providers' => 
    array (
      0 => 'Lab404\\Impersonate\\ImpersonateServiceProvider',
    ),
  ),
  'laravel-shift/blueprint' => 
  array (
    'providers' => 
    array (
      0 => 'Blueprint\\BlueprintServiceProvider',
    ),
  ),
  'laravel/pail' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Pail\\PailServiceProvider',
    ),
  ),
  'laravel/sail' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Sail\\SailServiceProvider',
    ),
  ),
  'laravel/sanctum' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Sanctum\\SanctumServiceProvider',
    ),
  ),
  'laravel/socialite' => 
  array (
    'aliases' => 
    array (
      'Socialite' => 'Laravel\\Socialite\\Facades\\Socialite',
    ),
    'providers' => 
    array (
      0 => 'Laravel\\Socialite\\SocialiteServiceProvider',
    ),
  ),
  'laravel/tinker' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Tinker\\TinkerServiceProvider',
    ),
  ),
  'livewire/livewire' => 
  array (
    'aliases' => 
    array (
      'Livewire' => 'Livewire\\Livewire',
    ),
    'providers' => 
    array (
      0 => 'Livewire\\LivewireServiceProvider',
    ),
  ),
  'maatwebsite/excel' => 
  array (
    'aliases' => 
    array (
      'Excel' => 'Maatwebsite\\Excel\\Facades\\Excel',
    ),
    'providers' => 
    array (
      0 => 'Maatwebsite\\Excel\\ExcelServiceProvider',
    ),
  ),
  'nesbot/carbon' => 
  array (
    'providers' => 
    array (
      0 => 'Carbon\\Laravel\\ServiceProvider',
    ),
  ),
  'nunomaduro/collision' => 
  array (
    'providers' => 
    array (
      0 => 'NunoMaduro\\Collision\\Adapters\\Laravel\\CollisionServiceProvider',
    ),
  ),
  'nunomaduro/termwind' => 
  array (
    'providers' => 
    array (
      0 => 'Termwind\\Laravel\\TermwindServiceProvider',
    ),
  ),
  'owenvoke/blade-fontawesome' => 
  array (
    'providers' => 
    array (
      0 => 'OwenVoke\\BladeFontAwesome\\BladeFontAwesomeServiceProvider',
    ),
  ),
  'pestphp/pest-plugin-laravel' => 
  array (
    'providers' => 
    array (
      0 => 'Pest\\Laravel\\PestServiceProvider',
    ),
  ),
  'pxlrbt/filament-environment-indicator' => 
  array (
    'providers' => 
    array (
      0 => '\\pxlrbt\\FilamentEnvironmentIndicator\\FilamentEnvironmentIndicatorServiceProvider',
    ),
  ),
  'pxlrbt/filament-excel' => 
  array (
    'providers' => 
    array (
      0 => 'pxlrbt\\FilamentExcel\\FilamentExcelServiceProvider',
    ),
  ),
  'resend/resend-laravel' => 
  array (
    'providers' => 
    array (
      0 => 'Resend\\Laravel\\ResendServiceProvider',
    ),
  ),
  'rupadana/filament-api-service' => 
  array (
    'aliases' => 
    array (
      'ApiService' => 'Rupadana\\ApiService\\Facades\\ApiService',
    ),
    'providers' => 
    array (
      0 => 'Rupadana\\ApiService\\ApiServiceServiceProvider',
      1 => 'Rupadana\\ApiService\\AuthServiceProvider',
    ),
  ),
  'ryangjchandler/blade-capture-directive' => 
  array (
    'aliases' => 
    array (
      'BladeCaptureDirective' => 'RyanChandler\\BladeCaptureDirective\\Facades\\BladeCaptureDirective',
    ),
    'providers' => 
    array (
      0 => 'RyanChandler\\BladeCaptureDirective\\BladeCaptureDirectiveServiceProvider',
    ),
  ),
  'socialiteproviders/manager' => 
  array (
    'providers' => 
    array (
      0 => 'SocialiteProviders\\Manager\\ServiceProvider',
    ),
  ),
  'spatie/laravel-medialibrary' => 
  array (
    'providers' => 
    array (
      0 => 'Spatie\\MediaLibrary\\MediaLibraryServiceProvider',
    ),
  ),
  'spatie/laravel-permission' => 
  array (
    'providers' => 
    array (
      0 => 'Spatie\\Permission\\PermissionServiceProvider',
    ),
  ),
  'spatie/laravel-query-builder' => 
  array (
    'providers' => 
    array (
      0 => 'Spatie\\QueryBuilder\\QueryBuilderServiceProvider',
    ),
  ),
  'spatie/laravel-settings' => 
  array (
    'providers' => 
    array (
      0 => 'Spatie\\LaravelSettings\\LaravelSettingsServiceProvider',
    ),
  ),
  'stechstudio/filament-impersonate' => 
  array (
    'providers' => 
    array (
      0 => 'STS\\FilamentImpersonate\\FilamentImpersonateServiceProvider',
    ),
  ),
  'tomatophp/console-helpers' => 
  array (
    'providers' => 
    array (
      0 => 'TomatoPHP\\ConsoleHelpers\\ConsoleHelpersServiceProvider',
    ),
  ),
  'tomatophp/filament-media-manager' => 
  array (
    'providers' => 
    array (
      0 => 'TomatoPHP\\FilamentMediaManager\\FilamentMediaManagerServiceProvider',
    ),
  ),
);