<?php

use App\Http\Controllers\Api\AuthController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

use App\Http\Controllers\Api\AuthController as CustomAuthController;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');


// Route::post('/login', [AuthController::class, 'login']);

Route::post('/auth/login', [CustomAuthController::class, 'login']);
Route::post('/auth/logout', [CustomAuthController::class, 'logout'])->middleware('auth:sanctum');
