<?php

namespace {{ namespace }};

{{ clusterImport }}use {{ resource }}\Pages;
use {{ resource }}\RelationManagers;
use {{ model }};
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class {{ resourceClass }} extends Resource
{
    protected static ?string $model = {{ modelClass }}::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';{{ clusterAssignment }}

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
{{ formSchema }}
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
{{ tableColumns }}
            ])
            ->filters([
{{ tableFilters }}
            ])
            ->actions([
{{ tableActions }}
   Tables\Actions\DeleteAction::make(),

            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
{{ tableBulkActions }}
                ]),
            ]);
    }
{{ relations }}
    public static function getPages(): array
    {
        return [
{{ pages }}
        ];
    }{{ eloquentQuery }}
}
