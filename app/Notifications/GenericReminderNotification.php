<?php
namespace App\Notifications;

use App\Models\Reminder;
use Filament\Notifications\Notification as FilamentNotification;
use Illuminate\Notifications\Notification;
use Illuminate\Notifications\Messages\MailMessage;

class GenericReminderNotification extends Notification
{
    public function __construct(public Reminder $reminder) {}

    public function via($notifiable): array
    {
        return ['database', 'mail'];
    }

    public function toMail($notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject('Reminder: ' . $this->reminder->type)
            ->line($this->reminder->message)
            ->action('View Details', url('/reminders/' . $this->reminder->id))
            ->line('Thank you for using our application!');
    }

    public function toDatabase($notifiable): array
    {
        return FilamentNotification::make()
            ->title($this->reminder->type)
            ->body($this->reminder->message)
            ->actions([
                Action::make('view')
                    ->button()
                    ->url(route('filament.resources.reminders.view', $this->reminder))
            ])
            ->getDatabaseMessage();
    }
}
