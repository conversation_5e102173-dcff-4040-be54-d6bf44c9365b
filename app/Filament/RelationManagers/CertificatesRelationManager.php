<?php

namespace App\Filament\RelationManagers;

use Filament\Forms;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Forms\Form;
use Filament\Tables;
use Filament\Tables\Table;

class CertificatesRelationManager extends RelationManager
{
    protected static string $relationship = 'certificates';

    public function form(Form $form): Form
    {
        return $form->schema([
            Forms\Components\Select::make('certificate_master_id')
                ->label('Certificate Type')
                ->relationship('master', 'name')
                ->required()
                ->reactive()
                ->afterStateUpdated(function ($state, $set, $get) {
                    // When certificate type changes, recalc expiry date if issue_date set
                    $issueDate = $get('issue_date');
                    $certificateMaster = \App\Models\CertificateMaster::find($state);

                    if ($certificateMaster && $issueDate) {
                        $period = $certificateMaster->validity_period ?? 0;
                        $unit = $certificateMaster->validity_unit ?? 'days';
                        $expiry = \Carbon\Carbon::parse($issueDate)->add($unit, (int)$period);
                        $set('expiry_date', $expiry->toDateString());
                    }
                }),
            Forms\Components\TextInput::make('certificate_number')->required(),

            Forms\Components\DatePicker::make('issue_date')
                ->required()
                ->reactive()
                ->afterStateUpdated(function ($state, $set, $get) {
                    // Get certificate master info
                    $certificateMasterId = $get('certificate_master_id');
                    $certificateMaster = \App\Models\CertificateMaster::find($certificateMasterId);

                    if ($certificateMaster && $state) {
                        $period = $certificateMaster->validity_period ?? 0;
                        $unit = $certificateMaster->validity_unit ?? 'days';
                        // Use Carbon for date addition
                        $expiry = \Carbon\Carbon::parse($state)->add($unit, (int)$period);
                        $set('expiry_date', $expiry->toDateString());
                    }
                }),
            Forms\Components\DatePicker::make('expiry_date')
                ->required(),

            Forms\Components\DatePicker::make('renewal_date')
                ->nullable()
                ->helperText('Set manually or will auto-calculate if applicable.'),

            Forms\Components\TextInput::make('issuing_authority')->required(),
            Forms\Components\Textarea::make('notes')->nullable(),
        ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('certificate_number')
            ->columns([
                Tables\Columns\TextColumn::make('master.name')->label('Certificate Type')->sortable(),
                Tables\Columns\TextColumn::make('certificate_number')->sortable(),
                Tables\Columns\TextColumn::make('issue_date'),
                Tables\Columns\TextColumn::make('expiry_date'),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ]);
    }
}
