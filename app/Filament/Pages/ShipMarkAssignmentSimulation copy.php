<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;
use App\Models\Ship;
use App\Models\ShipMark;
use App\Models\ShipCrewPositionRequirement;
use App\Models\Crew;
use App\Models\CrewAssignment;
use App\Models\CrewCertificateRequirementSet;
use Filament\Forms\Components\Select;
use Filament\Notifications\Notification;
use Illuminate\Support\Arr;

class ShipMarkAssignmentSimulation extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-adjustments-vertical';
    protected static string $view = 'filament.pages.ship-mark-assignment-simulation';
    protected static ?string $navigationLabel = 'Ship Mark Assignment';

    public $ship_id = null;
    public $ship_mark_id = null;

    // Per-position slots: [['designation_id' => x, 'slot' => 1, ...]]
    public $positionSlots = [];
    public $crewSuggestions = [];   // [slot_key => [Crew, ...]]
    public $selectedAssignments = []; // [slot_key => crew_id]
    public $currentAssignments = [];  // [slot_key => Crew|null]
    public $offboardCrew = [];

    public function mount()
    {
        $this->ship_id = null;
        $this->ship_mark_id = null;
        $this->positionSlots = [];
        $this->crewSuggestions = [];
        $this->selectedAssignments = [];
        $this->currentAssignments = [];
        $this->offboardCrew = [];
    }

    protected function getFormSchema(): array
    {
        return [
            Select::make('ship_id')
                ->label('Ship')
                ->options(fn () => Ship::pluck('name', 'id'))
                ->searchable()
                ->required()
                ->reactive()
                ->afterStateUpdated(fn () => $this->resetShipMark()),

            Select::make('ship_mark_id')
                ->label('Ship Mark')
                ->options(function (callable $get) {
                    $shipId = $get('ship_id');
                    if (!$shipId) return [];
                    // You may filter marks by ship if needed
                    return ShipMark::pluck('name', 'id');
                })
                ->searchable()
                ->required()
                ->reactive()
                ->afterStateUpdated(fn () => $this->simulateRequirements()),
        ];
    }

    // Resets everything when ship changes
    public function resetShipMark()
    {
        $this->form->fill(['ship_mark_id' => null]);
        $this->positionSlots = [];
        $this->crewSuggestions = [];
        $this->selectedAssignments = [];
        $this->currentAssignments = [];
        $this->offboardCrew = [];
    }

    // Main simulation: build slots, suggest crew, set assignments, track offboard
    public function simulateRequirements()
    {
        $data = $this->form->getState();
        $shipId = $data['ship_id'] ?? null;
        $shipMarkId = $data['ship_mark_id'] ?? null;
        if (!$shipId || !$shipMarkId) return;

        // 1. Get required position slots (explode by min_required for each req)
        $positionSlots = [];
        $positionReqs = ShipCrewPositionRequirement::with(['designation'])
            ->where('ship_id', $shipId)
            ->where('ship_mark_id', $shipMarkId)
            ->get();

        foreach ($positionReqs as $req) {
            $count = max(1, $req->min_required);
            for ($i = 1; $i <= $count; $i++) {
                $slotKey = $req->designation_id . '_' . $i; // unique slot key
                $positionSlots[$slotKey] = [
                    'position_req_id' => $req->id,
                    'designation_id' => $req->designation_id,
                    'designation_name' => $req->designation->name,
                    'slot_num' => $i,
                ];
            }
        }
        $this->positionSlots = $positionSlots;

        // 2. Get all crew currently assigned to this ship/mark
        $assignedCrew = CrewAssignment::with(['crew'])
            ->where('ship_id', $shipId)
            ->where('ship_mark_id', $shipMarkId)
            ->where('status', 'assigned')
            ->get();

        // Map by designation/slot if possible
        $currentAssignments = [];
        $usedCrewIds = [];
        foreach ($assignedCrew as $assign) {
            // Assign to first available slot of designation
            $slotKey = $assign->designation_id . '_' . (
                Arr::first(array_keys(array_filter($positionSlots, fn($ps) =>
                    $ps['designation_id'] === $assign->designation_id
                )))
                ?? '1'
            );
            $currentAssignments[$slotKey] = $assign->crew;
            $usedCrewIds[] = $assign->crew_id;
        }
        $this->currentAssignments = $currentAssignments;

        // 3. Get all available crew (not assigned anywhere) + assigned crew for this ship/mark
        $availableCrew = Crew::with(['certificates'])
            ->whereDoesntHave('currentAssignment', function ($q) {
                $q->where('status', 'assigned');
            })
            ->orWhereIn('id', $usedCrewIds)
            ->get();

        // 4. Suggest crew for each slot (check certificates ALL/ANY logic)
        $crewSuggestions = [];
        $filledCrewIds = $usedCrewIds; // We'll add new selections to this as we go

        foreach ($positionSlots as $slotKey => $slot) {
            $suggestions = [];
            foreach ($availableCrew as $crew) {
                // If already assigned in another slot, skip
                if (in_array($crew->id, $filledCrewIds) && !in_array($crew->id, $usedCrewIds)) continue;

                if ($this->crewMatchesRequirement($crew, $slot['position_req_id'])) {
                    $suggestions[] = $crew;
                }
            }
            $crewSuggestions[$slotKey] = $suggestions;

            // Auto-assign existing crew if possible
            if (isset($currentAssignments[$slotKey]) && $this->crewMatchesRequirement($currentAssignments[$slotKey], $slot['position_req_id'])) {
                $this->selectedAssignments[$slotKey] = $currentAssignments[$slotKey]->id;
                $filledCrewIds[] = $currentAssignments[$slotKey]->id;
            } else {
                // Auto-select if only 1 suggestion
                if (count($suggestions) === 1) {
                    $this->selectedAssignments[$slotKey] = $suggestions[0]->id;
                    $filledCrewIds[] = $suggestions[0]->id;
                } else {
                    $this->selectedAssignments[$slotKey] = null;
                }
            }
        }

        $this->crewSuggestions = $crewSuggestions;

        // 5. Find offboard crew (not assigned and can't fit anywhere)
        $offboard = [];
        foreach ($availableCrew as $crew) {
            $canAssign = false;
            foreach ($positionSlots as $slotKey => $slot) {
                if ($this->crewMatchesRequirement($crew, $slot['position_req_id'])) {
                    $canAssign = true; break;
                }
            }
            if (!$canAssign && !in_array($crew->id, $usedCrewIds)) {
                $offboard[] = $crew;
            }
        }
        $this->offboardCrew = $offboard;
    }

    // The core matching logic for crew->position based on certificate set ALL/ANY logic
    private function crewMatchesRequirement(Crew $crew, $positionReqId): bool
    {
        $requirementSets = CrewCertificateRequirementSet::with('requirements')
            ->where('ship_crew_position_requirement_id', $positionReqId)
            ->get();

        foreach ($requirementSets as $set) {
            $requiredCerts = $set->requirements->pluck('certificate_type_id')->toArray();
            $crewCerts = $crew->certificates->pluck('crew_certificate_type_id')->toArray();

            if ($set->logic_type === 'ALL') {
                if (array_diff($requiredCerts, $crewCerts)) {
                    return false;
                }
            } elseif ($set->logic_type === 'ANY') {
                $min = $set->min_required ?: 1;
                $matches = count(array_intersect($requiredCerts, $crewCerts));
                if ($matches < $min) {
                    return false;
                }
            }
        }
        return true;
    }

    // For passing state to blade
    protected function getViewData(): array
    {
        return [
            'positionSlots' => $this->positionSlots,
            'crewSuggestions' => $this->crewSuggestions,
            'selectedAssignments' => $this->selectedAssignments,
            'offboardCrew' => $this->offboardCrew,
        ];
    }

    // Confirm assignments: handle assigning, offboarding, etc.
    public function confirmAssignment()
    {
        $data = $this->form->getState();
        $shipId = $data['ship_id'];
        $shipMarkId = $data['ship_mark_id'];

        // 1. Assign all selected crew to each slot
        foreach ($this->positionSlots as $slotKey => $slot) {
            $crewId = $this->selectedAssignments[$slotKey] ?? null;
            if (!$crewId) continue;

            CrewAssignment::updateOrCreate([
                'ship_id' => $shipId,
                'ship_mark_id' => $shipMarkId,
                'designation_id' => $slot['designation_id'],
                'slot_num' => $slot['slot_num'] ?? 1,
            ], [
                'crew_id' => $crewId,
                'status' => 'assigned',
                'assigned_at' => now(),
                'relieved_at' => null, // Clear relieved time if reassigned
            ]);
        }

        // 2. Offboard (relieve) previously assigned crew who are now unassigned
        $assignedCrew = CrewAssignment::where('ship_id', $shipId)
            ->where('ship_mark_id', $shipMarkId)
            ->where('status', 'assigned')
            ->get();

        $stillAssignedCrewIds = array_filter($this->selectedAssignments); // All crew_ids currently assigned in the form

        foreach ($assignedCrew as $assignment) {
            if (!in_array($assignment->crew_id, $stillAssignedCrewIds)) {
                $assignment->status = 'relieved';
                $assignment->relieved_at = now();
                $assignment->save();
            }
        }

        Notification::make()
            ->title('Assignments confirmed successfully!')
            ->success()
            ->send();
    }
}
