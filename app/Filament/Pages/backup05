<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;
use Filament\Forms;
use App\Models\Ship;
use App\Models\ShipMark;
use App\Models\ShipCrewPositionRequirement;
use App\Models\Crew;
use App\Models\CrewAssignment;
use Filament\Notifications\Notification;
use Filament\Forms\Contracts\HasForms;
use Illuminate\Support\Facades\DB;

class ShipMarkAssignmentSimulation extends Page implements HasForms
{
    protected static ?string $navigationIcon = 'heroicon-o-users';
    protected static ?string $navigationLabel = 'Crew Assignment Simulation';
    protected static string $view = 'filament.pages.ship-mark-assignment-simulation';

    public $ship_id = null;
    public $ship_mark_id = null;
    public $positions = [];
    public $assignments = [];
    public $toOffboard = [];

    public function mount()
    {
        $this->fillForm();
    }

    public function fillForm()
    {
        $this->form->fill([
            'ship_id' => $this->ship_id,
            'ship_mark_id' => $this->ship_mark_id,
            'assignments' => [],
        ]);
        $this->positions = [];
        $this->toOffboard = [];

        if ($this->ship_id && $this->ship_mark_id) {
            $positions = ShipCrewPositionRequirement::with([
                'designation',
                'requirementSetGroups.sets.certificates.certificateType',
            ])
                ->where('ship_id', $this->ship_id)
                ->where('ship_mark_id', $this->ship_mark_id)
                ->get();

            $this->positions = $positions->map(function ($pos) {
                return [
                    'id'           => $pos->id,
                    'designation'  => $pos->designation->name,
                    'min_required' => $pos->min_required,
                    'max_allowed'  => $pos->max_allowed,
                    'groups'       => $pos->requirementSetGroups->map(function ($group) {
                        return [
                            'name' => $group->name,
                            'sets' => $group->sets->map(function ($set) {
                                return [
                                    'name'        => $set->name,
                                    'logic_type'  => $set->logic_type,
                                    'min_required' => $set->min_required,
                                    'certificates' => $set->certificates->map(fn($req) => $req->certificateType?->name)->filter()->values()->all(),
                                ];
                            })->toArray(),
                        ];
                    })->toArray(),
                ];
            })->toArray();

            // --- Offboarding Crew ---
            $assignedCrewIds = CrewAssignment::where('ship_id', $this->ship_id)
                ->where('ship_mark_id', $this->ship_mark_id)
                ->pluck('crew_id')->unique()->toArray();

            $allCrewIds = Crew::pluck('id')->toArray();
            $oldCrew = CrewAssignment::where('ship_id', $this->ship_id)
                ->where('ship_mark_id', '<>', $this->ship_mark_id)
                ->whereNull('relieved_at')
                ->whereIn('crew_id', $allCrewIds)
                ->with('crew')
                ->get()
                ->map->crew
                ->filter(fn($crew) => $crew && !in_array($crew->id, $assignedCrewIds))
                ->unique('id')->values();

            $this->toOffboard = $oldCrew;
        }
    }

    // Suggest eligible crew (simplified, adjust as needed)
    public function suggestCrewForPosition($position, $already = [])
    {
        return Crew::whereDoesntHave('currentAssignment', fn($q) => $q->where('status', 'assigned'))
            ->whereNotIn('id', $already)
            ->get()
            ->filter(function ($crew) use ($position) {
                foreach ($position->requirementSetGroups as $group) {
                    $okGroup = false;
                    foreach ($group->sets as $set) {
                        $requiredCertIds = $set->certificates->pluck('certificate_type_id')->toArray();
                        $crewCertIds = $crew->certificates->pluck('crew_certificate_type_id')->toArray();
                        if ($set->logic_type === 'ALL') {
                            if (count(array_diff($requiredCertIds, $crewCertIds)) === 0) {
                                $okGroup = true;
                                break;
                            }
                        } else {
                            $min = $set->min_required ?: 1;
                            if (count(array_intersect($requiredCertIds, $crewCertIds)) >= $min) {
                                $okGroup = true;
                                break;
                            }
                        }
                    }
                    if (!$okGroup) return false;
                }
                return true;
            })->values();
    }

    protected function getFormSchema(): array
    {
        $form = [
            Forms\Components\Select::make('ship_id')
                ->label('Ship')
                ->options(Ship::pluck('name', 'id'))
                ->reactive()
                ->afterStateUpdated(fn($state, $livewire) => $livewire->ship_mark_id = null)
                ->afterStateUpdated(fn($state, $livewire) => $livewire->fillForm())
                ->required(),

            Forms\Components\Select::make('ship_mark_id')
                ->label('Ship Mark')
                ->options(fn($get) => $get('ship_id') ? ShipMark::pluck('name', 'id') : [])
                ->reactive()
                ->afterStateUpdated(fn($state, $livewire) => $livewire->fillForm())
                ->required(),
        ];

        foreach ($this->positions as $position) {
            $fields = [];
            $already = [];
            $crewOptions = $this->suggestCrewForPosition(
                ShipCrewPositionRequirement::with(['requirementSetGroups.sets.certificates.certificateType'])
                    ->find($position['id']),
                $already
            )->mapWithKeys(fn($crew) => [$crew->id => $crew->display_name])->toArray();
            $hasNoCrew = count($crewOptions) === 0;

            for ($i = 0; $i < $position['min_required']; $i++) {
                $fields[] = Forms\Components\Select::make("assignments.{$position['id']}.{$i}")
                    ->label("{$position['designation']} (Slot " . ($i + 1) . ")")
                    ->options($crewOptions)
                    ->searchable()
                    ->required()
                    ->distinct()
                    ->helperText($hasNoCrew ? 'No eligible crew available!' : null)
                    ->disabled($hasNoCrew); // Optional: disable if none
                if (isset($this->assignments[$position['id']][$i])) {
                    $already[] = $this->assignments[$position['id']][$i];
                }
            }

            $reqDescription = collect($position['groups'])->map(function ($group) {
                return $group['name'] . ': ' . collect($group['sets'])->map(function ($set) {
                    return "[{$set['name']}: {$set['logic_type']}" . ($set['logic_type'] == 'ANY' ? '(' . $set['min_required'] . ')' : '') . " - " . implode(', ', $set['certificates']) . "]";
                })->join(' OR ');
            })->join(' AND ');

            $form[] = Forms\Components\Section::make($position['designation'])
                ->description(
                    $hasNoCrew
                        ? '<span style="color:#dc2626;font-weight:bold;">🚩 No eligible crew found for this position!</span><br>' . $reqDescription
                        : $reqDescription
                )
                ->schema($fields)
                ->columns($position['min_required'])
                ->columnSpanFull();
        }

        return $form;
    }

    public function save()
    {
        $data = $this->form->getState();
        $ship_id = $data['ship_id'];
        $ship_mark_id = $data['ship_mark_id'];
        $assignments = $data['assignments'] ?? [];
        DB::transaction(function () use ($ship_id, $ship_mark_id, $assignments) {
            foreach ($assignments as $position_id => $slots) {
                foreach ($slots as $crew_id) {
                    if (!$crew_id) continue;
                    CrewAssignment::updateOrCreate(
                        [
                            'ship_id' => $ship_id,
                            'ship_mark_id' => $ship_mark_id,
                            'ship_crew_position_requirement_id' => $position_id,
                            'crew_id' => $crew_id,
                        ],
                        [
                            'status' => 'assigned',
                            'assigned_at' => now(),
                        ]
                    );
                }
            }
        });

        Notification::make()
            ->title('Crew assignments saved!')
            ->success()
            ->send();

        $this->fillForm();
    }
}

<x-filament::page>
    <form wire:submit.prevent="save" class="space-y-8">
        {{ $this->form }}

        @if (count($this->toOffboard))
            <x-filament::card class="mb-6 border-red-300 bg-red-50">
                <div class="font-bold text-red-700 flex items-center gap-2 mb-2">
                    <x-filament::icon name="heroicon-o-exclamation-triangle" class="w-5 h-5" />
                    Crew to Offboard (Not eligible for any position)
                </div>
                <ul class="list-disc ml-5 text-red-600">
                    @foreach ($this->toOffboard as $crew)
                        <li>
                            {{ $crew->display_name ?? $crew->name ?? 'Crew' }}
                            ({{ $crew->unique_crew_id ?? $crew->id }})
                        </li>
                    @endforeach
                </ul>
            </x-filament::card>
        @endif

        <div class="mt-6 flex justify-end">
            <x-filament::button type="submit" color="primary">
                Save Assignments
            </x-filament::button>
        </div>
    </form>
</x-filament::page>



--------------------------------------------

<x-filament::page>
    <form wire:submit.prevent="save">
        {{ $this->form }}

        @if (count($this->toOffboard))
            <x-filament::card class="mb-6 border-red-300 bg-red-50">
                <div class="font-bold text-red-700 flex items-center gap-2 mb-2">
                    <x-filament::icon name="heroicon-o-exclamation-triangle" class="w-5 h-5" /> Crew to Offboard
                </div>
                <ul class="list-disc ml-5 text-red-600">
                    @foreach ($this->toOffboard as $crew)
                        <li>{{ $crew->display_name }} ({{ $crew->unique_crew_id }}) - <span class="text-xs">No longer
                                meets requirements for any position in this Ship/Mark</span></li>
                    @endforeach
                </ul>
            </x-filament::card>
        @endif

        @if (count($this->positions))
            <x-filament::card class="space-y-4">
                @foreach ($this->positions as $pos)
                    <div class="flex flex-col md:flex-row items-center gap-4 justify-between border-b pb-2">
                        <div class="flex-1">
                            <div class="font-bold">{{ $pos['designation'] }}</div>
                            <div class="text-xs text-gray-600">
                                Min: {{ $pos['min_required'] }}, Max: {{ $pos['max_allowed'] }}
                            </div>
                            <div class="text-xs text-gray-500 mt-1">
                                @foreach ($pos['groups'] as $group)
                                    <div>
                                        <span class="inline-block font-semibold">{{ $group['name'] }}</span>
                                        @foreach ($group['sets'] as $set)
                                            <span
                                                class="inline-block bg-primary-100 text-primary-800 rounded px-2 py-0.5 text-xs ml-1 mb-1">
                                                {{ $set['name'] }}:
                                                {{ $set['logic_type'] }}{{ $set['logic_type'] === 'ANY' ? ' (' . $set['min_required'] . ')' : '' }}
                                                [{{ implode(', ', $set['certificates']) }}]
                                            </span>
                                        @endforeach
                                    </div>
                                @endforeach
                            </div>
                        </div>
                        <div class="flex items-center gap-2">
                            @for ($slot = 0; $slot < $pos['min_required']; $slot++)
                                <x-filament::input.wrapper class="mb-2">
                                    <select wire:model.defer="assignments.{{ $pos['id'] }}.{{ $slot }}"
                                        class="filament-forms-select-component w-52 border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500">
                                        <option value="">-- Select Crew --</option>
                                        @php
                                            // Get the list of IDs already chosen for other slots for this position
                                            $selectedInOtherSlots = collect($this->assignments[$pos['id']] ?? [])
                                                ->except($slot)
                                                ->filter()
                                                ->toArray();
                                        @endphp
                                        @foreach ($this->suggestions[$pos['id']] ?? [] as $crew)
                                            <option value="{{ $crew->id }}"
                                                @if (in_array($crew->id, $selectedInOtherSlots)) disabled @endif>
                                                {{ $crew->display_name }} ({{ $crew->unique_crew_id }})
                                            </option>
                                        @endforeach
                                    </select>
                                    <span class="text-xs text-gray-400 ml-2">Slot {{ $slot + 1 }}</span>
                                </x-filament::input.wrapper>
                            @endfor
                        </div>
                    </div>
                @endforeach
            </x-filament::card>
        @else
            <x-filament::card class="mt-8 text-center text-gray-500">
                Select a Ship and Mark to simulate crew assignments.
            </x-filament::card>
        @endif

        <div class="mt-6 flex justify-end">
            <x-filament::button type="submit" color="primary">
                Save Assignments
            </x-filament::button>
        </div>
    </form>
</x-filament::page>
<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;
use Filament\Forms;
use App\Models\Ship;
use App\Models\ShipMark;
use App\Models\ShipCrewPositionRequirement;
use App\Models\Crew;
use App\Models\CrewAssignment;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\DB;

class ShipMarkAssignmentSimulation extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-users';
    protected static ?string $navigationLabel = 'Crew Assignment Simulation';
    protected static string $view = 'filament.pages.ship-mark-assignment-simulation';

    public $ship_id;
    public $ship_mark_id;
    public $positions = [];      // [ ['id'=>, 'designation'=>, 'min_required'=>, ...], ... ]
    public $assignments = [];    // [position_id][slot] = crew_id

    public $toOffboard = [];     // [Crew, ...] ← Not implemented in this minimal example

    public function mount()
    {
        $this->fillForm();
    }

    public function fillForm()
    {
        $this->form->fill([
            'ship_id' => $this->ship_id,
            'ship_mark_id' => $this->ship_mark_id,
            'assignments' => [],
        ]);
        $this->positions = [];
        if ($this->ship_id && $this->ship_mark_id) {
            $this->positions = ShipCrewPositionRequirement::with('designation')
                ->where('ship_id', $this->ship_id)
                ->where('ship_mark_id', $this->ship_mark_id)
                ->get()
                ->map(function ($pos) {
                    return [
                        'id'           => $pos->id,
                        'designation'  => $pos->designation->name,
                        'min_required' => $pos->min_required,
                        'max_allowed'  => $pos->max_allowed,
                    ];
                })->toArray();
        }
    }

    protected function getFormSchema(): array
    {
        return [
            Forms\Components\Select::make('ship_id')
                ->label('Ship')
                ->options(Ship::pluck('name', 'id'))
                ->reactive()
                ->afterStateUpdated(function ($state, $livewire) {
                    $livewire->ship_mark_id = null;
                    $livewire->positions = [];
                    $livewire->fillForm();
                })
                ->required(),

            Forms\Components\Select::make('ship_mark_id')
                ->label('Ship Mark')
                ->options(fn ($get) =>
                    $get('ship_id')
                        ? ShipMark::pluck('name', 'id')
                        : []
                )
                ->reactive()
                ->afterStateUpdated(function ($state, $livewire) {
                    $livewire->fillForm();
                })
                ->required(),

            // Dynamic: For each position, render N select boxes
            Forms\Components\Group::make(
                fn ($get) => collect($this->positions)->map(function ($position) use ($get) {
                    $fields = [];
                    $alreadyAssigned = [];
                    for ($i = 0; $i < $position['min_required']; $i++) {
                        $fields[] = Forms\Components\Select::make("assignments.{$position['id']}.{$i}")
                            ->label("{$position['designation']} (Slot ".($i+1).")")
                            ->options(
                                Crew::whereNotIn('id', $alreadyAssigned)
                                                ->get()
                                                ->mapWithKeys(fn($c) => [$c->id => $c->display_name])
                            )
                            ->searchable()
                            ->required()
                            ->distinct()
                            ->columnSpan(1);
                        // Exclude picked crew in this position from next slot
                        if (isset($get('assignments')[$position['id']][$i])) {
                            $alreadyAssigned[] = $get('assignments')[$position['id']][$i];
                        }
                    }
                    return Forms\Components\Section::make($position['designation'])
                        ->description("Min: {$position['min_required']} Max: {$position['max_allowed']}")
                        ->schema($fields)
                        ->columns($position['min_required']);
                })->toArray()
            )->visible(fn ($get) => $get('ship_id') && $get('ship_mark_id'))
             ->columnSpanFull(),
        ];
    }

    public function save()
    {
        $data = $this->form->getState();
        $ship_id = $data['ship_id'];
        $ship_mark_id = $data['ship_mark_id'];
        $assignments = $data['assignments'] ?? [];

        DB::transaction(function () use ($ship_id, $ship_mark_id, $assignments) {
            foreach ($assignments as $position_id => $slots) {
                foreach ($slots as $crew_id) {
                    CrewAssignment::updateOrCreate(
                        [
                            'ship_id' => $ship_id,
                            'ship_mark_id' => $ship_mark_id,
                            'ship_crew_position_requirement_id' => $position_id,
                            'crew_id' => $crew_id,
                        ],
                        [
                            'status' => 'assigned',
                            'assigned_at' => now(),
                        ]
                    );
                }
            }
        });

        Notification::make()
            ->title('Crew assignments saved!')
            ->success()
            ->send();

        $this->fillForm();
    }
}
------------------------

<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;
use Filament\Forms;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Repeater;
use Filament\Notifications\Notification;
use App\Models\Ship;
use App\Models\ShipMark;
use App\Models\ShipCrewPositionRequirement;
use App\Models\Crew;
use App\Models\CrewAssignment;
use Illuminate\Support\Facades\DB;

class ShipMarkAssignmentSimulation extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-users';
    protected static ?string $navigationLabel = 'Crew Assignment Simulation';
    protected static string $view = 'filament.pages.ship-mark-assignment-simulation';

    public $ship_id;
    public $ship_mark_id;
    public $positions = [];
    public $assignments = [];   // [ positionId => [slotId => crewId] ]
    public $toOffboard = [];    // Ineligible crew

    public $suggestions = [];

    public function mount()
    {
        $this->form->fill(['ship_id' => null, 'ship_mark_id' => null]);
    }

    protected function getFormSchema(): array
    {
        return [
            Section::make('Select Context')
                ->schema([
                   Select::make('ship_id')
    ->label('Ship')
    ->options(Ship::pluck('name', 'id'))
    ->reactive()
    ->afterStateUpdated(function ($state, $livewire) {
        $livewire->ship_mark_id = null;
        $livewire->positions = [];
        $livewire->assignments = [];
        $livewire->toOffboard = [];
        $livewire->suggestions = [];
        $livewire->form->fill([
            'ship_mark_id' => null,
            'assignments' => [],
        ]);
    })
    ->required(),
                   Select::make('ship_mark_id')
    ->label('Ship Mark')
    ->options(fn($get) => $get('ship_id') ? ShipMark::pluck('name', 'id') : [])
    ->reactive()
    ->afterStateUpdated(fn($state, $livewire) => $livewire->simulate())
    ->required(),
                ]),
            Group::make()
                ->schema(fn() => $this->renderPositionSections())
                ->visible(fn() => count($this->positions) > 0),
        ];
    }

    protected function renderPositionSections(): array
    {
        return array_map(function ($pos) {
            $slots = [];
            for ($i = 0; $i < $pos['min_required']; $i++) {
                $slots[] = Select::make("assignments.{$pos['id']}.{$i}")
                    ->label("{$pos['designation']} – Slot " . ($i + 1))
                    ->options($this->getSlotOptions($pos['id'], $i))
                    ->required();
            }

            return Section::make($pos['designation'])
                ->description("Min: {$pos['min_required']} | Max: {$pos['max_allowed']}")
                ->schema($slots)
                ->columns($pos['min_required'])
                ->extraAttributes([
                    'class' => !$pos['satisfied'] ? 'bg-red-50 border border-red-300' : 'bg-green-50 border border-green-300',
                ]);
        }, $this->positions);
    }

    protected function getSlotOptions($posId, $slotIndex): array
    {
        $selectedOther = collect($this->assignments[$posId] ?? [])
            ->except([$slotIndex])
            ->filter()
            ->values()
            ->toArray();

        return collect($this->suggestions[$posId] ?? [])
            ->reject(fn($c) => in_array($c->id, $selectedOther))
            ->mapWithKeys(fn($c) => [$c->id => "{$c->display_name} ({$c->unique_crew_id})"])
            ->toArray();
    }

    public function simulate()
    {
        $this->positions = [];
        $this->assignments = [];
        $this->toOffboard = [];

        if (!$this->ship_id || !$this->ship_mark_id) return;

        $reqs = ShipCrewPositionRequirement::with([
            'designation',
            'requirementSetGroups.sets.certificates.certificateType',
        ])
            ->where(['ship_id' => $this->ship_id, 'ship_mark_id' => $this->ship_mark_id])
            ->get();

        // Determine eligible crew and flagged-to-offboard
        $current = CrewAssignment::with('crew')
            ->where(['ship_id' => $this->ship_id, 'ship_mark_id' => $this->ship_mark_id])
            ->get();

        $allEligible = Crew::with('certificates.certificateType')->get();

        $this->positions = $reqs->map(fn($pos) => [
            'id' => $pos->id,
            'designation' => $pos->designation->name,
            'min_required' => $pos->min_required,
            'max_allowed' => $pos->max_allowed,
            'satisfied' => false,
        ])->toArray();

        foreach ($reqs as $pos) {
            $eligible = $this->suggestCrewFor($pos);
            $this->suggestions[$pos->id] = $eligible;

            $this->positions = array_map(
                fn($x) => $x['id'] == $pos->id
                    ? array_merge($x, ['satisfied' => count($eligible) >= $x['min_required']])
                    : $x,
                $this->positions
            );

            $currentAssigned = $current->firstWhere('ship_crew_position_requirement_id', $pos->id);
            if ($currentAssigned) {
                foreach (range(0, $pos->min_required - 1) as $slot) {
                    $this->assignments[$pos->id][$slot] = $currentAssigned->crew->id;
                }
            }
        }

        // Offboarding detection
        $current->each(function ($ca) {
            if (! collect($this->suggestions)
                ->flatten()
                ->pluck('id')
                ->contains($ca->crew_id)) {
                $this->toOffboard[] = $ca->crew;
            }
        });

        Notification::make()
            ->title('Simulation updated')
            ->info()
            ->send();
    }

    protected function suggestCrewFor($pos)
    {
        // Simplified eligibility logic: must meet ANY one group
        $crew = Crew::with('certificates.certificateType')->get();
        return $crew->filter(function ($c) use ($pos) {
            // group logic: true if any group with ALL or ANY satisfied
            return $pos->requirementSetGroups->first(
                fn($g) =>
                $g->sets->contains(fn($set) => $this->checkSet($c, $set))
            );
        });
    }

    protected function checkSet($crew, $set): bool
    {
        $have = $crew->certificates->pluck('certificateType.id')->toArray();
        $req = $set->certificates->pluck('certificateType.id')->toArray();
        if ($set->logic_type === 'ALL') {
            return empty(array_diff($req, $have));
        }
        return count(array_intersect($req, $have)) >= ($set->min_required ?: 1);
    }

    public function save()
    {
        DB::transaction(function () {
            foreach ($this->assignments as $posId => $slots) {
                foreach ($slots as $crewId) {
                    CrewAssignment::updateOrCreate(
                        [
                            'ship_id' => $this->ship_id,
                            'ship_mark_id' => $this->ship_mark_id,
                            'ship_crew_position_requirement_id' => $posId,
                            'crew_id' => $crewId,
                        ],
                        [
                            'status' => 'assigned',
                            'assigned_at' => now(),
                        ]
                    );
                }
            }
        });

        Notification::make()
            ->title('Saved!')
            ->success()
            ->send();
    }
}
<x-filament::page>
    <form wire:submit.prevent="save">
        {{ $this->form }}

        @if (count($this->toOffboard))
            <x-filament::card class="mb-6 border-red-300 bg-red-50">
                <div class="font-bold text-red-700 flex items-center gap-2 mb-2">
                    <x-filament::icon name="heroicon-o-exclamation-triangle" class="w-5 h-5" />
                    Crew to Offboard
                </div>
                <ul class="list-disc ml-5 text-red-600">
                    @foreach ($this->toOffboard as $crew)
                        <li>
                            {{ $crew->display_name }} ({{ $crew->unique_crew_id }})
                            <span class="text-xs">No longer meets requirements for any position in this Ship/Mark</span>
                        </li>
                    @endforeach
                </ul>
            </x-filament::card>
        @endif

        <div class="mt-6 flex justify-end">
            <x-filament::button type="submit" color="primary">
                Save Assignments
            </x-filament::button>
        </div>
    </form>
</x-filament::page>
------------------------------------
<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;
use Filament\Forms;
use App\Models\Ship;
use App\Models\ShipMark;
use App\Models\ShipCrewPositionRequirement;
use App\Models\Crew;
use App\Models\CrewAssignment;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\DB;

class ShipMarkAssignmentSimulation extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-users';
    protected static ?string $navigationLabel = 'Crew Assignment Simulation';
    protected static string $view = 'filament.pages.ship-mark-assignment-simulation';

    public $ship_id;
    public $ship_mark_id;
    public $positions = [];        // Array of positions
    public $assignments = [];      // [positionId][slot] = crewId
    public $toOffboard = [];       // List of ineligible crew
    public $suggestions = [];      // [positionId][crew]

    // --------- Mount ----------
    public function mount()
    {
        $this->form->fill(['ship_id' => null, 'ship_mark_id' => null]);
    }

    // --------- Filament Form Schema ----------
    protected function getFormSchema(): array
    {
        return [
            Forms\Components\Section::make('Context')
                ->columns(3)
                ->schema([
                    Forms\Components\Select::make('ship_id')
                        ->label('Ship')
                        ->options(Ship::pluck('name', 'id'))
                        ->reactive()
                        ->afterStateUpdated(fn() => $this->resetContext())
                        ->required(),
                    Forms\Components\Select::make('ship_mark_id')
                        ->label('Ship Mark')
                        ->options(fn($get) => $get('ship_id') ? ShipMark::pluck('name', 'id') : [])
                        ->reactive()
                        ->afterStateUpdated(fn() => $this->simulate())
                        ->required(),
                ]),
            Forms\Components\Group::make()
                ->columns(3)
                ->schema(fn() => $this->renderPositionSections())
                ->visible(fn() => count($this->positions) > 0),
            // Offboard warning at top
            Forms\Components\Section::make()
                ->schema([
                    Forms\Components\Placeholder::make('offboard_warning')
                        ->content(
                            fn() =>
                            count($this->toOffboard)
                                ? '<div class="bg-red-50 border-l-4 border-red-500 text-red-900 p-3 mb-4">
                                    <strong>Crew to Offboard:</strong>
                                    <ul class="list-disc ml-5">' .
                                collect($this->toOffboard)->map(
                                    fn($c) =>
                                    "<li>{$c->display_name} ({$c->unique_crew_id})</li>"
                                )->implode('') .
                                '</ul>
                                </div>'
                                : ''
                        )

                ])
                ->visible(fn() => count($this->toOffboard) > 0),
        ];
    }

    // --------- Render Dynamic Position Sections ----------
    protected function renderPositionSections(): array
    {
        return array_map(function ($pos) {
            $slots = [];
            $anySlotNoOptions = false;
            $allSlotsAssigned = true;
            $anyAssigned = false;

            $assignedSlots = $this->assignments[$pos['id']] ?? [];

            for ($i = 0; $i < $pos['min_required']; $i++) {
                $options = $this->getSlotOptions($pos['id'], $i);

                // If any slot has no options, red border later
                if (count($options) === 0) {
                    $anySlotNoOptions = true;
                }
                // If any slot not assigned, not all assigned
                if (empty($assignedSlots[$i])) {
                    $allSlotsAssigned = false;
                } else {
                    $anyAssigned = true;
                }

                $slots[] = Forms\Components\Select::make("assignments.{$pos['id']}.{$i}")
                    ->label("Slot " . ($i + 1))
                    ->options($options)
                    ->disabled(count($options) === 0)
                    ->required()
                    ->distinct()
                    ->reactive()
                    ->placeholder('-- Select Crew --')
                    ->hint(count($options) === 0 ? 'No crew available' : '');
            }

            // Certificate logic summary
            $summary = '';
            foreach ($pos['groups'] as $group) {
                $summary .= "<div class='mb-2'><b>{$group['name']}</b><br>";
                foreach ($group['sets'] as $set) {
                    $setLogic = $set['logic_type'] === 'ALL'
                        ? 'All'
                        : "Any {$set['min_required']}";
                    $certNames = implode(', ', $set['certificates']);
                    $summary .= "<span class='inline-block bg-primary-100 text-primary-800 rounded px-2 py-0.5 text-xs mr-1 mb-1'>{$set['name']}: <b>{$setLogic}</b> [{$certNames}]</span><br>";
                }
                $summary .= "</div>";
            }

            // Dynamic color logic
            $borderClass = '';
            if ($anySlotNoOptions) {
                $borderClass = 'border border-red-500 bg-red-50';
            } elseif ($allSlotsAssigned && $anyAssigned) {
                $borderClass = 'border border-green-500 bg-green-50';
            }

            return Forms\Components\Section::make($pos['designation'])
                ->description(fn() => new \Illuminate\Support\HtmlString($summary))
                ->schema($slots)
                ->columns($pos['min_required'])
                ->extraAttributes(['class' => $borderClass]);
        }, $this->positions ?? []);
    }

    // --------- Get Eligible Crew Options for a Slot ----------

    protected function getSlotOptions($currentPosId, $currentSlotIndex): array
    {
        // Gather all assigned crew IDs except the current slot
        $selectedCrewIds = [];
        foreach ($this->assignments as $posId => $slots) {
            foreach ($slots as $slotIndex => $crewId) {
                if (
                    !is_null($crewId) &&
                    !(($posId == $currentPosId) && ($slotIndex == $currentSlotIndex))
                ) {
                    $selectedCrewIds[] = $crewId;
                }
            }
        }

        // Only show crew not already assigned anywhere else
        return collect($this->suggestions[$currentPosId] ?? [])
            ->reject(fn($c) => in_array($c->id, $selectedCrewIds))
            ->mapWithKeys(fn($c) => [$c->id => "{$c->display_name} ({$c->unique_crew_id})"])
            ->toArray();
    }

    // --------- Main Simulation Logic ----------
    public function simulate()
    {
        $this->positions = [];
        $this->assignments = [];
        $this->toOffboard = [];
        $this->suggestions = [];

        if (!$this->ship_id || !$this->ship_mark_id) return;

        $reqs = ShipCrewPositionRequirement::with([
            'designation',
            'requirementSetGroups.sets.certificates.certificateType',
        ])
            ->where(['ship_id' => $this->ship_id, 'ship_mark_id' => $this->ship_mark_id])
            ->get();

        $current = CrewAssignment::with('crew')
            ->where(['ship_id' => $this->ship_id, 'ship_mark_id' => $this->ship_mark_id])
            ->get();

        $allEligible = Crew::with('certificates.certificateType')->get();

        // Build positions with certificate group/set info
        $this->positions = $reqs->map(function ($pos) {
            $groups = $pos->requirementSetGroups->map(function ($group) {
                return [
                    'name' => $group->name,
                    'sets' => $group->sets->map(function ($set) {
                        return [
                            'name' => $set->name,
                            'logic_type' => $set->logic_type,
                            'min_required' => $set->min_required,
                            'certificates' => $set->certificates->map(fn($cert) => $cert->certificateType->name)->toArray(),
                        ];
                    })->toArray(),
                ];
            })->toArray();
            return [
                'id' => $pos->id,
                'designation' => $pos->designation->name,
                'min_required' => $pos->min_required,
                'max_allowed' => $pos->max_allowed,
                'groups' => $groups,
                'satisfied' => false,
            ];
        })->toArray();

        // Fill $this->suggestions and mark satisfied
        foreach ($reqs as $pos) {
            $eligible = $this->suggestCrewFor($pos);
            $this->suggestions[$pos->id] = $eligible;
            $this->positions = array_map(
                fn($x) => $x['id'] == $pos->id
                    ? array_merge($x, ['satisfied' => count($eligible) >= $x['min_required']])
                    : $x,
                $this->positions
            );
            // Assign already assigned crew
            $currentAssigned = $current->firstWhere('ship_crew_position_requirement_id', $pos->id);
            if ($currentAssigned) {
                foreach (range(0, $pos->min_required - 1) as $slot) {
                    $this->assignments[$pos->id][$slot] = $currentAssigned->crew->id;
                }
            }
        }

        // Offboard: crew assigned but not eligible anywhere
        $current->each(function ($ca) {
            if (!collect($this->suggestions)
                ->flatten()
                ->pluck('id')
                ->contains($ca->crew_id)) {
                $this->toOffboard[] = $ca->crew;
            }
        });
    }

    // --------- Suggest Crew for a Position ---------
    protected function suggestCrewFor($pos)
    {
        $crew = Crew::with('certificates.certificateType')->get();
        return $crew->filter(function ($c) use ($pos) {
            // Satisfy ANY group with at least one set satisfied
            return $pos->requirementSetGroups->first(
                fn($g) =>
                $g->sets->contains(fn($set) => $this->checkSet($c, $set))
            );
        });
    }

    // --------- Check if Crew Satisfies a Set ---------
    protected function checkSet($crew, $set): bool
    {
        $have = $crew->certificates->pluck('certificateType.id')->toArray();
        $req = $set->certificates->pluck('certificateType.id')->toArray();
        if ($set->logic_type === 'ALL') {
            return empty(array_diff($req, $have));
        }
        return count(array_intersect($req, $have)) >= ($set->min_required ?: 1);
    }

    // --------- Reset All ---------
    public function resetContext()
    {
        $this->ship_mark_id = null;
        $this->positions = [];
        $this->assignments = [];
        $this->toOffboard = [];
        $this->suggestions = [];
        $this->form->fill(['ship_mark_id' => null]);
    }

    // --------- Save Logic ---------
    public function save()
    {
        // Validate
        foreach ($this->positions as $pos) {
            $slots = $this->assignments[$pos['id']] ?? [];
            if (count(array_filter($slots)) < $pos['min_required']) {
                Notification::make()
                    ->title("Fill all slots for {$pos['designation']}")
                    ->danger()
                    ->send();
                return;
            }
        }
        // Save
        DB::transaction(function () {
            foreach ($this->assignments as $posId => $slots) {
                foreach ($slots as $crewId) {
                    CrewAssignment::updateOrCreate(
                        [
                            'ship_id' => $this->ship_id,
                            'ship_mark_id' => $this->ship_mark_id,
                            'ship_crew_position_requirement_id' => $posId,
                            'crew_id' => $crewId,
                        ],
                        [
                            'status' => 'assigned',
                            'assigned_at' => now(),
                        ]
                    );
                }
            }
        });
        Notification::make()
            ->title('Crew assignments saved!')
            ->success()
            ->send();
        $this->simulate();
    }
}
