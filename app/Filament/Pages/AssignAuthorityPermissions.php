<?php

namespace App\Filament\Pages;

use App\Models\AuthorityLevel;
use App\Models\MobileModule;
use Filament\Pages\Page;
use Filament\Forms;
use Filament\Notifications\Notification;
use Illuminate\Support\Str;
use Illuminate\Contracts\Support\Htmlable;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Components\{Select, Checkbox, Fieldset, Group, Section, Grid, Placeholder, Toggle};
use Filament\Actions\Action;

class AssignAuthorityPermissions extends Page implements HasForms
{
    use InteractsWithForms;

    protected static string $view = 'filament.pages.assign-authority-permissions';

    public ?AuthorityLevel $record = null;
    public array $permissionsFormState = [];

    protected static ?string $navigationLabel = null;

    public ?int $copyFromAuthorityLevelId = null;
    public bool $selectAll = false;

    public array $moduleToggle = [];



    public static function getSlug(): string
    {
        return 'assign-permissions/{record}';
    }



    public static function shouldRegisterNavigation(): bool
    {
        return false;
    }

    public function mount(AuthorityLevel $record): void
    {
        $this->record = $record;

        $this->form->fill([
            'permissionsFormState' => $this->getCurrentPermissions(),
            'selectAll' => false,
        ]);

        $this->record = $record;

        // Prefill per-module toggles (optional)
        foreach (MobileModule::with('permissions')->get() as $module) {
            $hasAll = $module->permissions->every(
                fn($perm) =>
                $this->record->permissions->contains($perm->id)
            );
            $this->moduleToggle[$module->id] = $hasAll;
        }
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('Copy Permissions')
                ->icon('heroicon-o-clipboard-document-check')
                ->visible(fn() => $this->copyFromAuthorityLevelId !== null)
                ->action('copyPermissionsFromAnotherAuthority'),
        ];
    }

    protected function getFormSchema(): array
    {
        return [
            Select::make('copyFromAuthorityLevelId')
                ->label('Copy Permissions From')
                ->options(AuthorityLevel::where('id', '!=', $this->record->id)->pluck('name', 'id'))
                ->searchable()
                ->live(),

            Toggle::make('selectAll')
                ->label('Select All Permissions')
                ->reactive()
                ->afterStateUpdated(fn($state) => $this->setAllPermissions($state))
                ->helperText('Toggle all module permissions on/off'),

            Section::make("Permissions for {$this->record->name}")
                ->schema($this->buildModulePermissionSchema())
                ->columns(1)
                ->extraAttributes(['class' => 'space-y-4']),
        ];
    }

    protected function buildModulePermissionSchema(): array
    {
        $schema = [];

        foreach (MobileModule::with('permissions')->get() as $module) {
            $fields = $module->permissions->map(function ($perm) use ($module) {
                return Checkbox::make("permissionsFormState.{$module->id}.{$perm->id}")
                    ->label(Str::after($perm->name, '.'))
                    ->inline()
                    ->live(); // ✅ this is critical!
            })->toArray();

            $fieldset = Fieldset::make($module->name)
                ->schema([
                    Checkbox::make("moduleToggle.{$module->id}")
                        ->label("Select All")
                        ->inline()
                        ->live()
                        ->afterStateUpdated(fn($state) => $this->setModulePermissions($module->id, $state)),
                    ...$fields,
                ])
                ->columns(6)
                ->extraAttributes(['class' => 'p-2 border rounded-md']);

            $schema[] = $fieldset;
        }

        return $schema;
    }

    public function setModulePermissions(int $moduleId, bool $state): void
    {
        $module = MobileModule::with('permissions')->find($moduleId);
        if (! $module) return;

        foreach ($module->permissions as $perm) {
            $this->permissionsFormState[$moduleId][$perm->id] = $state;
        }

        $this->moduleToggle[$moduleId] = $state;
    }


    public function setAllPermissions(bool $state): void
    {
        foreach (MobileModule::with('permissions')->get() as $module) {
            foreach ($module->permissions as $perm) {
                $this->permissionsFormState[$module->id][$perm->id] = $state;
            }

            $this->moduleToggle[$module->id] = $state;
        }
    }

    public function copyPermissionsFromAnotherAuthority(): void
    {
        $source = AuthorityLevel::find($this->copyFromAuthorityLevelId);
        if (! $source) {
            Notification::make()->title('Invalid source')->danger()->send();
            return;
        }

        $this->permissionsFormState = [];
        $this->moduleToggle = [];

        foreach (MobileModule::with('permissions')->get() as $module) {
            $hasAll = true;

            foreach ($module->permissions as $perm) {
                $assigned = $source->permissions->contains($perm->id);
                $this->permissionsFormState[$module->id][$perm->id] = $assigned;

                if (! $assigned) {
                    $hasAll = false;
                }
            }

            $this->moduleToggle[$module->id] = $hasAll;
        }

        Notification::make()->title("Permissions copied from {$source->name}")->success()->send();
    }


    protected function getCurrentPermissions(): array
    {
        $result = [];
        $assignedIds = $this->record->permissions->pluck('id')->toArray();

        foreach (MobileModule::with('permissions')->get() as $module) {
            foreach ($module->permissions as $perm) {
                $result[$module->id][$perm->id] = in_array($perm->id, $assignedIds);
            }
        }

        return $result;
    }

    public function save(): void
    {
        $selectedIds = collect($this->permissionsFormState)
            ->flatMap(fn($permissions) => collect($permissions)->filter()->keys())
            ->unique()
            ->values()
            ->toArray();

        $this->record->permissions()->sync($selectedIds);

        Notification::make()
            ->title('Permissions updated successfully.')
            ->success()
            ->send();
    }

    public function getTitle(): string|Htmlable
    {
        return 'Assign Module Permissions';
    }

    protected function getFormActions(): array
    {
        return [
            Forms\Components\Actions\Action::make('save')
                ->label('Save Permissions')
                ->submit('save')
                ->color('primary'),
        ];
    }

    public function updated($property, $value): void
    {
        // Check for individual permission checkbox changes
        if (preg_match('/^permissionsFormState\.(\d+)\.(\d+)$/', $property, $matches)) {
            $moduleId = (int) $matches[1];

            $module = MobileModule::with('permissions')->find($moduleId);
            if (! $module) return;

            $allChecked = $module->permissions->every(function ($perm) use ($moduleId) {
                return !empty($this->permissionsFormState[$moduleId][$perm->id]);
            });

            // ✅ Update per-module select all checkbox
            $this->moduleToggle[$moduleId] = $allChecked;

            // ✅ Update global select all toggle
            $this->selectAll = MobileModule::with('permissions')->get()->every(function ($module) {
                return $module->permissions->every(function ($perm) use ($module) {
                    return !empty($this->permissionsFormState[$module->id][$perm->id]);
                });
            });
        }
    }
}
