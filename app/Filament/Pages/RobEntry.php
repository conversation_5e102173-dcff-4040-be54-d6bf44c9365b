<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;
use Filament\Forms;
use App\Models\Ship;
use App\Models\ShipAsset;
use App\Models\AssetLogMetric;
use App\Models\DailyAssetLog;
use App\Models\AssetTransaction;
use App\Models\MasterEntry;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\DB;
use Filament\Forms\Get;

class RobEntry extends Page implements Forms\Contracts\HasForms
{
    use Forms\Concerns\InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-clipboard-document-list';
    protected static ?string $navigationGroup = 'Logs & Readings';
    protected static ?string $title = 'ROB Entry';
    protected static string $view = 'filament.pages.rob-entry';

    // Public property for form state
    public ?int $ship_id = null;
     public array $metrics = [];

    protected function getFormSchema(): array
    {
        return [
           Forms\Components\Select::make('ship_id')
            ->label('Ship')
            ->options(Ship::pluck('name', 'id'))
            ->required()
            ->reactive()
            ->afterStateUpdated(fn ($state, callable $set) => $set('metrics', []))
            ->searchable(),
            Forms\Components\Group::make(
                function (Get $get) {
                    $shipId = $get('ship_id');
                    if (!$shipId) {
                        return [
                            Forms\Components\Placeholder::make('select_ship_msg')
                                ->content('Select a ship to load all asset readings.'),
                        ];
                    }

                    $shipAssets = ShipAsset::with(['asset', 'metrics.metricType'])
                        ->where('ship_id', $shipId)
                        ->get();

                    $fields = [];

                    foreach ($shipAssets as $shipAsset) {
                        foreach ($shipAsset->metrics as $metric) {
                            $lastLog = AssetLogMetric::where('ship_asset_metric_id', $metric->id)
                                ->orderByDesc('created_at')
                                ->first();

                            $prefix = $shipAsset->id . '_' . $metric->id;


                            $fields[] = Forms\Components\Fieldset::make($shipAsset->asset->name . ' / ' . $metric->metricType->name)
                                ->schema([
                                    Forms\Components\Placeholder::make("unit_{$prefix}")
                                        ->label('Unit')
                                        ->content($metric->unit ?? $metric->metricType?->default_unit ?? '-'),
                                    Forms\Components\Placeholder::make("old_value_{$prefix}")
                                        ->label('Previous Value')
                                        ->content($lastLog?->value ?? '-'),
                                    Forms\Components\TextInput::make("metrics.{$prefix}.current_value")
                                        ->label('Current Value')
                                        ->numeric()
                                        ->required(),
                                    Forms\Components\TextInput::make("metrics.{$prefix}.addition")
                                        ->label('Addition (if topped up)')
                                        ->numeric()
                                        ->nullable(),
                                ])
                                ->columns(2);
                        }
                    }

                    if (empty($fields)) {
                        return [
                            Forms\Components\Placeholder::make('no_metrics')
                                ->content('No metrics found for this ship.'),
                        ];
                    }
                    return $fields;
                }
            )->reactive(),
        ];
    }

    // --- Form Submission Logic ---

    public function submit()
    {
        // dd('ship_id', [$this->ship_id]);
        // dd('form state before getState', $this->form->getRawState());
        // dd("in sub,mit");
        $data = $this->form->getState();
        $userId = auth()->id();
        // dd($data['metrics'],$data);
        if (empty($data['metrics'] ?? [])) {
            Notification::make()->danger()->title('No metrics filled.')->send();
            return;
        }

        DB::transaction(function () use ($data, $userId) {
            // dd($data['metrics'],$data);
            foreach ($data['metrics'] as $key => $values) {
                // $key = "{shipAssetId}_{metricId}"
                [$shipAssetId, $metricId] = explode('_', $key);
                $currentValue = $values['current_value'] ?? null;
                $addition = $values['addition'] ?? null;

                if ($currentValue === null) continue;

                // Create DailyAssetLog for this asset for today
               $log = DailyAssetLog::where('ship_asset_id', $shipAssetId)
                    ->whereDate('log_time', now()->toDateString())
                    ->first();

                if (!$log) {
                    $log = DailyAssetLog::create([
                        'ship_asset_id' => $shipAssetId,
                        'log_time'      => now(),
                        'recorded_by'   => $userId,
                    ]);
                }
                // Save AssetLogMetric
                AssetLogMetric::create([
                    'daily_asset_log_id'   => $log->id,
                    'ship_asset_metric_id' => $metricId,
                    'value'                => $currentValue,
                ]);

                // Log addition as asset_transaction (if entered)
                if ($addition && $addition > 0) {
                    $transactionType = MasterEntry::whereHas('type', fn($q) => $q->where('name', 'asset_transaction_type'))
                        ->where('code', 'addition')
                        ->first();
                    if ($transactionType) {
                        AssetTransaction::create([
                            'ship_asset_id'       => $shipAssetId,
                            'transaction_type_id' => $transactionType->id,
                            'quantity'            => $addition,
                            'new_level'           => $currentValue,
                            'recorded_by'         => $userId,
                            'transaction_time'    => now(),
                            'notes'               => 'Auto by ROB Entry',
                        ]);
                    }
                }
            }
        });

        Notification::make()
            ->success()
            ->title('ROB Saved')
            ->body('Your ROB log has been saved successfully.')
            ->send();

        $this->form->fill([]);
    }

    // For the Save button (you can use the Blade button or Filament actions)
    protected function getFormActions(): array
    {
        return [
            Forms\Components\Actions\Action::make('Save Log')
                ->submit('submit')
                ->label('Save Log')
        ];
    }
}
