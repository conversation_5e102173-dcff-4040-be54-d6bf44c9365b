<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;
use Filament\Forms;
use App\Models\Ship;
use App\Models\ShipMark;
use App\Models\ShipCrewPositionRequirement;
use App\Models\Crew;
use App\Models\CrewAssignment;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\DB;

class ShipMarkAssignmentSimulation extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-users';
    protected static ?string $navigationLabel = 'Crew Assignment Simulation';
    protected static string $view = 'filament.pages.ship-mark-assignment-simulation';

    public $ship_id;
    public $ship_mark_id;
    public $positions = [];        // Array of positions
    public $assignments = [];      // [positionId][slot] = crewId
    public $toOffboard = [];       // List of ineligible crew
    public $suggestions = [];      // [positionId][crew]

    // --------- Mount ----------
    public function mount()
    {
        $this->form->fill(['ship_id' => null, 'ship_mark_id' => null]);
    }

    // --------- Filament Form Schema ----------
    protected function getFormSchema(): array
    {
        return [
            Forms\Components\Section::make('Context')
                ->columns(3)
                ->schema([
                    Forms\Components\Select::make('ship_id')
                        ->label('Ship')
                        ->options(Ship::pluck('name', 'id'))
                        ->reactive()
                        ->afterStateUpdated(fn() => $this->resetContext())
                        ->required(),
                    Forms\Components\Select::make('ship_mark_id')
                        ->label('Ship Mark')
                        ->options(fn($get) => $get('ship_id') ? ShipMark::pluck('name', 'id') : [])
                        ->reactive()
                        ->afterStateUpdated(fn() => $this->simulate())
                        ->required(),
                ]),
            Forms\Components\Group::make()
                ->columns(3)
                ->schema(fn() => $this->renderPositionSections())
                ->visible(fn() => count($this->positions) > 0),
            // Offboard warning at top
            Forms\Components\Section::make()
                ->schema([
                    Forms\Components\Placeholder::make('offboard_warning')
                        ->content(
                            fn() =>
                            count($this->toOffboard)
                                ? '<div class="bg-red-50 border-l-4 border-red-500 text-red-900 p-3 mb-4">
                                    <strong>Crew to Offboard:</strong>
                                    <ul class="list-disc ml-5">' .
                                collect($this->toOffboard)->map(
                                    fn($c) =>
                                    "<li>{$c->display_name} ({$c->unique_crew_id})</li>"
                                )->implode('') .
                                '</ul>
                                </div>'
                                : ''
                        )

                ])
                ->visible(fn() => count($this->toOffboard) > 0),
        ];
    }

    // --------- Render Dynamic Position Sections ----------
    protected function renderPositionSections(): array
    {
        return array_map(function ($pos) {
            $slots = [];
            $anySlotNoOptions = false;
            $allSlotsAssigned = true;
            $anyAssigned = false;

            $assignedSlots = $this->assignments[$pos['id']] ?? [];

            for ($i = 0; $i < $pos['min_required']; $i++) {
                $options = $this->getSlotOptions($pos['id'], $i);

                // If any slot has no options, red border later
                if (count($options) === 0) {
                    $anySlotNoOptions = true;
                }
                // If any slot not assigned, not all assigned
                if (empty($assignedSlots[$i])) {
                    $allSlotsAssigned = false;
                } else {
                    $anyAssigned = true;
                }

                $slots[] = Forms\Components\Select::make("assignments.{$pos['id']}.{$i}")
                    ->label("Slot " . ($i + 1))
                    ->options($options)
                    ->disabled(count($options) === 0)
                    ->required()
                    ->distinct()
                    ->reactive()
                    ->placeholder('-- Select Crew --')
                    ->hint(count($options) === 0 ? 'No crew available' : '');
            }

            // Certificate logic summary
            $summary = '';
            foreach ($pos['groups'] as $group) {
                $summary .= "<div class='mb-2'><b>{$group['name']}</b><br>";
                foreach ($group['sets'] as $set) {
                    $setLogic = $set['logic_type'] === 'ALL'
                        ? 'All'
                        : "Any {$set['min_required']}";
                    $certNames = implode(', ', $set['certificates']);
                    $summary .= "<span class='inline-block bg-primary-100 text-primary-800 rounded px-2 py-0.5 text-xs mr-1 mb-1'>{$set['name']}: <b>{$setLogic}</b> [{$certNames}]</span><br>";
                }
                $summary .= "</div>";
            }

            // Dynamic color logic
            $borderClass = '';
            if ($anySlotNoOptions) {
                $borderClass = 'border border-red-500 bg-red-50';
            } elseif ($allSlotsAssigned && $anyAssigned) {
                $borderClass = 'border border-green-500 bg-green-50';
            }

            return Forms\Components\Section::make($pos['designation'])
                ->description(fn() => new \Illuminate\Support\HtmlString($summary))
                ->schema($slots)
                ->columns($pos['min_required'])
                ->extraAttributes(['class' => $borderClass]);
        }, $this->positions ?? []);
    }

    // --------- Get Eligible Crew Options for a Slot ----------
    protected function getSlotOptions_old($posId, $slotIndex): array
    {
        $selectedOther = collect($this->assignments[$posId] ?? [])
            ->except([$slotIndex])
            ->filter()
            ->values()
            ->toArray();

        return collect($this->suggestions[$posId] ?? [])
            ->reject(fn($c) => in_array($c->id, $selectedOther))
            ->mapWithKeys(fn($c) => [$c->id => "{$c->display_name} ({$c->unique_crew_id})"])
            ->toArray();
    }

    protected function getSlotOptions($currentPosId, $currentSlotIndex): array
    {
        // Gather all assigned crew IDs except the current slot
        $selectedCrewIds = [];
        foreach ($this->assignments as $posId => $slots) {
            foreach ($slots as $slotIndex => $crewId) {
                if (
                    !is_null($crewId) &&
                    !(($posId == $currentPosId) && ($slotIndex == $currentSlotIndex))
                ) {
                    $selectedCrewIds[] = $crewId;
                }
            }
        }

        // Only show crew not already assigned anywhere else
        return collect($this->suggestions[$currentPosId] ?? [])
            ->reject(fn($c) => in_array($c->id, $selectedCrewIds))
            ->mapWithKeys(fn($c) => [$c->id => "{$c->display_name} ({$c->unique_crew_id})"])
            ->toArray();
    }

    // --------- Main Simulation Logic ----------
    public function simulate()
    {
        $this->positions = [];
        $this->assignments = [];
        $this->toOffboard = [];
        $this->suggestions = [];

        if (!$this->ship_id || !$this->ship_mark_id) return;

        $reqs = ShipCrewPositionRequirement::with([
            'designation',
            'requirementSetGroups.sets.certificates.certificateType',
        ])
            ->where(['ship_id' => $this->ship_id, 'ship_mark_id' => $this->ship_mark_id])
            ->get();

        $current = CrewAssignment::with('crew')
            ->where(['ship_id' => $this->ship_id, 'ship_mark_id' => $this->ship_mark_id])
            ->get();

        $allEligible = Crew::with('certificates.certificateType')->get();

        // Build positions with certificate group/set info
        $this->positions = $reqs->map(function ($pos) {
            $groups = $pos->requirementSetGroups->map(function ($group) {
                return [
                    'name' => $group->name,
                    'sets' => $group->sets->map(function ($set) {
                        return [
                            'name' => $set->name,
                            'logic_type' => $set->logic_type,
                            'min_required' => $set->min_required,
                            'certificates' => $set->certificates->map(fn($cert) => $cert->certificateType->name)->toArray(),
                        ];
                    })->toArray(),
                ];
            })->toArray();
            return [
                'id' => $pos->id,
                'designation' => $pos->designation->name,
                'min_required' => $pos->min_required,
                'max_allowed' => $pos->max_allowed,
                'groups' => $groups,
                'satisfied' => false,
            ];
        })->toArray();

        // Fill $this->suggestions and mark satisfied
        foreach ($reqs as $pos) {
            $eligible = $this->suggestCrewFor($pos);
            $this->suggestions[$pos->id] = $eligible;
            $this->positions = array_map(
                fn($x) => $x['id'] == $pos->id
                    ? array_merge($x, ['satisfied' => count($eligible) >= $x['min_required']])
                    : $x,
                $this->positions
            );
            // Assign already assigned crew
            $currentAssigned = $current->firstWhere('ship_crew_position_requirement_id', $pos->id);
            if ($currentAssigned) {
                foreach (range(0, $pos->min_required - 1) as $slot) {
                    $this->assignments[$pos->id][$slot] = $currentAssigned->crew->id;
                }
            }
        }

        // Offboard: crew assigned but not eligible anywhere
        $current->each(function ($ca) {
            if (!collect($this->suggestions)
                ->flatten()
                ->pluck('id')
                ->contains($ca->crew_id)) {
                $this->toOffboard[] = $ca->crew;
            }
        });
    }

    // --------- Suggest Crew for a Position ---------
    protected function suggestCrewFor($pos)
    {
        $crew = Crew::with('certificates.certificateType')->get();
        return $crew->filter(function ($c) use ($pos) {
            // Satisfy ANY group with at least one set satisfied
            return $pos->requirementSetGroups->first(
                fn($g) =>
                $g->sets->contains(fn($set) => $this->checkSet($c, $set))
            );
        });
    }

    // --------- Check if Crew Satisfies a Set ---------
    protected function checkSet($crew, $set): bool
    {
        $have = $crew->certificates->pluck('certificateType.id')->toArray();
        $req = $set->certificates->pluck('certificateType.id')->toArray();
        if ($set->logic_type === 'ALL') {
            return empty(array_diff($req, $have));
        }
        return count(array_intersect($req, $have)) >= ($set->min_required ?: 1);
    }

    // --------- Reset All ---------
    public function resetContext()
    {
        $this->ship_mark_id = null;
        $this->positions = [];
        $this->assignments = [];
        $this->toOffboard = [];
        $this->suggestions = [];
        $this->form->fill(['ship_mark_id' => null]);
    }

    // --------- Save Logic ---------
    public function save()
    {
        // ✅ Validate: Ensure all required slots are filled
        foreach ($this->positions as $pos) {
            $slots = $this->assignments[$pos['id']] ?? [];
            if (count(array_filter($slots)) < $pos['min_required']) {
                Notification::make()
                    ->title("Fill all slots for {$pos['designation']}")
                    ->danger()
                    ->send();
                return;
            }
        }

        DB::transaction(function () {
            // ✅ Step 1: Collect all crew_ids being newly assigned
            $newAssignments = collect($this->assignments)
                ->flatten()
                ->unique()
                ->filter()
                ->values()
                ->toArray();

            // ✅ Step 2: Relieve all existing assignments for this ship/mark that are NOT in new list
            CrewAssignment::where('ship_id', $this->ship_id)
                ->where('ship_mark_id', $this->ship_mark_id)
                ->whereNull('relieved_at')
                ->whereNotIn('crew_id', $newAssignments)
                ->update([
                    'relieved_at' => now(),
                    'status' => 'relieved',
                ]);

            // ✅ Step 3: Create fresh assignments
            foreach ($this->assignments as $posId => $slots) {
                foreach ($slots as $crewId) {
                    CrewAssignment::create([
                        'ship_id' => $this->ship_id,
                        'ship_mark_id' => $this->ship_mark_id,
                        'ship_crew_position_requirement_id' => $posId,
                        'crew_id' => $crewId,
                        'status' => 'assigned',
                        'assigned_at' => now(),
                    ]);
                }
            }
        });

        Notification::make()
            ->title('Crew assignments saved!')
            ->success()
            ->send();

        $this->simulate();
    }
}
