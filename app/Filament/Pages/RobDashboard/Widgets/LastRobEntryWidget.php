<?php

namespace App\Filament\Pages\RobDashboard\Widgets;

use Filament\Widgets\TableWidget;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\Relation;
use App\Models\AssetLogMetric;
use App\Models\AssetTransaction;
use Filament\Tables;
class LastRobEntryWidget extends TableWidget
{
    public ?int $shipId = null;
    protected static ?string $heading = 'Last ROB Entries';
    protected int | string | array $columnSpan = 'full';

    protected function getTableQuery(): Builder|Relation|null
    {
        // Get latest AssetLogMetric per metric for is_rob=true and selected ship
        return AssetLogMetric::query()
            ->whereHas('shipAssetMetric', function ($q) {
                $q->where('is_rob', true);
                if ($this->shipId) {
                    $q->whereHas('shipAsset', fn($q2) => $q2->where('ship_id', $this->shipId));
                }
            })
            ->whereIn('id', function ($q) {
                $q->selectRaw('MAX(id)')
                    ->from('asset_log_metrics')
                    ->groupBy('ship_asset_metric_id');
            })
            ->with([
                'shipAssetMetric.metricType',
                'shipAssetMetric.shipAsset.asset.type',
                'dailyAssetLog.recorder',
            ]);
    }

    protected function getTableColumns(): array
    {
        return [
            Tables\Columns\TextColumn::make('shipAssetMetric.shipAsset.asset.name')->label('Asset')->sortable(),
            Tables\Columns\TextColumn::make('shipAssetMetric.metricType.name')->label('Metric')->sortable(),
            Tables\Columns\TextColumn::make('opening')
                ->label('Opening')
                ->getStateUsing(function ($record) {
                    $prev = AssetLogMetric::where('ship_asset_metric_id', $record->ship_asset_metric_id)
                        ->where('id', '<', $record->id)
                        ->latest('created_at')
                        ->first();
                    return $prev?->value ?? 0;
                })
                ->badge()->color('gray'),
            Tables\Columns\TextColumn::make('addition')
                ->label('Addition')
                ->getStateUsing(function ($record) {
                    $prev = AssetLogMetric::where('ship_asset_metric_id', $record->ship_asset_metric_id)
                        ->where('id', '<', $record->id)
                        ->latest('created_at')
                        ->first();
                    if (!$prev) return 0;
                    return AssetTransaction::where('ship_asset_id', $record->shipAssetMetric->ship_asset_id)
                        ->whereHas('transactionType', fn($q) => $q->whereIn('code', ['addition', 'refill']))
                        ->whereBetween('created_at', [$prev->created_at, $record->created_at])
                        ->sum('quantity');
                })
                ->badge()->color('success'),
            Tables\Columns\TextColumn::make('value')->label('Closing')->badge()->color('primary'),
            Tables\Columns\TextColumn::make('consumption')
                ->label('Consumption')
                ->getStateUsing(function ($record) {
                    $prev = AssetLogMetric::where('ship_asset_metric_id', $record->ship_asset_metric_id)
                        ->where('id', '<', $record->id)
                        ->latest('created_at')
                        ->first();
                    $opening = $prev?->value ?? 0;
                    $closing = $record->value;
                    $addition = AssetTransaction::where('ship_asset_id', $record->shipAssetMetric->ship_asset_id)
                        ->whereHas('transactionType', fn($q) => $q->whereIn('code', ['addition', 'refill']))
                        ->whereBetween('created_at', [$prev?->created_at ?? now()->subYears(10), $record->created_at])
                        ->sum('quantity');
                    return $opening + $addition - $closing;
                })
                ->badge()->color('danger'),
            Tables\Columns\TextColumn::make('unit')->label('Unit'),
            Tables\Columns\TextColumn::make('dailyAssetLog.recorder.name')->label('User')->searchable(),
            Tables\Columns\TextColumn::make('created_at')->dateTime()->label('Logged At')->sortable(),
        ];
    }
}
