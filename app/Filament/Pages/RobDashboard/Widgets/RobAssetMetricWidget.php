<?php

namespace App\Filament\Pages\RobDashboard\Widgets;

use Filament\Widgets\Widget;
use App\Models\ShipAssetMetric;
use App\Models\AssetLogMetric;
use App\Models\AssetTransaction;

class RobAssetMetricWidget extends Widget
{
    protected static string $view = 'filament.pages.rob-dashboard.widgets.rob-asset-metric-widget';

      protected int | string | array $columnSpan = 'full';

    public $shipId;
    protected static ?string $heading = 'ROB Metrics';

    public function getMetrics_old()
    {
        // Get all ship asset metrics with is_rob = true for this ship
        $metrics = ShipAssetMetric::whereHas('shipAsset', fn($q) => $q->where('ship_id', $this->shipId))
            ->where('is_rob', true)
            ->with(['shipAsset.asset', 'metricType'])
            ->get();

        return $metrics->map(function ($metric) {
            $latest = AssetLogMetric::where('ship_asset_metric_id', $metric->id)
                ->latest('created_at')->first();

            $prev = AssetLogMetric::where('ship_asset_metric_id', $metric->id)
                ->where('id', '<', $latest?->id)
                ->latest('created_at')->first();

            // $lastTxCount = AssetTransaction::where('ship_asset_id', $metric->ship_asset_id)->where('metric_type_id', $metric->id)
            //     ->latest('created_at')->count();
            $lastTx = AssetTransaction::where('ship_asset_id', $metric->ship_asset_id)->where('metric_type_id', $metric->id)
                ->latest('created_at')->first();

            $needsSync = $lastTx && $latest && $lastTx->created_at > $latest->created_at;

            return [
                'asset'        => $metric->shipAsset->asset->name ?? '',
                'metric'       => $metric->metricType->name ?? '',
                'unit'         => $metric->unit ?? '',
                'current'      => $latest?->value,
                'last_reading' => $latest?->created_at,
                'current_level'=> $metric->shipAsset->current_level,
                'current_level_time' => $metric->shipAsset->updated_at,
                'synced'       => !$needsSync,
                'sync_message' => $needsSync ? 'Last reading : ' . $latest?->value . ' at ' . $latest?->created_at : 'Up to date',
            ];
        });
    }

    public function getMetrics()
{
    $metrics = ShipAssetMetric::whereHas('shipAsset', fn($q) => $q->where('ship_id', $this->shipId))
        ->where('is_rob', true)
        ->with(['shipAsset.asset', 'metricType', 'shipAsset'])
        ->get();

    return $metrics->map(function ($metric) {
        $latest = AssetLogMetric::where('ship_asset_metric_id', $metric->id)
            ->latest('created_at')->first();

        $prev = AssetLogMetric::where('ship_asset_metric_id', $metric->id)
            ->where('id', '<', $latest?->id)
            ->latest('created_at')->first();

        $lastTx = AssetTransaction::where('ship_asset_id', $metric->ship_asset_id)
            ->where('metric_type_id', $metric->id)
            ->latest('created_at')->first();

        // Is there a more recent transaction than last log? (needs sync)
        $needsSync = $lastTx && $latest && $lastTx->created_at > $latest->created_at;

        // "Current Level": show from latest log if exists, else fallback to ship asset
        $currentValue =   $needsSync ? $metric->shipAsset->current_level : $latest?->value ;

        return [
            'asset'          => $metric->shipAsset->asset->name ?? '',
            'metric'         => $metric->metricType->name ?? '',
            'unit'           => $metric->unit ?? '',
            'current_level'  => $currentValue, // Always show latest value!
            'current'        => $latest?->value, // For log detail
            'last_log_value' => $prev?->value ?? '-', // Opening or previous value if you want to show in UI
            'last_reading'   => $latest?->created_at,
            'current_level_time' => $latest?->created_at ?? $metric->shipAsset->updated_at,
            'synced'         => !$needsSync,
            'sync_message' => $needsSync ? 'Last reading : ' . $latest?->value . ' at ' . $latest?->created_at : 'Up to date',
        ];
    });
}

}
