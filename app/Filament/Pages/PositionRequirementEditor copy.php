<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;
use Filament\Forms;
use App\Models\Ship;
use App\Models\ShipMark;
use App\Models\Designation;
use App\Models\ShipCrewPositionRequirement;
use App\Models\RequirementSetGroup;
use App\Models\CrewCertificateRequirementSet;
use App\Models\CrewCertificateRequirement;
use App\Models\CrewCertificateType;
use Illuminate\Support\Collection;
use Filament\Notifications\Notification;

class PositionRequirementEditor extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-wrench-screwdriver';
    protected static ?string $navigationLabel = 'Position Requirements';
    protected static ?string $title = 'Edit Position Requirements';
    protected static string $view = 'filament.pages.position-requirement-editor';

    // State
    public $ship_id = null;
    public $ship_mark_id = null;
    public $designation_id = null;
    public $position = null;

    public $groups = [];

    public $newCertificateTypeId = [];

    public function mount()
    {
        // Defaults (can preselect first ship, mark, designation if you want)
    }

    // Filament Form for selecting position
    protected function getFormSchema(): array
    {
        return [
            Forms\Components\Select::make('ship_id')
                ->label('Ship')
                ->options(Ship::pluck('name', 'id'))
                ->searchable()
                ->required()
                ->reactive()
                ->afterStateUpdated(fn($state, $livewire) => $livewire->resetShipMark()),
            Forms\Components\Select::make('ship_mark_id')
                ->label('Ship Mark')
                ->options(fn(callable $get) => $get('ship_id') ? ShipMark::pluck('name', 'id') : [])
                ->required()
                ->reactive()
                ->afterStateUpdated(fn($state, $livewire) => $livewire->resetDesignation()),
            Forms\Components\Select::make('designation_id')
                ->label('Designation')
                ->options(fn(callable $get) => $get('ship_id') ? Designation::pluck('name', 'id') : [])
                ->required()
                ->reactive()
                ->afterStateUpdated(fn($state, $livewire) => $livewire->loadPositionData()),
        ];
    }

    public function resetShipMark()
    {
        $this->form->fill(['ship_mark_id' => null, 'designation_id' => null]);
        $this->ship_mark_id = null;
        $this->designation_id = null;
        $this->groups = [];
        $this->position = null;
    }

    public function resetDesignation()
    {
        $this->form->fill(['designation_id' => null]);
        $this->groups = [];
    }

    public function loadPositionData()
    {
        $data = $this->form->getState();
        $shipId = $data['ship_id'] ?? null;
        $shipMarkId = $data['ship_mark_id'] ?? null;
        $designationId = $data['designation_id'] ?? null;

        $this->groups = [];
        $this->position = null;
        if (!$shipId || !$shipMarkId || !$designationId) return;

        $position = ShipCrewPositionRequirement::where([
            'ship_id' => $shipId,
            'ship_mark_id' => $shipMarkId,
            'designation_id' => $designationId,
        ])->first();

        $this->position = $position;
        if (!$position) return;

        // Load groups for this position
        $assignments = $position->requirementSetGroupAssignments()->with([
            'group.sets.requirements.certificateType'
        ])->get();

        $this->groups = $assignments->map(function ($asg) {
            $group = $asg->group;
            return [
                'id' => $group->id,
                'name' => $group->name,
                'description' => $group->description,
                'sets' => $group->sets->map(function ($set) {
                    return [
                        'id' => $set->id,
                        'name' => $set->name,
                        'logic_type' => $set->logic_type,
                        'min_required' => $set->min_required,
                        'certificates' => $set->requirements->map(fn($req) => [
                            'id' => $req->id,
                            'certificate_type_id' => $req->certificate_type_id,
                            'certificate_name' => $req->certificateType->name ?? 'Unknown',
                            'is_mandatory' => $req->is_mandatory,
                        ])->toArray(),
                    ];
                })->toArray(),
            ];
        })->toArray();
    }

    // Save Set Name
    public function saveSetName($setId, $name)
    {
        $set = \App\Models\CrewCertificateRequirementSet::find($setId);
        if ($set) {
            $set->name = $name;
            $set->save();
            $this->loadPositionData();
        }
    }

    // Save Logic Type
    public function saveSetLogicType($setId, $logicType)
    {
        $set = \App\Models\CrewCertificateRequirementSet::find($setId);
        if ($set && in_array($logicType, ['ALL', 'ANY'])) {
            $set->logic_type = $logicType;
            if ($logicType === 'ALL') {
                $set->min_required = null;
            }
            $set->save();
            $this->loadPositionData();
        }
    }

    // Save Min Required
    public function saveSetMinRequired($setId, $minRequired)
    {
        $set = \App\Models\CrewCertificateRequirementSet::find($setId);
        if ($set && $set->logic_type === 'ANY') {
            $set->min_required = intval($minRequired) > 0 ? intval($minRequired) : null;
            $set->save();
            $this->loadPositionData();
        }
    }


    // Add, edit, delete logic for groups/sets/certs here, call via actions in blade with Livewire wire:click

    public function addRequirementGroup($name = 'New Group')
    {
        if (!$this->position) return;
        $group = RequirementSetGroup::create(['name' => $name]);
        // Attach to position
        $this->position->requirementSetGroupAssignments()->create([
            'requirement_set_group_id' => $group->id,
        ]);
        $this->loadPositionData();
    }

    public function addSetToGroup($groupId, $name = 'New Set')
    {
        $group = RequirementSetGroup::find($groupId);
        if (!$group) return;
        $set = CrewCertificateRequirementSet::create([
            'name' => $name,
            'logic_type' => 'ALL', // default, let user edit
        ]);
        $group->sets()->attach($set->id);
        $this->loadPositionData();
    }


    public function saveGroupName($groupId, $name)
    {
        $group = \App\Models\RequirementSetGroup::find($groupId);
        if ($group) {
            $group->name = $name;
            $group->save();
            $this->loadPositionData(); // Refresh as needed
        }
    }
    public function addCertificateToSet($setId)
    {
        $certificateTypeId = $this->newCertificateTypeId[$setId] ?? null;
        if (!$certificateTypeId) return;

        // Add logic to insert the certificate to the set...
        CrewCertificateRequirement::firstOrCreate([
            'requirement_set_id' => $setId,
            'certificate_type_id' => $certificateTypeId,
        ], [
            // add any other fields here if needed
        ]);

        // Reset dropdown after adding
        $this->newCertificateTypeId[$setId] = null;

        $this->loadPositionData(); // Refresh UI if needed
    }

    public function deleteRequirementGroup($groupId)
    {
        RequirementSetGroup::find($groupId)?->delete();
        $this->loadPositionData();
    }

    public function deleteSet($setId)
    {
        CrewCertificateRequirementSet::find($setId)?->delete();
        $this->loadPositionData();
    }

    public function deleteCertificate($certReqId)
    {
        CrewCertificateRequirement::find($certReqId)?->delete();
        $this->loadPositionData();
    }

    // ... add other update actions as needed

    protected function getViewData(): array
    {
        return [
            'form' => $this->form,
            'groups' => $this->groups,
            'position' => $this->position,
            'allCertificateTypes' => CrewCertificateType::pluck('name', 'id')->toArray(),
        ];
    }
}
