<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;
use App\Models\Ship;
use App\Models\AssetLogMetric;
use Filament\Tables;
use Filament\Tables\Table;

use App\Filament\Pages\RobDashboard\Widgets\RobAssetMetricWidget;
use App\Filament\Pages\RobDashboard\Widgets\LastRobEntryWidget;


use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Concerns\InteractsWithForms;

class RobDashboard extends Page implements HasForms, Tables\Contracts\HasTable
{
    use InteractsWithForms, Tables\Concerns\InteractsWithTable;

    // ...rest of your code


    protected static ?string $navigationIcon = 'heroicon-o-chart-bar';
    protected static ?string $navigationGroup = 'Logs & Readings';
    protected static ?string $title = 'ROB Dashboard';
    protected static string $view = 'filament.pages.rob-dashboard';

    public ?int $ship_id = 1;

    protected function getFormSchema(): array
    {
        return [
            \Filament\Forms\Components\Select::make('ship_id')
                ->label('Ship')
                ->options(Ship::pluck('name', 'id'))
                ->searchable()
                ->required()
                ->reactive(),
        ];

    }

    public function getHeaderWidgetsColumns(): int
    {
        return 3; // Forces all widgets to span the full width
    }
    protected function getHeaderWidgets(): array
    {
        return [
           RobAssetMetricWidget::make([
                'shipId' => $this->ship_id,
            ]),
            LastRobEntryWidget::make([
                'shipId' => $this->ship_id,
            ]),
        ];


    }


    public function table(Table $table): Table
    {
        return $table
            ->query(
                AssetLogMetric::query()
                    ->whereHas('shipAssetMetric.shipAsset', fn($q) => $this->ship_id ? $q->where('ship_id', $this->ship_id) : $q)
                    ->with(['dailyAssetLog.shipAsset.asset', 'metricType', 'dailyAssetLog.recordedBy'])
            )
            ->columns([
                Tables\Columns\TextColumn::make('dailyAssetLog.shipAsset.asset.name')->label('Asset')->sortable(),
                Tables\Columns\TextColumn::make('metricType.name')->label('Metric')->sortable(),
                Tables\Columns\TextColumn::make('value')->label('Value'),
                Tables\Columns\TextColumn::make('unit')->label('Unit'),
                Tables\Columns\TextColumn::make('dailyAssetLog.recordedBy.name')->label('User'),
                Tables\Columns\TextColumn::make('created_at')->dateTime()->label('Logged At')->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('ship_id')
                    ->label('Ship')
                    ->options(Ship::pluck('name', 'id'))
                    ->default($this->ship_id)
                    ->query(function ($query, $value) {
                        if ($value) {
                            $query->whereHas('shipAssetMetric.shipAsset', fn($q) => $q->where('ship_id', $value));
                        }
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->modalHeading('ROB Log Details')
                    ->modalContent(fn($record) => view('filament.pages.partials.rob-log-details', ['record' => $record])),
            ])
            ->defaultSort('created_at', 'desc');
    }
}
