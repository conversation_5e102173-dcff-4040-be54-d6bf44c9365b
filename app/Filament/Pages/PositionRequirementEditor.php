<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;
use Filament\Forms;
use App\Models\Ship;
use App\Models\ShipMark;
use App\Models\Designation;
use App\Models\ShipCrewPositionRequirement;
use App\Models\CrewCertificateRequirementSet;
use App\Models\CrewCertificateRequirement;
use App\Models\CrewCertificateType;
use App\Models\RequirementSetGroup;
use App\Models\RequirementSetGroupSet;
use App\Models\PositionRequirementSetGroupAssignment;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Section;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\DB;

class PositionRequirementEditor extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-user-group';
    protected static ?string $navigationLabel = 'Crew Certificate Assignment';
    protected static string $view = 'filament.pages.position-requirement-editor';

    // State
    public $ship_id = null;
    public $ship_mark_id = null;
    public $designation_id = null;
    public $groups = [];

    public function mount()
    {
        $this->form->fill([
            'ship_id' => $this->ship_id,
            'ship_mark_id' => $this->ship_mark_id,
            'designation_id' => $this->designation_id,
            'groups' => [],
        ]);
    }

    public function resetMarkDesignation()
    {
        $this->ship_mark_id = null;
        $this->designation_id = null;
        $this->groups = [];
        $this->form->fill([
            'ship_mark_id' => null,
            'designation_id' => null,
            'groups' => [],
        ]);
    }

    public function resetDesignation()
    {
        $this->designation_id = null;
        $this->groups = [];
        $this->form->fill([
            'designation_id' => null,
            'groups' => [],
        ]);
    }

    public function designationChanged()
    {
        $this->fillForm();
    }

    public function fillForm()
    {
        $this->groups = [];

        if ($this->ship_id && $this->ship_mark_id && $this->designation_id) {
            $position = ShipCrewPositionRequirement::where([
                'ship_id' => $this->ship_id,
                'ship_mark_id' => $this->ship_mark_id,
                'designation_id' => $this->designation_id,
            ])->first();

            if ($position) {
                foreach ($position->requirementSetGroups as $group) {
                    $sets = [];
                    foreach ($group->sets as $set) {
                        $certificates = $set->certificates->map(function ($cert) {
                            return [
                                'certificate_type_id' => $cert->certificate_type_id,
                            ];
                        })->toArray();

                        $sets[] = [
                            'set_id'      => $set->id,
                            'name'        => $set->name,
                            'logic_type'  => $set->logic_type,
                            'min_required'=> $set->min_required,
                            'certificates'=> $certificates,
                        ];
                    }
                    $this->groups[] = [
                        'group_id'   => $group->id,
                        'name'       => $group->name,
                        'description'=> $group->description,
                        'sets'       => $sets,
                    ];
                }
            }
        }

        $this->form->fill([
            'groups' => $this->groups,
        ]);
    }

    protected function getFormSchema(): array
    {
        return [
            Section::make('Select Ship, Mark, Designation')
                ->columns(3)
                ->schema([
                    Select::make('ship_id')
                        ->label('Ship')
                        ->options(Ship::pluck('name', 'id'))
                        ->reactive()
                        ->afterStateUpdated(fn ($state, $livewire) => $livewire->resetMarkDesignation())
                        ->required(),

                    Select::make('ship_mark_id')
                        ->label('Ship Mark')
                        ->options(fn ($get) =>
                            $get('ship_id')
                                ? ShipMark::pluck('name', 'id')
                                : []
                        )
                        ->reactive()
                        ->afterStateUpdated(fn ($state, $livewire) => $livewire->resetDesignation())
                        ->required(),

                    Select::make('designation_id')
                        ->label('Designation')
                        ->options(fn ($get) =>
                            $get('ship_id') && $get('ship_mark_id')
                                ? Designation::pluck('name', 'id')
                                : []
                        )
                        ->required()
                        ->reactive()
                        ->afterStateUpdated(function ($state, $livewire) {
                            $livewire->designationChanged();
                        }),
                ]),

            Section::make('Requirement Groups & Sets')
                ->columns(1)
                ->visible(fn ($get) => $get('ship_id') && $get('ship_mark_id') && $get('designation_id'))
                ->schema([
                    Repeater::make('groups')
                        ->label('Requirement Groups')
                        ->schema([
                            TextInput::make('name')
                                ->label('Group Name')
                                ->required(),

                            Textarea::make('description')
                                ->label('Group Description'),

                            Repeater::make('sets')
                                ->label('Sets (AND inside group)')
                                ->schema([
                                    TextInput::make('name')
                                        ->label('Set Name')
                                        ->required(),

                                    Select::make('logic_type')
                                        ->label('Logic')
                                        ->options(['ALL' => 'All certificates', 'ANY' => 'Any N certificates'])
                                        ->required()
                                        ->reactive(),

                                    TextInput::make('min_required')
                                        ->label('Min Required (if ANY)')
                                        ->numeric()
                                        ->visible(fn ($get) => $get('logic_type') === 'ANY'),

                                    Repeater::make('certificates')
                                        ->label('Certificates')
                                        ->schema([
                                            Select::make('certificate_type_id')
                                                ->label('Certificate')
                                                ->options(function (callable $get, $state, $context) {
                                                    $all = CrewCertificateType::pluck('name', 'id')->toArray();
                                                    $certificates = $get('../certificates') ?? [];
                                                    $currentIndex = $get('__index');

                                                    // Remove selected certificates (except current)
                                                    $selected = [];
                                                    foreach ($certificates as $idx => $row) {
                                                        if ($idx != $currentIndex && isset($row['certificate_type_id'])) {
                                                            $selected[] = $row['certificate_type_id'];
                                                        }
                                                    }
                                                    return collect($all)->except($selected);
                                                })
                                                ->searchable()
                                                ->required(),
                                        ])
                                        ->addActionLabel('Add Certificate')
                                        ->collapsible(),
                                ])
                                ->addActionLabel('Add Set')
                                ->collapsible(),
                        ])
                        ->addActionLabel('Add Group')
                        ->collapsible()
                        ->default([]),
                ]),
        ];
    }

    public function save()
    {
        $data = $this->form->getState();

        $ship_id = $data['ship_id'];
        $ship_mark_id = $data['ship_mark_id'];
        $designation_id = $data['designation_id'];
        $groupsInput = $data['groups'] ?? [];

        DB::transaction(function () use ($ship_id, $ship_mark_id, $designation_id, $groupsInput) {
            // 1. Find or create the position
            $position = ShipCrewPositionRequirement::firstOrCreate([
                'ship_id' => $ship_id,
                'ship_mark_id' => $ship_mark_id,
                'designation_id' => $designation_id,
            ]);

            // 2. Delete previous assignments for this position
            PositionRequirementSetGroupAssignment::where('ship_crew_position_requirement_id', $position->id)->delete();

            // 3. For each group in input, create or update
            foreach ($groupsInput as $groupData) {
                $group = RequirementSetGroup::firstOrCreate(
                    ['name' => $groupData['name']],
                    ['description' => $groupData['description'] ?? null]
                );

                PositionRequirementSetGroupAssignment::create([
                    'ship_crew_position_requirement_id' => $position->id,
                    'requirement_set_group_id' => $group->id,
                ]);

                // Remove old sets from group (for this overwrite)
                RequirementSetGroupSet::where('requirement_set_group_id', $group->id)->delete();

                foreach ($groupData['sets'] ?? [] as $setData) {
                    $set = CrewCertificateRequirementSet::firstOrCreate([
                        'name' => $setData['name'],
                        'logic_type' => $setData['logic_type'],
                        'min_required' => $setData['min_required'] ?? null,
                    ]);

                    // Map: group -> set assignment
                    RequirementSetGroupSet::create([
                        'requirement_set_group_id' => $group->id,
                        'requirement_set_id' => $set->id,
                    ]);

                    // Remove old certificates from set (overwrite)
                    CrewCertificateRequirement::where('requirement_set_id', $set->id)->delete();

                    foreach ($setData['certificates'] ?? [] as $cert) {
                        CrewCertificateRequirement::create([
                            'requirement_set_id' => $set->id,
                            'certificate_type_id' => $cert['certificate_type_id'],
                        ]);
                    }
                }
            }
        });

        Notification::make()
            ->title('Requirements updated successfully!')
            ->success()
            ->send();

        $this->fillForm();
    }
}
