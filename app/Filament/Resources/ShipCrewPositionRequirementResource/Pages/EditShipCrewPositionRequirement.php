<?php

namespace App\Filament\Resources\ShipCrewPositionRequirementResource\Pages;

use App\Filament\Resources\ShipCrewPositionRequirementResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditShipCrewPositionRequirement extends EditRecord
{
    protected static string $resource = ShipCrewPositionRequirementResource::class;

    protected function getHeaderActions(): array
    {
        return [
        ];
    }

    //customize redirect after create
    public function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
