<?php

namespace App\Filament\Resources\ShipCrewPositionRequirementResource\Pages;

use App\Filament\Resources\ShipCrewPositionRequirementResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateShipCrewPositionRequirement extends CreateRecord
{
    protected static string $resource = ShipCrewPositionRequirementResource::class;
    protected static bool $canCreateAnother = false;

    //customize redirect after create
    public function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
