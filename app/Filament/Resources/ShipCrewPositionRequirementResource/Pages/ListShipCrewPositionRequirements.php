<?php

namespace App\Filament\Resources\ShipCrewPositionRequirementResource\Pages;

use App\Filament\Resources\ShipCrewPositionRequirementResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListShipCrewPositionRequirements extends ListRecords
{
    protected static string $resource = ShipCrewPositionRequirementResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
