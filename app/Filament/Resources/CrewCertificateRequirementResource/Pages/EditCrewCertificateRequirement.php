<?php

namespace App\Filament\Resources\CrewCertificateRequirementResource\Pages;

use App\Filament\Resources\CrewCertificateRequirementResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditCrewCertificateRequirement extends EditRecord
{
    protected static string $resource = CrewCertificateRequirementResource::class;

    protected function getHeaderActions(): array
    {
        return [
        ];
    }

    //customize redirect after create
    public function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
