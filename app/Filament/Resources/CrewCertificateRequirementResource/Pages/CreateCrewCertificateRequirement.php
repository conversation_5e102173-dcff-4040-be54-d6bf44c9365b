<?php

namespace App\Filament\Resources\CrewCertificateRequirementResource\Pages;

use App\Filament\Resources\CrewCertificateRequirementResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateCrewCertificateRequirement extends CreateRecord
{
    protected static string $resource = CrewCertificateRequirementResource::class;
    protected static bool $canCreateAnother = false;

    //customize redirect after create
    public function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
