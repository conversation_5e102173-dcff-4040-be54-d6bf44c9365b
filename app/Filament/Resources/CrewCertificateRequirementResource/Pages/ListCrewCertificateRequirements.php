<?php

namespace App\Filament\Resources\CrewCertificateRequirementResource\Pages;

use App\Filament\Resources\CrewCertificateRequirementResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListCrewCertificateRequirements extends ListRecords
{
    protected static string $resource = CrewCertificateRequirementResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
