<?php

namespace App\Filament\Resources;

use App\Filament\Resources\AssetResource\Pages;
use App\Models\Asset;
use App\Models\AssetCategory;
use App\Models\AssetSpecKey;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use App\Models\MasterEntry;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class AssetResource extends Resource
{
    protected static ?string $model = Asset::class;

    protected static ?string $navigationIcon = 'heroicon-o-cube';
      protected static ?string $navigationGroup = 'Ship Management';
     protected static ?int $navigationSort = 90;

    public static function form(Form $form): Form
    {
        return $form->schema([
            Forms\Components\TextInput::make('name')
                ->required()
                ->maxLength(255),


                  Forms\Components\Select::make('type_id')
                ->label('Type')
                ->searchable()
                ->options(fn () => MasterEntry::query()
                    ->whereHas('type', fn ($q) => $q->where('name', 'asset_type'))
                    ->pluck('name', 'id'))
                ->required()
                ->reactive(), // needed for conditionally showing options




            Forms\Components\Select::make('category_id')
                ->label('Category')
                ->relationship('category', 'name')
                ->searchable()
                ->nullable(),

            Forms\Components\Toggle::make('is_compliance')->label('Compliance Asset'),

            Forms\Components\Toggle::make('is_fire_safety')->label('Fire Safety'),

            Forms\Components\Toggle::make('survey_required')->label('Survey Required'),

            Forms\Components\TextInput::make('unit')
                ->maxLength(20),

                     Forms\Components\Select::make('consumable_type_id')
                ->label('Consumable Type')
                ->searchable()
                ->options(fn () => MasterEntry::query()
                    ->whereHas('type', fn ($q) => $q->where('name', 'asset_consumable_type'))
                    ->pluck('name', 'id'))
                ->required()
                ->reactive(), // needed for conditionally showing options


            Forms\Components\Repeater::make('default_spec')
                ->schema([
                    Forms\Components\Select::make('key')
                        ->label('Specification Key')
                        ->options(fn () => AssetSpecKey::pluck('key', 'key')->filter())
                        ->required()
                        ->searchable(),
                    Forms\Components\TextInput::make('value')
                        ->label('Value')
                        ->required(),
                ])
                ->default([])
                ->label('Default Specifications')
                ->columns(2)
                ->collapsible(),


            Forms\Components\FileUpload::make('image')
                ->image()
                ->directory('assets/images')
                ->nullable(),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table->columns([
            Tables\Columns\TextColumn::make('name')->searchable(),
            Tables\Columns\TextColumn::make('type.name')->label('Type')->sortable(),
            Tables\Columns\TextColumn::make('category.name')->label('Category')->sortable(),
            Tables\Columns\IconColumn::make('is_compliance')->boolean(),
            Tables\Columns\IconColumn::make('is_fire_safety')->boolean(),
            Tables\Columns\IconColumn::make('survey_required')->boolean(),
            Tables\Columns\TextColumn::make('unit'),
            Tables\Columns\TextColumn::make('consumableType.name')->label('Consumable Type')->sortable(),
            Tables\Columns\ImageColumn::make('image')->circular()->label('Image'),
            Tables\Columns\TextColumn::make('created_at')->dateTime()->sortable(),
        ])
        ->defaultSort('created_at', 'desc')
        ->filters([
            // Add filters if needed
        ])
        ->actions([
            Tables\Actions\EditAction::make(),
            Tables\Actions\DeleteAction::make(),
        ])
        ->bulkActions([
            Tables\Actions\DeleteBulkAction::make(),
        ]);
    }

    public static function getRelations(): array
    {
        return [];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAssets::route('/'),
            'create' => Pages\CreateAsset::route('/create'),
            'edit' => Pages\EditAsset::route('/{record}/edit'),
        ];
    }
}
