<?php

namespace App\Filament\Resources\CrewAssignmentResource\Pages;

use App\Filament\Resources\CrewAssignmentResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateCrewAssignment extends CreateRecord
{
    protected static string $resource = CrewAssignmentResource::class;
    protected static bool $canCreateAnother = false;

    //customize redirect after create
    public function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
