<?php

namespace App\Filament\Resources\ShipAssetMetricResource\Pages;

use App\Filament\Resources\ShipAssetMetricResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateShipAssetMetric extends CreateRecord
{
    protected static string $resource = ShipAssetMetricResource::class;
    protected static bool $canCreateAnother = false;

    //customize redirect after create
    public function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
