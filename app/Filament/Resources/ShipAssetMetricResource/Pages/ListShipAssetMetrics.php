<?php

namespace App\Filament\Resources\ShipAssetMetricResource\Pages;

use App\Filament\Resources\ShipAssetMetricResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListShipAssetMetrics extends ListRecords
{
    protected static string $resource = ShipAssetMetricResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
