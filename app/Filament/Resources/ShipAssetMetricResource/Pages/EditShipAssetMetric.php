<?php

namespace App\Filament\Resources\ShipAssetMetricResource\Pages;

use App\Filament\Resources\ShipAssetMetricResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditShipAssetMetric extends EditRecord
{
    protected static string $resource = ShipAssetMetricResource::class;

    protected function getHeaderActions(): array
    {
        return [
        ];
    }

    //customize redirect after create
    public function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
