<?php

namespace App\Filament\Resources\SpecsResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use App\Models\AssetSpecKey;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class SpecsRelationManager extends RelationManager
{
    protected static string $relationship = 'specs'; // as in ShipAsset model

    public function form(Form $form): Form
    {
        return $form->schema([
            Forms\Components\Select::make('spec_key_id')
            ->label('Specification Key')
            ->relationship('specKey', 'key') // assumes ShipAssetSpec belongsTo AssetSpecKey as 'specKey'
            ->searchable()
            ->required()
            ->createOptionForm([
                Forms\Components\TextInput::make('key')->label('Key')->required()->unique(),
                Forms\Components\TextInput::make('unit')->label('Unit')->nullable(),
                Forms\Components\TextInput::make('description')->label('Description')->nullable(),
            ]),
            Forms\Components\TextInput::make('value')->label('Value')->required(),
        ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('specKey.key')->label('Spec Key'),
                Tables\Columns\TextColumn::make('value')->label('Value'),
            ])
            ->headerActions([Tables\Actions\CreateAction::make()])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make(),
            ]);
    }
}
