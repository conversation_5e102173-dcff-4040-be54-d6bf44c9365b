<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CrewAssignmentResource\Pages;
use App\Filament\Resources\CrewAssignmentResource\RelationManagers;
use App\Models\CrewAssignment;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class CrewAssignmentResource extends Resource
{
    protected static ?string $model = CrewAssignment::class;

    protected static ?string $navigationGroup = 'Crew Compliance';
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form->schema([
            Forms\Components\Select::make('crew_id')
                ->label('Crew')
                ->relationship('crew', 'id') // Use a real column (e.g. id or unique_crew_id)
                ->getOptionLabelFromRecordUsing(
                    fn($record) => $record->display_name // display_name accessor on Crew model
                )
                ->searchable() // Optional: customize search, see below
                ->required(),
            Forms\Components\Select::make('ship_id')->relationship('ship', 'name')->required(),
            Forms\Components\Select::make('ship_mark_id')->relationship('mark', 'name')->required(),
            Forms\Components\Select::make('ship_crew_position_requirement_id')->relationship('positionRequirement', 'name')->required(),
            Forms\Components\DatePicker::make('assigned_at')->required(),
            Forms\Components\DatePicker::make('relieved_at'),
            Forms\Components\Select::make('status')->options([
                'assigned' => 'Assigned',
                'relieved' => 'Relieved',
            ])->default('assigned')->required(),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('crew.display_name')->sortable(),
                Tables\Columns\TextColumn::make('ship.name')->sortable(),
                Tables\Columns\TextColumn::make('shipMark.name')->label('Mark'),
                Tables\Columns\TextColumn::make('positionRequirement.designation.name')->sortable(),
                Tables\Columns\TextColumn::make('status')->badge(),
                Tables\Columns\TextColumn::make('assigned_at')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('relieved_at')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),

            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCrewAssignments::route('/'),
            'create' => Pages\CreateCrewAssignment::route('/create'),
            'edit' => Pages\EditCrewAssignment::route('/{record}/edit'),
        ];
    }
}
