<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CertificateResource\Pages;
use App\Models\Certificate;
use App\Models\CertificateMaster;
use Filament\Forms;
use Filament\Resources\Resource;
use Filament\Tables;
use App\Models\Ship;
use App\Models\ShipAsset;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Carbon\Carbon;

class CertificateResource extends Resource
{
    protected static ?string $model = Certificate::class;
    protected static ?string $navigationIcon =  'heroicon-o-rectangle-stack';
    protected static ?string $navigationGroup = 'Ship Management';
     protected static ?int $navigationSort = 71;

    public static function form(Forms\Form $form): Forms\Form
    {
        return $form->schema([
            Forms\Components\Select::make('certificate_master_id')
                ->label('Certificate Type')
                ->options(CertificateMaster::pluck('name', 'id'))
                ->required()
                ->searchable()
                ->afterStateUpdated(
                    fn($state, Forms\Set $set, Forms\Get $get) =>
                    self::setExpiryAndRenewal($get, $set, $state, $get('issue_date'))
                ),
            Forms\Components\Select::make('certifiable_type')
                ->label('Type')
                ->options([
                    'App\\Models\\Ship' => 'Ship',
                    'App\\Models\\ShipAsset' => 'Ship Asset',
                ])
                ->required()
                ->reactive(),
            Forms\Components\Select::make('certifiable_id')
                ->label('Ship / Ship Asset')
                ->options(fn(Forms\Get $get) => match ($get('certifiable_type')) {
                    'App\\Models\\Ship' => \App\Models\Ship::pluck('name', 'id'),
                    'App\\Models\\ShipAsset' => \App\Models\ShipAsset::with('asset', 'ship')->get()->mapWithKeys(fn($a) => [
                        $a->id => "{$a->asset?->name} ({$a->ship?->name})"
                    ]),
                    default => [],
                })
                ->required()
                ->searchable()
                ->reactive(),
            Forms\Components\TextInput::make('certificate_number')->required(),
            Forms\Components\DatePicker::make('issue_date')
                ->required()
                ->reactive()
                ->afterStateUpdated(
                    fn($state, Forms\Set $set, Forms\Get $get) =>
                    self::setExpiryAndRenewal($get, $set, $get('certificate_master_id'), $state)
                ),
            Forms\Components\DatePicker::make('expiry_date')
                ->required()
                ->dehydrated()
                ->default(fn(Forms\Get $get) => $get('expiry_date')), // allows override
            Forms\Components\DatePicker::make('renewal_date')
                ->dehydrated()
                ->default(fn(Forms\Get $get) => $get('renewal_date'))
                ->visible(
                    fn(Forms\Get $get) =>
                    optional(CertificateMaster::find($get('certificate_master_id')))
                        ->requires_renewal ?? false
                ),
            Forms\Components\TextInput::make('issuing_authority')->required(),
            Forms\Components\Textarea::make('notes')->nullable(),
        ]);
    }
    private static function setExpiryAndRenewal($get, $set, $cmId = null, $issueDate = null)
    {
        $cm = CertificateMaster::with('validityUnit')->find($cmId ?: $get('certificate_master_id'));
        $date = $issueDate ?: $get('issue_date');

        // Default: clear both dates
        $set('expiry_date', null);
        $set('renewal_date', null);

        if (!$cm || !$date) {
            return;
        }

        $period = (int) $cm->validity_value; // should be your int column
        $unit = $cm->validityUnit?->code; // 'days', 'months', 'years'

        if ($period && $unit) {
            $expiry = match ($unit) {
                'days' => Carbon::parse($date)->addDays($period),
                'months' => Carbon::parse($date)->addMonths($period),
                'years' => Carbon::parse($date)->addYears($period),
                default => null
            };
            if ($expiry) {
                $set('expiry_date', $expiry->toDateString());
                // Use your own business logic for renewal_date
                if ($cm->requires_renewal) {
                    // Example: 30 days before expiry (customize as needed)
                    $set('renewal_date', $expiry->copy()->subDays(30)->toDateString());
                }
            }
        }
    }


    public static function table(Tables\Table $table): Tables\Table
    {
        return $table->columns([
            Tables\Columns\TextColumn::make('certificateMaster.name')->label('Certificate Type')->sortable(),

            // Display certifiable_type as human-readable
            Tables\Columns\TextColumn::make('certifiable_type')
                ->label('For')
                ->formatStateUsing(function ($state) {
                    // You can add more types here as needed!
                    return match ($state) {
                        'App\Models\Ship' => 'Ship',
                        'App\Models\ShipAsset' => 'Ship Asset',
                        default => $state,
                    };
                })
                ->sortable(),

            // Show name for Ship or ShipAsset
            Tables\Columns\TextColumn::make('certifiable_id')
                ->label('Ship / Asset')
                ->formatStateUsing(function ($state, $record) {
                    if ($record->certifiable_type === 'App\Models\Ship') {
                        return optional($record->certifiable)->name ?? "-";
                    } elseif ($record->certifiable_type === 'App\Models\ShipAsset') {
                        $asset = $record->certifiable->asset->name ?? '';
                        $ship = $record->certifiable->ship->name ?? '';
                        return $asset && $ship
                            ? "{$asset} ({$ship})"
                            : ($asset ?: ($ship ?: "-"));
                    }
                    return "-";
                })
                ->sortable(),

            Tables\Columns\TextColumn::make('certificate_number')->label('Certificate #')->sortable(),
            Tables\Columns\TextColumn::make('issue_date')->date()->sortable(),
            Tables\Columns\TextColumn::make('expiry_date')->date()->sortable(),
            Tables\Columns\TextColumn::make('created_at')->dateTime()->sortable(),
        ])
            ->filters([])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make(),
            ]);
    }


    public static function getRelations(): array
    {
        return [];
    }
    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCertificates::route('/'),
            'create' => Pages\CreateCertificate::route('/create'),
            'edit' => Pages\EditCertificate::route('/{record}/edit'),
        ];
    }
}
