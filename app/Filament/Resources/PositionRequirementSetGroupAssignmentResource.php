<?php

namespace App\Filament\Resources;

use App\Filament\Resources\PositionRequirementSetGroupAssignmentResource\Pages;
use App\Models\PositionRequirementSetGroupAssignment;
use App\Models\ShipCrewPositionRequirement;
use App\Models\RequirementSetGroup;
use Filament\Resources\Resource;
use Filament\Forms;
use Filament\Tables;

class PositionRequirementSetGroupAssignmentResource extends Resource
{
    protected static ?string $model = PositionRequirementSetGroupAssignment::class;
    protected static ?string $navigationIcon = 'heroicon-o-link';
    protected static ?string $navigationLabel = 'Assign Set Groups to Position';
    protected static ?string $navigationGroup = 'Crew Compliance';

    public static function form(Forms\Form $form): Forms\Form
    {
        return $form->schema([
            Forms\Components\Select::make('ship_crew_position_requirement_id')
                ->label('Ship / Mark / Designation')
                ->options(
                    ShipCrewPositionRequirement::with(['ship', 'shipMark', 'designation'])->get()->mapWithKeys(
                        fn ($p) => [
                            $p->id => $p->ship->name
                                . ' - ' . $p->shipMark->name
                                . ' - ' . $p->designation->name
                                . " (min: {$p->min_required}, max: {$p->max_allowed})"
                        ]
                    )->toArray()
                )
                ->searchable()
                ->required(),

            Forms\Components\Select::make('requirement_set_group_id')
                ->label('Requirement Set Group')
                ->options(
                    RequirementSetGroup::pluck('name', 'id')->toArray()
                )
                ->searchable()
                ->required(),
        ]);
    }

    public static function table(Tables\Table $table): Tables\Table
    {
        return $table->columns([
            Tables\Columns\TextColumn::make('position')
                ->label('Ship / Mark / Designation')
                ->formatStateUsing(function ($record) {
                    $pos = $record->position;
                    return $pos
                        ? $pos->ship->name . ' - ' . $pos->shipMark->name . ' - ' . $pos->designation->name
                        : '-';
                }),
            Tables\Columns\TextColumn::make('requirementSetGroup.name')
                ->label('Set Group'),
        ]);
    }

    public static function getPages(): array
    {
        return [
            'index'  => Pages\ListPositionRequirementSetGroupAssignments::route('/'),
            'create' => Pages\CreatePositionRequirementSetGroupAssignment::route('/create'),
            'edit'   => Pages\EditPositionRequirementSetGroupAssignment::route('/{record}/edit'),
        ];
    }
}
