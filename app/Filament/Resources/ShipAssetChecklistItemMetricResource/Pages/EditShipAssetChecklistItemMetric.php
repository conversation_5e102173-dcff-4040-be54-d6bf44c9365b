<?php

namespace App\Filament\Resources\ShipAssetChecklistItemMetricResource\Pages;

use App\Filament\Resources\ShipAssetChecklistItemMetricResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditShipAssetChecklistItemMetric extends EditRecord
{
    protected static string $resource = ShipAssetChecklistItemMetricResource::class;

    protected function getHeaderActions(): array
    {
        return [
        ];
    }

    //customize redirect after create
    public function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
