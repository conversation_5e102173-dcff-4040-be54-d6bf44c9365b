<?php

namespace App\Filament\Resources\ShipAssetChecklistItemMetricResource\Pages;

use App\Filament\Resources\ShipAssetChecklistItemMetricResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListShipAssetChecklistItemMetrics extends ListRecords
{
    protected static string $resource = ShipAssetChecklistItemMetricResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
