<?php

namespace App\Filament\Resources\ShipAssetChecklistItemMetricResource\Pages;

use App\Filament\Resources\ShipAssetChecklistItemMetricResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateShipAssetChecklistItemMetric extends CreateRecord
{
    protected static string $resource = ShipAssetChecklistItemMetricResource::class;
    protected static bool $canCreateAnother = false;

    //customize redirect after create
    public function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
