<?php

namespace App\Filament\Resources\ShipMarkResource\Pages;

use App\Filament\Resources\ShipMarkResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditShipMark extends EditRecord
{
    protected static string $resource = ShipMarkResource::class;

    protected function getHeaderActions(): array
    {
        return [
        ];
    }

    //customize redirect after create
    public function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
