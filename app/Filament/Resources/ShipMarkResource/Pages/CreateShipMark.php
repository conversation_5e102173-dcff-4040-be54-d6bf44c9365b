<?php

namespace App\Filament\Resources\ShipMarkResource\Pages;

use App\Filament\Resources\ShipMarkResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateShipMark extends CreateRecord
{
    protected static string $resource = ShipMarkResource::class;
    protected static bool $canCreateAnother = false;

    //customize redirect after create
    public function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
