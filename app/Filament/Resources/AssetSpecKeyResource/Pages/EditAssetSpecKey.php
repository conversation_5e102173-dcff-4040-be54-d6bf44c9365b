<?php

namespace App\Filament\Resources\AssetSpecKeyResource\Pages;

use App\Filament\Resources\AssetSpecKeyResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditAssetSpecKey extends EditRecord
{
    protected static string $resource = AssetSpecKeyResource::class;

    protected function getHeaderActions(): array
    {
        return [
        ];
    }

    //customize redirect after create
    public function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
