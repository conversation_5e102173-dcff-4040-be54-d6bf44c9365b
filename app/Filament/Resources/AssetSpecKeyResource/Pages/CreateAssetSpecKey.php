<?php

namespace App\Filament\Resources\AssetSpecKeyResource\Pages;

use App\Filament\Resources\AssetSpecKeyResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateAssetSpecKey extends CreateRecord
{
    protected static string $resource = AssetSpecKeyResource::class;
    protected static bool $canCreateAnother = false;

    //customize redirect after create
    public function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
