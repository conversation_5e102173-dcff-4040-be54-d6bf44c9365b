<?php

namespace App\Filament\Resources\AssetSpecKeyResource\Pages;

use App\Filament\Resources\AssetSpecKeyResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListAssetSpecKeys extends ListRecords
{
    protected static string $resource = AssetSpecKeyResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
