<?php

namespace App\Filament\Resources\ShipDocumentMasterResource\Pages;

use App\Filament\Resources\ShipDocumentMasterResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListShipDocumentMasters extends ListRecords
{
    protected static string $resource = ShipDocumentMasterResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
