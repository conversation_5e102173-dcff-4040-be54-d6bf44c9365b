<?php

namespace App\Filament\Resources\ShipDocumentMasterResource\Pages;

use App\Filament\Resources\ShipDocumentMasterResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditShipDocumentMaster extends EditRecord
{
    protected static string $resource = ShipDocumentMasterResource::class;

    protected function getHeaderActions(): array
    {
        return [
        ];
    }

    //customize redirect after create
    public function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
