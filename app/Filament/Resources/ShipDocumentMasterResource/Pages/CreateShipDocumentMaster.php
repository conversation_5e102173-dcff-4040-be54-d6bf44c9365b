<?php

namespace App\Filament\Resources\ShipDocumentMasterResource\Pages;

use App\Filament\Resources\ShipDocumentMasterResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateShipDocumentMaster extends CreateRecord
{
    protected static string $resource = ShipDocumentMasterResource::class;
    protected static bool $canCreateAnother = false;

    //customize redirect after create
    public function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
