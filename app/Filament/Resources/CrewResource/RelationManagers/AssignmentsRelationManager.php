<?php

namespace App\Filament\Resources\CrewResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class AssignmentsRelationManager extends RelationManager
{
    protected static string $relationship = 'assignments';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
               Forms\Components\Select::make('ship_mark_id')
                ->label('Ship Mark')
                ->relationship('shipMark', 'name') // or your appropriate display field
                ->required(),
            Forms\Components\Select::make('designation_id')
                ->relationship('designation', 'name')
                ->required(),
            Forms\Components\DateTimePicker::make('assigned_at')->label('Onboarded At')->required(),
            Forms\Components\DateTimePicker::make('relieved_at')->label('Offboarded At'),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('designation.name')
            ->columns([
                  Tables\Columns\TextColumn::make('shipMark.ship.name')->label('Ship'),
            Tables\Columns\TextColumn::make('shipMark.mark')->label('Mark'),
            Tables\Columns\TextColumn::make('designation.name')->label('Designation'),
            Tables\Columns\TextColumn::make('assigned_at')->label('Onboarded At')->dateTime(),
            Tables\Columns\TextColumn::make('relieved_at')->label('Offboarded At')->dateTime(),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
