<?php

namespace App\Filament\Resources\CrewResource\Pages;

use App\Filament\Resources\CrewResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateCrew extends CreateRecord
{
    protected static string $resource = CrewResource::class;
    protected static bool $canCreateAnother = false;

    //customize redirect after create
    public function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
