<?php

namespace App\Filament\Resources;

use App\Filament\Resources\AssetChecklistMasterResource\Pages;
use App\Models\AssetChecklistMaster;
use App\Models\AssetCategory;
use App\Models\Tag;
use App\Models\MasterEntry;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class AssetChecklistMasterResource extends Resource
{
    protected static ?string $model = AssetChecklistMaster::class;
    protected static ?string $navigationIcon = 'heroicon-o-check-circle';
    protected static ?string $navigationGroup = 'Ship Management';
     protected static ?int $navigationSort = 92;

    public static function form(Form $form): Form
    {
        return $form->schema([
            Forms\Components\Select::make('category_id')
                ->label('Asset Category')
                ->options(fn() => AssetCategory::pluck('name', 'id')->toArray())
                ->searchable()
                ->nullable(),

            Forms\Components\TextInput::make('question')
                ->required()
                ->maxLength(255)
                ->columnSpanFull(),

                Forms\Components\Select::make('input_type_id')
                ->label('Input Type')
                ->searchable()
                ->options(fn () => MasterEntry::query()
                    ->whereHas('type', fn ($q) => $q->where('name', 'input_type'))
                    ->pluck('name', 'id'))
                ->required()
                ->reactive(), // needed for conditionally showing options


            // Dynamic options for select type fields


             Forms\Components\Repeater::make('options')
                    ->label('Options')
                    ->schema([
                        Forms\Components\TextInput::make('option')
                            ->label('Option')
                            ->required(),
                    ])
                    ->default([]) // ← for new create
                    ->columns(1)
                    ->addActionLabel('Add Option')
                    ->visible(fn(Forms\Get $get) => in_array(
                        optional(MasterEntry::find($get('input_type_id')))->code,
                        ['select_single', 'select_multiple', 'select']
                    ))
                    ->afterStateHydrated(function ($component, $state) {
                        // If it's a flat array (["A", "B"]), convert it to structured form
                        if (is_array($state) && collect($state)->every(fn($v) => is_string($v))) {
                            $component->state(
                                collect($state)
                                    ->map(fn($value) => ['option' => $value])
                                    ->values()
                                    ->all()
                            );
                        }
                    })
                    ->dehydrated(true),



            Forms\Components\Toggle::make('image_required')
                ->label('Image Required'),

            Forms\Components\Toggle::make('is_for_survey')
                ->label('Used for Survey'),

            Forms\Components\Select::make('default_tag_id')
                ->label('Default Tag')
                ->options(fn() => Tag::pluck('name', 'id')->toArray())
                ->searchable()
                ->nullable(),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table->columns([
            Tables\Columns\TextColumn::make('question')->searchable(),
            Tables\Columns\TextColumn::make('category.name')->label('Category')->sortable(),
            Tables\Columns\TextColumn::make('inputType.name')->label('Input Type')->sortable(),
            Tables\Columns\IconColumn::make('image_required')->boolean()->label('Image Req.'),
            Tables\Columns\IconColumn::make('is_for_survey')->boolean()->label('For Survey'),
            Tables\Columns\TextColumn::make('defaultTag.name')->label('Default Tag'),
            Tables\Columns\TextColumn::make('created_at')->dateTime()->sortable()->toggleable(isToggledHiddenByDefault: true),
            Tables\Columns\TextColumn::make('updated_at')->dateTime()->sortable()->toggleable(isToggledHiddenByDefault: true),
        ])
            ->filters([])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAssetChecklistMasters::route('/'),
            'create' => Pages\CreateAssetChecklistMaster::route('/create'),
            'edit' => Pages\EditAssetChecklistMaster::route('/{record}/edit'),
        ];
    }
}
