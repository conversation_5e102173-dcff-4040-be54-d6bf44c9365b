<?php

namespace App\Filament\Resources\DailyAssetLogResource\RelationManagers;

use Filament\Forms;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Forms\Get;
use Filament\Tables;
use Filament\Forms\Form;
use Filament\Tables\Table;

class AssetLogMetricsRelationManager extends RelationManager
{
    protected static string $relationship = 'metrics';

    public function form(Form $form): Form
    {
        return $form->schema([
            Forms\Components\Select::make('ship_asset_metric_id')
                ->label('Metric')
                ->options(function (Get $get) {
                    // Only metrics for this ship asset
                    $parent = $this->getOwnerRecord();
                    // Get the related ShipAsset (you may need to adjust this depending on how your DailyAssetLog stores reference)
                    $shipAssetId = $parent->ship_asset_id ?? null;
                    if (!$shipAssetId) return [];
                    return \App\Models\ShipAssetMetric::where('ship_asset_id', $shipAssetId)
                        ->with('metricType')
                        ->get()
                        ->mapWithKeys(fn($m) => [$m->id => $m->metricType->name])
                        ->toArray();
                })
                ->required()
                ->searchable(),
            Forms\Components\TextInput::make('value')
                ->label('Reading/Value')
                ->required()
                ->numeric(),

            Forms\Components\Textarea::make('notes')->nullable(),
        ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('shipAssetMetric.metricType.name')
            ->columns([
                Tables\Columns\TextColumn::make('shipAssetMetric.metricType.name')->label('Metric'),
                Tables\Columns\TextColumn::make('value')->label('Value'),
                Tables\Columns\TextColumn::make('created_at')->dateTime(),
            ])
             ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ]);
    }
}
