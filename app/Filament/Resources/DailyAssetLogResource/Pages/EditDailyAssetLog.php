<?php

namespace App\Filament\Resources\DailyAssetLogResource\Pages;

use App\Filament\Resources\DailyAssetLogResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditDailyAssetLog extends EditRecord
{
    protected static string $resource = DailyAssetLogResource::class;

    protected function getHeaderActions(): array
    {
        return [
        ];
    }

    //customize redirect after create
    public function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
