<?php

namespace App\Filament\Resources\DailyAssetLogResource\Pages;

use App\Filament\Resources\DailyAssetLogResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListDailyAssetLogs extends ListRecords
{
    protected static string $resource = DailyAssetLogResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
