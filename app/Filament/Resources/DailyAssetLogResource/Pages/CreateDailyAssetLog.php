<?php

namespace App\Filament\Resources\DailyAssetLogResource\Pages;

use App\Filament\Resources\DailyAssetLogResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateDailyAssetLog extends CreateRecord
{
    protected static string $resource = DailyAssetLogResource::class;
    protected static bool $canCreateAnother = false;

    //customize redirect after create
    public function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
