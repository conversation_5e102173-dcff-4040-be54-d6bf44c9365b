<?php

namespace App\Filament\Resources\AssetChecklistMasterResource\Pages;

use App\Filament\Resources\AssetChecklistMasterResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateAssetChecklistMaster extends CreateRecord
{
    protected static string $resource = AssetChecklistMasterResource::class;
    protected static bool $canCreateAnother = false;

    //customize redirect after create
    public function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
