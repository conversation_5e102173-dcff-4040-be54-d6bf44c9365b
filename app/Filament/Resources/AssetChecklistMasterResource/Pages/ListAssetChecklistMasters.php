<?php

namespace App\Filament\Resources\AssetChecklistMasterResource\Pages;

use App\Filament\Resources\AssetChecklistMasterResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListAssetChecklistMasters extends ListRecords
{
    protected static string $resource = AssetChecklistMasterResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
