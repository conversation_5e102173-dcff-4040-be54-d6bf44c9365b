<?php

namespace App\Filament\Resources\AssetChecklistMasterResource\Pages;

use App\Filament\Resources\AssetChecklistMasterResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditAssetChecklistMaster extends EditRecord
{
    protected static string $resource = AssetChecklistMasterResource::class;

    protected function getHeaderActions(): array
    {
        return [
        ];
    }

     protected function mutateFormDataBeforeSave(array $data): array
    {

        if (isset($data['options']) && is_array($data['options'])) {
            $data['options'] = collect($data['options'])
                ->map(function ($item) {
                    // Accept both string or ['option' => '...']
                    if (is_array($item) && isset($item['option'])) {
                        return $item['option'];
                    } elseif (is_string($item)) {
                        return $item;
                    }
                    return null;
                })
                ->filter()
                ->values()
                ->unique()
                ->all();
        }

        return $data;
    }

    //customize redirect after create
    public function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
