<?php

namespace App\Filament\Resources;

use App\Filament\Resources\AssetMovementResource\Pages;
use App\Models\AssetMovement;
use App\Models\Asset;
use App\Models\Ship;
use App\Models\Location;
use App\Models\User;
use App\Models\MasterEntry;
use Filament\Forms;
use Filament\Resources\Resource;
use Filament\Tables;

class AssetMovementResource extends Resource
{
    protected static ?string $model = AssetMovement::class;
    protected static ?string $navigationIcon = 'heroicon-o-arrows-up-down';
    protected static ?string $navigationGroup = 'Asset Logs';

    public static function form(Forms\Form $form): Forms\Form
    {
        return $form->schema([
            Forms\Components\Select::make('asset_id')
                ->relationship('asset', 'name')
                ->searchable()
                ->required(),
            Forms\Components\Select::make('ship_id')
                ->relationship('ship', 'name')
                ->searchable()
                ->required(),
            Forms\Components\Select::make('from_location_id')
                ->relationship('fromLocation', 'name')
                ->nullable()
                ->searchable(),
            Forms\Components\Select::make('to_location_id')
                ->relationship('toLocation', 'name')
                ->nullable()
                ->searchable(),
            Forms\Components\TextInput::make('quantity')->numeric()->default(1)->required(),

            Forms\Components\Select::make('movement_type_id')
                ->options(fn() => MasterEntry::whereHas('type', fn($q) => $q->where('name','asset_movement_type'))->pluck('name','id'))
                ->searchable()
                ->required(),
            Forms\Components\Select::make('moved_by')
                ->relationship('user', 'name')
                ->searchable()
                ->required(),
            Forms\Components\DateTimePicker::make('moved_at')->default(now())->required(),
            Forms\Components\Textarea::make('notes')->nullable(),
        ]);
    }

    public static function table(Tables\Table $table): Tables\Table
    {
        return $table->columns([
            Tables\Columns\TextColumn::make('asset.name')->label('Asset'),
            Tables\Columns\TextColumn::make('ship.name')->label('Ship'),
            Tables\Columns\TextColumn::make('fromLocation.name')->label('From Location'),
            Tables\Columns\TextColumn::make('toLocation.name')->label('To Location'),
            Tables\Columns\TextColumn::make('quantity'),
            Tables\Columns\TextColumn::make('movementType.name')->label('Movement Type'),
            Tables\Columns\TextColumn::make('user.name')->label('Moved By'),
            Tables\Columns\TextColumn::make('moved_at')->dateTime(),
            Tables\Columns\TextColumn::make('created_at')->dateTime()->sortable(),
        ])
        ->defaultSort('moved_at', 'desc')
        ->actions([
            Tables\Actions\EditAction::make(),
            Tables\Actions\DeleteAction::make(),
        ])
        ->bulkActions([
            Tables\Actions\DeleteBulkAction::make(),
        ]);
    }

    public static function getRelations(): array
    {
        return [];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAssetMovements::route('/'),
            'create' => Pages\CreateAssetMovement::route('/create'),
            'edit' => Pages\EditAssetMovement::route('/{record}/edit'),
        ];
    }
}
