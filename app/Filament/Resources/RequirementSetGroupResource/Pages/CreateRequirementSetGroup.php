<?php

namespace App\Filament\Resources\RequirementSetGroupResource\Pages;

use App\Filament\Resources\RequirementSetGroupResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateRequirementSetGroup extends CreateRecord
{
    protected static string $resource = RequirementSetGroupResource::class;
    protected static bool $canCreateAnother = false;

    //customize redirect after create
    public function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
