<?php

namespace App\Filament\Resources\RequirementSetGroupResource\Pages;

use App\Filament\Resources\RequirementSetGroupResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListRequirementSetGroups extends ListRecords
{
    protected static string $resource = RequirementSetGroupResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
