<?php

namespace App\Filament\Resources\RequirementSetGroupResource\Pages;

use App\Filament\Resources\RequirementSetGroupResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditRequirementSetGroup extends EditRecord
{
    protected static string $resource = RequirementSetGroupResource::class;

    protected function getHeaderActions(): array
    {
        return [
        ];
    }

    //customize redirect after create
    public function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
