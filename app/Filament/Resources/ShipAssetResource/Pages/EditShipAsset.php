<?php

namespace App\Filament\Resources\ShipAssetResource\Pages;

use App\Filament\Resources\ShipAssetResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditShipAsset extends EditRecord
{
    protected static string $resource = ShipAssetResource::class;

    protected function getHeaderActions(): array
    {
        return [
        ];
    }

    //customize redirect after create
    public function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
