<?php

namespace App\Filament\Resources\ShipAssetResource\Pages;

use App\Filament\Resources\ShipAssetResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListShipAssets extends ListRecords
{
    protected static string $resource = ShipAssetResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
