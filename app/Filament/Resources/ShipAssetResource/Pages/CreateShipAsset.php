<?php

namespace App\Filament\Resources\ShipAssetResource\Pages;

use App\Filament\Resources\ShipAssetResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateShipAsset extends CreateRecord
{
    protected static string $resource = ShipAssetResource::class;
    protected static bool $canCreateAnother = false;

    //customize redirect after create
    public function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
