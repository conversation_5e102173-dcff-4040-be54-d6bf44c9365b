<?php

namespace App\Filament\Resources\ShipAssetResource\RelationManagers;

use App\Models\MetricType;
use App\Models\MasterEntry;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;

class MetricsRelationManager extends RelationManager
{
    protected static string $relationship = 'metrics'; // should match ShipAsset::metrics()

    public function form(Form $form): Form
    {
        return $form->schema([
            Forms\Components\Select::make('metric_type_id')
                ->label('Metric Type')
                ->relationship('metricType', 'name')
                ->searchable()
                ->required(),

            Forms\Components\Toggle::make('is_required')
                ->label('Is Required')
                ->default(false),
            Forms\Components\Toggle::make('is_rob')
                ->label('Is Primary')
                ->default(false),

            Forms\Components\Toggle::make('is_transactional')
                ->label('Is Required')
                ->default(false),
            Forms\Components\Toggle::make('is_primary')
                ->label('Is Primary')
                ->default(false),

            Forms\Components\TextInput::make('min_threshold')
                ->label('Min Threshold')
                ->numeric()
                ->step(0.0001)
                ->nullable(),
            Forms\Components\TextInput::make('max_threshold')
                ->label('Max Threshold')
                ->numeric()
                ->step(0.0001)
                ->nullable(),

            Forms\Components\Toggle::make('alert_on_min_breach')
                ->label('Alert on Min Breach')
                ->default(false),
            Forms\Components\Toggle::make('alert_on_max_breach')
                ->label('Alert on Max Breach')
                ->default(false),

            Forms\Components\Select::make('alert_type_id')
                ->label('Alert Type')
                ->options(fn() => MasterEntry::whereHas('type', fn($q) => $q->where('name','alert_type'))->pluck('name','id'))
                ->searchable()
                ->nullable(),

            Forms\Components\TextInput::make('unit')
                ->label('Unit')
                ->maxLength(16)
                ->nullable(),

            Forms\Components\TextInput::make('reminder_days')
                ->label('Reminder Days (comma separated)')
                ->helperText('Enter as 1,7,30 for days')
                ->afterStateHydrated(function ($component, $state) {
                    // Cast array to string for form display
                    $component->state(is_array($state) ? implode(',', $state) : $state);
                })
                ->dehydrateStateUsing(function ($state) {
                    // Convert string to array for DB
                    return array_filter(array_map('trim', explode(',', $state)));
                })
                ->nullable(),

            Forms\Components\Textarea::make('notes')
                ->label('Notes')
                ->maxLength(500)
                ->nullable(),
        ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('metricType.name')->label('Metric'),
                Tables\Columns\IconColumn::make('is_primary')->boolean(),
                Tables\Columns\IconColumn::make('is_rob')->boolean(),
                Tables\Columns\IconColumn::make('is_transactional')->boolean(),
                Tables\Columns\IconColumn::make('is_required')->boolean(),
                Tables\Columns\TextColumn::make('min_threshold')->label('Min'),
                Tables\Columns\TextColumn::make('max_threshold')->label('Max'),
                Tables\Columns\IconColumn::make('alert_on_min_breach')->boolean()->label('Alert Min'),
                Tables\Columns\IconColumn::make('alert_on_max_breach')->boolean()->label('Alert Max'),
                Tables\Columns\TextColumn::make('alertType.name')->label('Alert Type')->sortable(),
                Tables\Columns\TextColumn::make('unit')->label('Unit'),
                Tables\Columns\TextColumn::make('reminder_days')->label('Reminders')
                    ->formatStateUsing(fn($state) => is_array($state) ? implode(',', $state) : $state),
                Tables\Columns\TextColumn::make('notes')->label('Notes')->limit(30),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ]);
    }
}
