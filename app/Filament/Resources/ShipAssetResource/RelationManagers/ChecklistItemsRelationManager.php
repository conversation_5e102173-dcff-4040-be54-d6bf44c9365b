<?php

namespace App\Filament\Resources\ShipAssetResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use App\Filament\Resources\ShipAssetChecklistItemResource;

class ChecklistItemsRelationManager extends RelationManager
{
    protected static string $relationship = 'checklistItems';

    public function form(Form $form): Form
    {
        return $form->schema(ShipAssetChecklistItemResource::getFormSchema());
        // return $form->schema([
        //     Forms\Components\Select::make('checklist_master_id')
        //         ->label('Checklist Question')
        //         ->relationship('master', 'question')
        //         ->required()
        //         ->searchable(),
        //     Forms\Components\Toggle::make('is_for_survey')->label('Is for Survey?'),
        // ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('master.question')
            ->columns([
                Tables\Columns\TextColumn::make('master.question')->label('Checklist Question')->searchable(),
                Tables\Columns\IconColumn::make('is_for_survey')->boolean()->label('For Survey'),
            ])
            ->filters([])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make(),
            ]);
    }
}
