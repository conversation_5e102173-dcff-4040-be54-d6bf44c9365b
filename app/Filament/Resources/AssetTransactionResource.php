<?php

namespace App\Filament\Resources;

use App\Filament\Resources\AssetTransactionResource\Pages;
use App\Models\AssetTransaction;
use App\Models\ShipAsset;
use App\Models\MasterEntry;
use App\Models\User;
use Filament\Forms;
use Filament\Resources\Resource;
use Filament\Tables;

class AssetTransactionResource extends Resource
{
    protected static ?string $model = AssetTransaction::class;
    protected static ?string $navigationIcon = 'heroicon-o-arrows-right-left';
    protected static ?string $navigationGroup = 'Ship Management';

    public static function form(Forms\Form $form): Forms\Form
    {
        return $form->schema([
            Forms\Components\Select::make('ship_asset_id')
                ->label('Ship Asset')
                ->options(ShipAsset::with('asset', 'ship')->get()
                    ->mapWithKeys(fn($s) => [$s->id => $s->asset->name . ' (' . $s->ship->name . ')']))
                ->searchable()
                ->required()
                ->reactive(),

           Forms\Components\Select::make('metric_type_id')
            ->label('Metric')
            ->options(function ($get) {
                $shipAssetId = $get('ship_asset_id');
                if (!$shipAssetId) return [];
                return \App\Models\ShipAssetMetric::where('ship_asset_id', $shipAssetId)
                    ->with('metricType')
                    ->get()
                    ->mapWithKeys(fn($m) => [$m->metric_type_id => $m->metricType->name]);
            })
            ->required()
            ->searchable()
            ->reactive(),

            Forms\Components\Select::make('transaction_type_id')
                ->label('Transaction Type')
                ->options(fn() => MasterEntry::whereHas('type', fn($q) => $q->where('name', 'asset_transaction_type'))->pluck('name', 'id'))
                ->required(),

            Forms\Components\TextInput::make('quantity')
                ->numeric()
                ->required()
                ->afterStateUpdated(function ($state, $set, $get) {
                    static::updateNewLevel($set, $get, $state);
                }),

            Forms\Components\Placeholder::make('unit')
                ->label('Unit')
                ->content(
                    fn($get) =>
                    \App\Models\MetricType::find($get('metric_type_id'))?->default_unit ?? '-'
                ),

            Forms\Components\TextInput::make('old_level')->readOnly()->dehydrated(true),
            Forms\Components\TextInput::make('new_level')->readOnly()->dehydrated(true),

            // Select for admins (editable), disabled for others
            Forms\Components\Select::make('recorded_by')
                ->label('Recorded By')
                ->options(User::pluck('name', 'id'))
                ->default(auth()->id())
                ->required()
                ->searchable()
                ->disabled(!auth()->user()->hasRole('super_admin')),

            // Hidden field ensures the value is POSTed
            Forms\Components\Hidden::make('recorded_by')
                ->default(auth()->id())
                ->dehydrated()
                ->visible(!auth()->user()->hasRole('super_admin')), // Only non-admins see this hidden input


            Forms\Components\Textarea::make('notes')->nullable(),
            Forms\Components\DateTimePicker::make('transaction_time')->default(now())->required(),
        ]);
    }

    // Logic for updating levels
    protected static function updateNewLevel($set, $get, $qty)
    {
        $shipAsset = ShipAsset::find($get('ship_asset_id'));
        $metricTypeId = $get('metric_type_id');
        $type = MasterEntry::find($get('transaction_type_id'))?->code;
        if (!$shipAsset || !$metricTypeId) return;

        // Get most recent transaction for this asset+metric
        $lastTxn = AssetTransaction::where('ship_asset_id', $shipAsset->id)
            ->where('metric_type_id', $metricTypeId)
            ->orderByDesc('transaction_time')->first();


        $oldLevel = $lastTxn?->new_level ?? 0;
        $newLevel = $oldLevel;

        if ($type === 'addition' || $type === 'refill') {
            $newLevel += floatval($qty);
        } elseif ($type === 'consumption') {
            $newLevel -= floatval($qty);
        } elseif ($type === 'adjustment') {
            $newLevel = floatval($qty);
        }

        $set('old_level', $oldLevel);
        $set('new_level', $newLevel);

        // dd($lastTxn, $newLevel, $oldLevel);
    }

    public static function table(Tables\Table $table): Tables\Table
    {
        return $table->columns([
            Tables\Columns\TextColumn::make('shipAsset.asset.name')->label('Asset'),
            Tables\Columns\TextColumn::make('metricType.name')->label('Metric'),
            Tables\Columns\TextColumn::make('transactionType.name')->label('Type'),
            Tables\Columns\TextColumn::make('quantity')->suffix(fn($record) => $record->metricType?->default_unit),
            Tables\Columns\TextColumn::make('old_level')->label('Previous Level'),
            Tables\Columns\TextColumn::make('new_level')->label('New Level'),
            Tables\Columns\TextColumn::make('recordedBy.name')->label('Recorded By'),
            Tables\Columns\TextColumn::make('transaction_time')->dateTime(),
            Tables\Columns\TextColumn::make('created_at')->dateTime()->sortable(),
          ])->filters([])->actions([
            Tables\Actions\EditAction::make(),
            Tables\Actions\DeleteAction::make(),
        ])->bulkActions([
            Tables\Actions\DeleteBulkAction::make(),
        ]);
    }


    public static function getRelations(): array
    {
        return [];
    }
    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAssetTransactions::route('/'),
            'create' => Pages\CreateAssetTransaction::route('/create'),
            'edit' => Pages\EditAssetTransaction::route('/{record}/edit'),
        ];
    }
}
