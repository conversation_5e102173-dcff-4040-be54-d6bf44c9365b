<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ShipDocumentResource\Pages;
use App\Models\ShipDocument;
use App\Models\Ship;
use App\Models\ShipDocumentMaster;
use Filament\Forms;
use Filament\Resources\Resource;
use Filament\Tables;

class ShipDocumentResource extends Resource
{
    protected static ?string $model = ShipDocument::class;

    protected static ?string $navigationGroup = 'Ship Management';
    protected static ?int $navigationSort = 81;
    protected static ?string $navigationIcon = 'heroicon-o-document';

    public static function form(Forms\Form $form): Forms\Form
    {
        return $form->schema([
            Forms\Components\Select::make('ship_id')
                ->label('Ship')
                ->options(Ship::pluck('name','id'))
                ->required()
                ->searchable(),
            Forms\Components\Select::make('document_master_id')
                ->label('Document Type')
                ->options(ShipDocumentMaster::pluck('name','id'))
                ->required()
                ->searchable(),
            Forms\Components\FileUpload::make('document_path')->directory('ship_documents')->required(),
            Forms\Components\Textarea::make('notes')->nullable(),
        ]);
    }

    public static function table(Tables\Table $table): Tables\Table
    {
        return $table->columns([
            Tables\Columns\TextColumn::make('ship.name')->sortable(),
            Tables\Columns\TextColumn::make('documentMaster.name')->label('Type')->sortable(),
            Tables\Columns\TextColumn::make('document_path')->label('File'),
            Tables\Columns\TextColumn::make('created_at')->dateTime()->sortable(),
        ])->filters([])->actions([
            Tables\Actions\EditAction::make(),
            Tables\Actions\DeleteAction::make(),
        ])->bulkActions([
            Tables\Actions\DeleteBulkAction::make(),
        ]);
    }

    public static function getRelations(): array { return []; }
    public static function getPages(): array
    {
        return [
            'index' => Pages\ListShipDocuments::route('/'),
            'create' => Pages\CreateShipDocument::route('/create'),
            'edit' => Pages\EditShipDocument::route('/{record}/edit'),
        ];
    }
}
