<?php

namespace App\Filament\Resources\ShipClassResource\Pages;

use App\Filament\Resources\ShipClassResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListShipClasses extends ListRecords
{
    protected static string $resource = ShipClassResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
