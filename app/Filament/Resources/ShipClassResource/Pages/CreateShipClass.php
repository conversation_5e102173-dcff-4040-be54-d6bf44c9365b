<?php

namespace App\Filament\Resources\ShipClassResource\Pages;

use App\Filament\Resources\ShipClassResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateShipClass extends CreateRecord
{
    protected static string $resource = ShipClassResource::class;
    protected static bool $canCreateAnother = false;

    //customize redirect after create
    public function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
