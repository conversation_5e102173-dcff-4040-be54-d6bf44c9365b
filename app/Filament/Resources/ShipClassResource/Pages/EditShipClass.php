<?php

namespace App\Filament\Resources\ShipClassResource\Pages;

use App\Filament\Resources\ShipClassResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditShipClass extends EditRecord
{
    protected static string $resource = ShipClassResource::class;

    protected function getHeaderActions(): array
    {
        return [
        ];
    }

    //customize redirect after create
    public function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
