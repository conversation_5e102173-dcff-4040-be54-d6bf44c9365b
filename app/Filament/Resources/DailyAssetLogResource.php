<?php

namespace App\Filament\Resources;

use App\Filament\Resources\DailyAssetLogResource\Pages;
use  App\Filament\Resources\DailyAssetLogResource\RelationManagers\AssetLogMetricsRelationManager;
use App\Models\DailyAssetLog;
use App\Models\ShipAsset;
use App\Models\User;
use Filament\Forms;
use Filament\Resources\Resource;
use Filament\Tables;

class DailyAssetLogResource extends Resource
{
    protected static ?string $model = DailyAssetLog::class;
    protected static ?string $navigationIcon = 'heroicon-o-calendar-days';
    protected static ?string $navigationGroup = 'Asset Logs';

    public static function form(Forms\Form $form): Forms\Form
    {
        return $form->schema([
            Forms\Components\Select::make('ship_asset_id')
                ->label('Ship Asset')
                ->options(ShipAsset::with('asset', 'ship')->get()->mapWithKeys(
                    fn($sa) => [$sa->id => $sa->asset->name.' ('.$sa->ship->name.')']
                ))
                ->required()
                ->searchable(),
            Forms\Components\DateTimePicker::make('log_time')
                ->default(now())
                ->required(),
            Forms\Components\Select::make('recorded_by')
                ->label('Recorded By')
                ->options(User::pluck('name', 'id'))
                ->searchable()
                ->required(),
            Forms\Components\Textarea::make('notes')->nullable(),
        ]);
    }

    public static function table(Tables\Table $table): Tables\Table
    {
        return $table->columns([
            Tables\Columns\TextColumn::make('shipAsset.asset.name')->label('Asset'),
            Tables\Columns\TextColumn::make('shipAsset.ship.name')->label('Ship'),
            Tables\Columns\TextColumn::make('log_time')->dateTime()->sortable(),
            Tables\Columns\TextColumn::make('recorder.name')->label('Recorded By'),
            Tables\Columns\TextColumn::make('created_at')->dateTime()->sortable(),
        ])
        ->defaultSort('log_time', 'desc')
        ->actions([
            Tables\Actions\EditAction::make(),
            Tables\Actions\DeleteAction::make(),
        ])
        ->bulkActions([
            Tables\Actions\DeleteBulkAction::make(),
        ]);
    }

    public static function getRelations(): array
    {
        return [
           AssetLogMetricsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDailyAssetLogs::route('/'),
            'create' => Pages\CreateDailyAssetLog::route('/create'),
            'edit' => Pages\EditDailyAssetLog::route('/{record}/edit'),
        ];
    }
}
