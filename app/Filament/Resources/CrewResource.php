<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CrewResource\Pages;
use App\Models\Crew;
use App\Models\MasterEntry;
use Filament\Resources\Resource;
use Filament\Forms;
use Filament\Tables;

class CrewResource extends Resource
{
    protected static ?string $model = Crew::class;
    protected static ?string $navigationIcon = 'heroicon-o-user-group';
    protected static ?string $navigationGroup = 'Crew Management';
    protected static ?string $label = 'Crew';

public static function form(Forms\Form $form): Forms\Form
{
    return $form->schema([
        Forms\Components\Select::make('user_id')
            ->label('User')
            ->options(
                \App\Models\User::whereHas('roles', fn ($q) => $q->where('name', 'crew'))
                    ->whereDoesntHave('crew')
                    ->pluck('name', 'id')
            )
            ->searchable()
            ->required()
            ->helperText('Select a user with the "crew" role only. Already assigned users are hidden.'),

        Forms\Components\TextInput::make('unique_crew_id')
            ->label('Unique Crew ID')
            ->required()
            ->unique(ignoreRecord: true)
            ->helperText('Enter a unique Crew ID (must not be used for any other crew). This field is mandatory.'),

        Forms\Components\TextInput::make('aadhaar_number')
            ->label('Aadhaar Number')
            ->required()
            ->minLength(12)
            ->maxLength(12)
            ->numeric()
            ->helperText('Enter the 12-digit Aadhaar number. No spaces or dashes.'),

        Forms\Components\TextInput::make('pan_number')
            ->label('PAN Number')
            // ->required()
            ->minLength(10)
            ->maxLength(10)
            ->regex('/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/')
            ->helperText('Enter the 10-character PAN (e.g., **********). Uppercase only, no spaces.'),

        Forms\Components\TextInput::make('passport_number')
            ->label('Passport Number')
            // ->required()
            ->maxLength(20)
            ->helperText('Enter the passport number as per the official document.'),

        Forms\Components\Select::make('status_id')
            ->label('Status')
            ->options(fn() => \App\Models\MasterEntry::whereHas('type', fn($q) => $q->where('name', 'crew_status'))->pluck('name', 'id'))
            ->searchable()
            ->required()
            ->helperText('Select the current status of the crew member (e.g., Active, On Leave, Inactive).'),

        // Add more fields as required, each with helperText and validation
    ]);


    }

    public static function table(Tables\Table $table): Tables\Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('display_name')->label('Name')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('unique_crew_id')->label('Crew ID')->sortable(),
                Tables\Columns\TextColumn::make('status.name')->label('Status'),
                Tables\Columns\TextColumn::make('current_assignment.designation.name')->label('Current Designation'),
                Tables\Columns\TextColumn::make('current_assignment.shipMark.ship.name')->label('Current Ship'),
                Tables\Columns\TextColumn::make('current_assignment.assigned_at')->label('Onboarded At')->dateTime(),
                Tables\Columns\TextColumn::make('current_assignment.relieved_at')->label('Offboarded At')->dateTime(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            CrewResource\RelationManagers\AssignmentsRelationManager::class,
            // Add CrewHistoryRelationManager if you wish
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCrews::route('/'),
            'create' => Pages\CreateCrew::route('/create'),
            'edit' => Pages\EditCrew::route('/{record}/edit'),
            // 'view' => Pages\ViewCrew::route('/{record}'),
        ];
    }
}
