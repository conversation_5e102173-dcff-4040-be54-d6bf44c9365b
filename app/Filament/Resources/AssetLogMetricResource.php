<?php

namespace App\Filament\Resources;

use App\Filament\Resources\AssetLogMetricResource\Pages;
use App\Models\AssetLogMetric;
use App\Models\MetricType;
use App\Models\DailyAssetLog;
use Filament\Forms;
use Filament\Resources\Resource;
use Filament\Tables;

class AssetLogMetricResource extends Resource
{
    protected static ?string $model = AssetLogMetric::class;
    protected static ?string $navigationIcon = 'heroicon-o-clipboard-document-list';
    protected static ?string $navigationGroup = 'Asset Logs';

    public static function form(Forms\Form $form): Forms\Form
    {
        return $form->schema([
            Forms\Components\Select::make('daily_asset_log_id')
                ->relationship('dailyAssetLog', 'id')
                ->searchable()
                ->required(),
          Forms\Components\Select::make('ship_asset_metric_id')
    ->label('Metric')
        ->options(function ($get) {
            // If used as a sub-form in a DailyAssetLog, $get('daily_asset_log_id') or parent relation can be used
            $log = DailyAssetLog::find($get('daily_asset_log_id'));
            if (!$log) return [];
            return \App\Models\ShipAssetMetric::where('ship_asset_id', $log->ship_asset_id)
                ->get()
                ->mapWithKeys(fn($m) => [$m->id => $m->metricType->name . ($m->is_primary ? ' (Primary)' : '')]);
        })
        ->required()
        ->searchable(),
    Forms\Components\TextInput::make('value')->numeric()->required(),
        ]);
    }

    public static function table(Tables\Table $table): Tables\Table
    {
        return $table->columns([
            Tables\Columns\TextColumn::make('dailyAssetLog.id')->label('Log #'),
            Tables\Columns\TextColumn::make('metricType.name')->label('Metric'),
            Tables\Columns\TextColumn::make('shipAssetMetric.unit')->label('Unit'),
            Tables\Columns\TextColumn::make('value')->label('Value'),
            Tables\Columns\TextColumn::make('created_at')->dateTime()->sortable(),
        ])
        ->defaultSort('created_at', 'desc')
        ->actions([
            Tables\Actions\EditAction::make(),
            Tables\Actions\DeleteAction::make(),
        ])
        ->bulkActions([
            Tables\Actions\DeleteBulkAction::make(),
        ]);
    }

    public static function getRelations(): array
    {
        return [];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAssetLogMetrics::route('/'),
            'create' => Pages\CreateAssetLogMetric::route('/create'),
            'edit' => Pages\EditAssetLogMetric::route('/{record}/edit'),
        ];
    }
}
