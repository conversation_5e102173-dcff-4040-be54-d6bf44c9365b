<?php

namespace App\Filament\Resources\LocationTypeResource\Pages;

use App\Filament\Resources\LocationTypeResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateLocationType extends CreateRecord
{
    protected static string $resource = LocationTypeResource::class;
    protected static bool $canCreateAnother = false;

    //customize redirect after create
    public function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
