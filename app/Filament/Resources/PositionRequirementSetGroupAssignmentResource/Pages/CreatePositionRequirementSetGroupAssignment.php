<?php

namespace App\Filament\Resources\PositionRequirementSetGroupAssignmentResource\Pages;

use App\Filament\Resources\PositionRequirementSetGroupAssignmentResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreatePositionRequirementSetGroupAssignment extends CreateRecord
{
    protected static string $resource = PositionRequirementSetGroupAssignmentResource::class;
    protected static bool $canCreateAnother = false;

    //customize redirect after create
    public function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
