<?php

namespace App\Filament\Resources\PositionRequirementSetGroupAssignmentResource\Pages;

use App\Filament\Resources\PositionRequirementSetGroupAssignmentResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListPositionRequirementSetGroupAssignments extends ListRecords
{
    protected static string $resource = PositionRequirementSetGroupAssignmentResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
