<?php

namespace App\Filament\Resources\PositionRequirementSetGroupAssignmentResource\Pages;

use App\Filament\Resources\PositionRequirementSetGroupAssignmentResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditPositionRequirementSetGroupAssignment extends EditRecord
{
    protected static string $resource = PositionRequirementSetGroupAssignmentResource::class;

    protected function getHeaderActions(): array
    {
        return [
        ];
    }

    //customize redirect after create
    public function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
