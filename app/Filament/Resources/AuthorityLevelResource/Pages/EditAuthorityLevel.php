<?php

namespace App\Filament\Resources\AuthorityLevelResource\Pages;

use App\Filament\Resources\AuthorityLevelResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditAuthorityLevel extends EditRecord
{
    protected static string $resource = AuthorityLevelResource::class;

    protected function getHeaderActions(): array
    {
        return [
        ];
    }

    //customize redirect after create
    public function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
