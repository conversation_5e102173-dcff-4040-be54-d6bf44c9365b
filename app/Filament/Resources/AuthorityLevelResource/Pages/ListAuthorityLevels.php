<?php

namespace App\Filament\Resources\AuthorityLevelResource\Pages;

use App\Filament\Resources\AuthorityLevelResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListAuthorityLevels extends ListRecords
{
    protected static string $resource = AuthorityLevelResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
