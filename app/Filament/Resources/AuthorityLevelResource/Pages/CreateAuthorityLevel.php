<?php

namespace App\Filament\Resources\AuthorityLevelResource\Pages;

use App\Filament\Resources\AuthorityLevelResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateAuthorityLevel extends CreateRecord
{
    protected static string $resource = AuthorityLevelResource::class;
    protected static bool $canCreateAnother = false;

    //customize redirect after create
    public function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
