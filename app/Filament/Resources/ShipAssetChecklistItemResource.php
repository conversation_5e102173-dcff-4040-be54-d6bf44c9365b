<?php
namespace App\Filament\Resources;

use App\Filament\Resources\ShipAssetChecklistItemResource\Pages;
use App\Filament\Resources\ShipAssetChecklistItemResource\RelationManagers\ChecklistItemMetricsRelationManager;
use App\Models\ShipAssetChecklistItem;
use App\Models\ShipAsset;
use App\Models\AssetChecklistMaster;
use Filament\Forms;
use Filament\Resources\Resource;
use Filament\Tables;

class ShipAssetChecklistItemResource extends Resource
{
    protected static ?string $model = ShipAssetChecklistItem::class;
    protected static ?string $navigationGroup = 'Ship Management';
    protected static ?int $navigationSort = 103;
    protected static ?string $navigationIcon = 'heroicon-o-cube';

     public static function getFormSchema(): array
    {
          return [
            Forms\Components\Select::make('ship_asset_id')
                ->label('Ship Asset')
                ->options(function () {
                    return ShipAsset::with(['asset', 'ship'])
                        ->get()
                        ->mapWithKeys(function ($shipAsset) {
                            $label = $shipAsset->asset?->name ?? 'Unknown';
                            $shipName = $shipAsset->ship?->name ? ' (' . $shipAsset->ship->name . ')' : '';
                            return [$shipAsset->id => $label . $shipName];
                        });
                })
                ->searchable()
                ->required(),
            Forms\Components\Select::make('checklist_master_id')
                ->label('Checklist')
                ->options(AssetChecklistMaster::pluck('question', 'id'))
                ->required()->searchable(),
            Forms\Components\Toggle::make('is_for_survey')->label('Is for Survey'),
        ];
    }
    public static function form(Forms\Form $form): Forms\Form
    {
        return $form->schema(static::getFormSchema());
    }

    public static function table(Tables\Table $table): Tables\Table
    {
        return $table->columns([
            Tables\Columns\TextColumn::make('shipAsset.asset.name')->label('Asset')->sortable()->searchable(),
            Tables\Columns\TextColumn::make('shipAsset.ship.name')->label('Ship')->sortable()->searchable(),
            Tables\Columns\TextColumn::make('checklistMaster.question')->label('Checklist'),
            Tables\Columns\IconColumn::make('is_for_survey')->boolean(),
            Tables\Columns\TextColumn::make('created_at')->dateTime()->sortable(),
        ])->filters([])->actions([
                    Tables\Actions\EditAction::make(),
                    Tables\Actions\DeleteAction::make(),
                ])->bulkActions([
                    Tables\Actions\DeleteBulkAction::make(),
                ]);
    }

    public static function getRelations(): array
    {
        return [
            ChecklistItemMetricsRelationManager::class,
            // Add other relation managers if any
        ];
    }
    public static function getPages(): array
    {
        return [
            'index' => Pages\ListShipAssetChecklistItems::route('/'),
            'create' => Pages\CreateShipAssetChecklistItem::route('/create'),
            'edit' => Pages\EditShipAssetChecklistItem::route('/{record}/edit'),
        ];
    }
}
