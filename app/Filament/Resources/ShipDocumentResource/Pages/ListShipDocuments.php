<?php

namespace App\Filament\Resources\ShipDocumentResource\Pages;

use App\Filament\Resources\ShipDocumentResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListShipDocuments extends ListRecords
{
    protected static string $resource = ShipDocumentResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
