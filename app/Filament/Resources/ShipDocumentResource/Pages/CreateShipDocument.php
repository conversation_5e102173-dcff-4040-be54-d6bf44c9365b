<?php

namespace App\Filament\Resources\ShipDocumentResource\Pages;

use App\Filament\Resources\ShipDocumentResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateShipDocument extends CreateRecord
{
    protected static string $resource = ShipDocumentResource::class;
    protected static bool $canCreateAnother = false;

    //customize redirect after create
    public function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
