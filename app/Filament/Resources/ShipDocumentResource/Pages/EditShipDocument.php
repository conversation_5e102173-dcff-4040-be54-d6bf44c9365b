<?php

namespace App\Filament\Resources\ShipDocumentResource\Pages;

use App\Filament\Resources\ShipDocumentResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditShipDocument extends EditRecord
{
    protected static string $resource = ShipDocumentResource::class;

    protected function getHeaderActions(): array
    {
        return [
        ];
    }

    //customize redirect after create
    public function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
