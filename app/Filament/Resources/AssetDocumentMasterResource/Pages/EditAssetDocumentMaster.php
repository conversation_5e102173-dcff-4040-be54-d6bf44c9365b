<?php

namespace App\Filament\Resources\AssetDocumentMasterResource\Pages;

use App\Filament\Resources\AssetDocumentMasterResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditAssetDocumentMaster extends EditRecord
{
    protected static string $resource = AssetDocumentMasterResource::class;

    protected function getHeaderActions(): array
    {
        return [
        ];
    }

    //customize redirect after create
    public function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
