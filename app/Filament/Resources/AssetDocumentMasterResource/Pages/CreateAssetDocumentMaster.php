<?php

namespace App\Filament\Resources\AssetDocumentMasterResource\Pages;

use App\Filament\Resources\AssetDocumentMasterResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateAssetDocumentMaster extends CreateRecord
{
    protected static string $resource = AssetDocumentMasterResource::class;
    protected static bool $canCreateAnother = false;

    //customize redirect after create
    public function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
