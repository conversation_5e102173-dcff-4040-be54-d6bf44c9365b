<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CrewCertificateRequirementResource\Pages;
use App\Models\CrewCertificateRequirement;
use App\Models\CrewCertificateRequirementSet;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Forms\Components\Select;

class CrewCertificateRequirementResource extends Resource
{
    protected static ?string $model = CrewCertificateRequirement::class;
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationGroup = 'Crew Compliance';
    protected static ?string $navigationLabel = 'Crew Certificate Requirements';

    public static function form(Form $form): Form
    {
        return $form->schema([
            // Forms\Components\Select::make('requirement_set_id')
            //     ->label('Requirement Set')
            //     ->relationship('requirementSet', 'name')
            //     // ->searchable()
            //     ->nullable()
            //     ->helperText('(Optional) Use for grouping multiple certificate rules under a common set, e.g., for future advanced logic. Leave blank if not used.'),



Select::make('requirement_set_id')
    ->label('Requirement Set')
    ->options(function () {
        return CrewCertificateRequirementSet::with([
                'positions.ship',
                'positions.shipMark',
                'positions.designation',
                'groups.setGroupPositions.ship',   // deep eager load
                'groups.setGroupPositions.shipMark',
                'groups.setGroupPositions.designation',
            ])
            ->get()
            ->mapWithKeys(function ($set) {
                // Collect positions directly linked
                $positions = collect($set->positions->all());

                // Add positions from groups (indirect links)
                foreach ($set->groups as $group) {
                    // Each group can be linked to multiple positions (via position_requirement_set_group_assignments)
                    foreach ($group->setGroupPositions ?? [] as $pos) {
                        $positions->push($pos);
                    }
                }

                // Remove duplicates (by id)
                $uniquePositions = $positions->unique('id');

                // Build readable label
                $label = $set->name;
                if ($uniquePositions->count()) {
                    $label .= ' [ ' . $uniquePositions->map(function ($pos) {
                        $ship        = $pos->ship->name        ?? '(No Ship)';
                        $mark        = $pos->shipMark->name    ?? '(No Mark)';
                        $designation = $pos->designation->name ?? '(No Designation)';
                        return "{$ship} / {$mark} / {$designation}";
                    })->join(' | ') . ' ]';
                } else {
                    $label .= ' [ (Unassigned) ]';
                }
                return [$set->id => $label];
            })
            ->toArray();
    })
    ->searchable()
    ->required(),
            //   Forms\Components\Select::make('ship_crew_position_requirement_id')
            //     ->label('Crew Position Requirement')
            //     ->relationship('positionRequirement', 'id')
            //     ->getOptionLabelFromRecordUsing(fn($record) => $record->display_name)
            //     // ->searchable()
            //     ->getSearchResultsUsing(function (string $search) {
            //         return \App\Models\ShipCrewPositionRequirement::with(['ship', 'shipMark', 'designation'])
            //             ->get()
            //             ->filter(function ($item) use ($search) {
            //                 return stripos($item->display_name, $search) !== false;
            //             })
            //             ->pluck('display_name', 'id');
            //     })
            //     ->optionsLimit(20)
            //     ->required()
            //     ->helperText('Select which ship, mark (location), and crew designation this certificate is required for. (e.g., "Chief Engineer – Tug Boat – Harbour")'),

            Forms\Components\Select::make('certificate_type_id')
                ->label('Certificate Type')
                ->relationship('certificateType', 'name')
                // ->searchable()
                ->required()
                ->helperText('Choose the type of certificate required for this position (e.g., STCW, CoC).'),

            Forms\Components\Toggle::make('is_primary')
                ->label('Is Primary')
                ->helperText('If enabled, this is the most important (primary) certificate for this crew position. Usually only one should be primary.'),

            Forms\Components\Textarea::make('remarks')
                ->label('Remarks')
                ->rows(2)
                ->nullable()
                ->helperText('Any special notes or clarifications for this certificate requirement.'),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('requirementSet.name')
                    ->label('Requirement Set')
                    ->default("- Not Set -")
                    ->toggleable()
                    ->limit(18),
                Tables\Columns\TextColumn::make('positionRequirement.display_name')->label('Position Requirement')
                    ->limit(length: 100)
                    ->toggleable(),
                Tables\Columns\TextColumn::make('certificateType.name')
                    ->label('Certificate Type')
                    ->searchable(),
                Tables\Columns\IconColumn::make('is_primary')
                    ->boolean()
                    ->label('Primary'),
                Tables\Columns\TextColumn::make('remarks')
                    ->label('Remarks')
                    ->default("- Not Remarks -")
                    ->limit(30)
                    ->toggleable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Created')
                    ->since()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label('Updated')
                    ->since()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                // Add custom filters if needed
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            // Add relation managers if you add nested resources
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCrewCertificateRequirements::route('/'),
            'create' => Pages\CreateCrewCertificateRequirement::route('/create'),
            'edit' => Pages\EditCrewCertificateRequirement::route('/{record}/edit'),
        ];
    }
}
