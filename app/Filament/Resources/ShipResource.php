<?php

// app/Filament/Resources/ShipResource.php
namespace App\Filament\Resources;

use App\Filament\Resources\ShipResource\Pages;
use App\Filament\Resources\ShipResource\RelationManagers\CrewPositionsRelationManager;
use App\Models\Ship;
use App\Models\Company;
use App\Models\ShipType;
use App\Models\ShipClass;
use App\Models\MasterEntry;
use App\Models\Country;
use Filament\Forms;
use Filament\Resources\Resource;
use Filament\Tables;
use Illuminate\Database\Eloquent\Builder;
use \App\Filament\RelationManagers\CertificatesRelationManager;

class ShipResource extends Resource
{
    protected static ?string $model = Ship::class;
    protected static ?string $navigationGroup = 'Ship Management';

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?int $navigationSort = 100;

    public static function form(Forms\Form $form): Forms\Form
    {
        return $form->schema([
            Forms\Components\Section::make('Basic Info')->schema([
                Forms\Components\TextInput::make('imo_number')
                    ->label('IMO Number')
                    ->required()
                    ->unique(ignoreRecord: true),
                Forms\Components\TextInput::make('official_number')
                    ->label('Official Number'),
                Forms\Components\TextInput::make('name')
                    ->label('Ship Name')
                    ->required()
                    ->maxLength(150),
                Forms\Components\TextInput::make('call_sign')
                    ->label('Call Sign')
                    ->maxLength(50),
            ])->columns(2),

            Forms\Components\Section::make('Company & Classification')->schema([
                Forms\Components\Select::make('company_id')
                    ->label('Company')
                    ->options(Company::pluck('name', 'id'))
                    ->searchable()
                    ->required(),
                Forms\Components\Select::make('ship_type_id')
                    ->label('Ship Type')
                    ->options(ShipType::pluck('name', 'id'))
                    ->searchable()
                    ->required(),
                Forms\Components\Select::make('ship_class_id')
                    ->label('Ship Class')
                    ->options(ShipClass::pluck('name', 'id'))
                    ->searchable()
                    ->required(),
            ])->columns(3),

            Forms\Components\Section::make('Dimensions')->schema([
                Forms\Components\TextInput::make('length_overall')->numeric()->label('Length Overall'),
                Forms\Components\TextInput::make('breadth')->numeric()->label('Breadth'),
                Forms\Components\TextInput::make('depth')->numeric()->label('Depth'),
                Forms\Components\TextInput::make('draft')->numeric()->label('Draft'),
                Forms\Components\TextInput::make('gross_tonnage')->numeric()->label('Gross Tonnage'),
                Forms\Components\TextInput::make('net_tonnage')->numeric()->label('Net Tonnage'),
                Forms\Components\TextInput::make('deadweight')->numeric()->label('Deadweight'),
            ])->columns(3),

            Forms\Components\Section::make('Construction Details')->schema([
                Forms\Components\TextInput::make('year_built')->numeric()->maxLength(4)->label('Year Built'),
                Forms\Components\TextInput::make('builder')->label('Builder'),
                Forms\Components\TextInput::make('hull_number')->label('Hull Number'),
            ])->columns(3),

            Forms\Components\Section::make('Status & Flags')->schema([
                Forms\Components\Select::make('status_id')
                    ->label('Status')
                    ->options(fn() => MasterEntry::whereHas('type', fn($q) => $q->where('name', 'ship_status'))->pluck('name', 'id'))
                    ->required(),
                Forms\Components\DatePicker::make('commission_date')->label('Commission Date'),
                Forms\Components\DatePicker::make('decommission_date')->label('Decommission Date'),
                Forms\Components\Select::make('flag_id')
                    ->label('Flag (Country)')
                    ->options(Country::pluck('name', 'id'))
                    ->searchable(),
                Forms\Components\TextInput::make('port_of_registry')->label('Port of Registry'),
            ])->columns(2),

            Forms\Components\Section::make('Additional Notes')->schema([
                Forms\Components\Textarea::make('notes')->columnSpanFull()->rows(4),
            ]),
        ]);
    }


    public static function table(Tables\Table $table): Tables\Table
    {
        return $table->columns([
            Tables\Columns\TextColumn::make('imo_number')->sortable()->searchable(),
            Tables\Columns\TextColumn::make('name')->sortable()->searchable(),
            Tables\Columns\TextColumn::make('shipType.name')->label('Type')->sortable(),
            Tables\Columns\TextColumn::make('shipClass.name')->label('Class')->sortable(),
            Tables\Columns\TextColumn::make('company.name')->label('Company')->sortable(),
            Tables\Columns\TextColumn::make('status.name')->label('Status')->sortable(),
            Tables\Columns\TextColumn::make('created_at')->dateTime()->sortable(),
        ])->filters([])->actions([
            Tables\Actions\EditAction::make(),
            Tables\Actions\DeleteAction::make(),
        ])->bulkActions([
            Tables\Actions\DeleteBulkAction::make(),
        ]);
    }

    public static function getRelations(): array
    {
        return [
              CrewPositionsRelationManager::class,
              CertificatesRelationManager::class,
        ];
    }
    public static function getPages(): array
    {
        return [
            'index' => Pages\ListShips::route('/'),
            'create' => Pages\CreateShip::route('/create'),
            'edit' => Pages\EditShip::route('/{record}/edit'),
        ];
    }
}
