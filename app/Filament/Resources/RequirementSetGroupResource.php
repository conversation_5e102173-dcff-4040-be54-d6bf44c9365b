<?php

namespace App\Filament\Resources;

use App\Filament\Resources\RequirementSetGroupResource\Pages;
use App\Models\RequirementSetGroup;
use App\Models\CrewCertificateRequirementSet;
use Filament\Forms;
use Filament\Tables;
use Filament\Resources\Resource;

class RequirementSetGroupResource extends Resource
{
    protected static ?string $model = RequirementSetGroup::class;
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationLabel = 'Requirement Set Groups';
    protected static ?string $navigationGroup = 'Crew Compliance';

    public static function form(Forms\Form $form): Forms\Form
    {
        return $form->schema([
            Forms\Components\TextInput::make('name')
                ->label('Group Name')
                ->required()
                ->maxLength(191),
            Forms\Components\Textarea::make('description')
                ->label('Description')
                ->maxLength(500),
            Forms\Components\Select::make('sets')
                ->label('Requirement Sets (AND logic within group)')
                ->relationship('sets', 'name')
                ->preload()
                ->multiple()
                ->required()
                ->helperText('Add all sets required together in this group.
                    <br>
                    <b>For positions assigned multiple groups: Crew can qualify by any one group (OR logic between groups).</b>
                    <br>
                    <b>If all certificates are mandatory, create one set with all required certificates and assign only that set to the group.</b>')
                ->columnSpanFull(),
        ]);
    }

    public static function table(Tables\Table $table): Tables\Table
    {
        return $table->columns([
            Tables\Columns\TextColumn::make('name')
                ->sortable()
                ->searchable()
                ->label('Group'),
            Tables\Columns\TextColumn::make('description')
                ->label('Description')
                ->limit(30),
            Tables\Columns\TextColumn::make('sets.name')
    ->label('Requirement Sets')
    ->listWithLineBreaks()
    ->bulleted()
    ->limitList(5)
    ->expandableLimitedList(),

            Tables\Columns\TextColumn::make('created_at')
                ->label('Created')
                ->dateTime('d-M-Y'),
        ]);
    }

    public static function getPages(): array
    {
        return [
            'index'  => Pages\ListRequirementSetGroups::route('/'),
            'create' => Pages\CreateRequirementSetGroup::route('/create'),
            'edit'   => Pages\EditRequirementSetGroup::route('/{record}/edit'),
        ];
    }
}
