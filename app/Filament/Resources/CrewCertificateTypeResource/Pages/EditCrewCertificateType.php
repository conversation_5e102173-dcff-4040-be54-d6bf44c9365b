<?php

namespace App\Filament\Resources\CrewCertificateTypeResource\Pages;

use App\Filament\Resources\CrewCertificateTypeResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditCrewCertificateType extends EditRecord
{
    protected static string $resource = CrewCertificateTypeResource::class;

    protected function getHeaderActions(): array
    {
        return [
        ];
    }

    //customize redirect after create
    public function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
