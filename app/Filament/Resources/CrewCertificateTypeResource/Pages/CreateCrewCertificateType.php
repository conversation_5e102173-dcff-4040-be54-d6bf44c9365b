<?php

namespace App\Filament\Resources\CrewCertificateTypeResource\Pages;

use App\Filament\Resources\CrewCertificateTypeResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateCrewCertificateType extends CreateRecord
{
    protected static string $resource = CrewCertificateTypeResource::class;
    protected static bool $canCreateAnother = false;

    //customize redirect after create
    public function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
