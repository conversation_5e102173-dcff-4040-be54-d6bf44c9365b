<?php

namespace App\Filament\Resources\CrewCertificateTypeResource\Pages;

use App\Filament\Resources\CrewCertificateTypeResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListCrewCertificateTypes extends ListRecords
{
    protected static string $resource = CrewCertificateTypeResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
