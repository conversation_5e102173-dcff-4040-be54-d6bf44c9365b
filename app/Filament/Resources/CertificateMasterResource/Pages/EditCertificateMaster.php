<?php

namespace App\Filament\Resources\CertificateMasterResource\Pages;

use App\Filament\Resources\CertificateMasterResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditCertificateMaster extends EditRecord
{
    protected static string $resource = CertificateMasterResource::class;

    protected function getHeaderActions(): array
    {
        return [
        ];
    }

    //customize redirect after create
    public function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
