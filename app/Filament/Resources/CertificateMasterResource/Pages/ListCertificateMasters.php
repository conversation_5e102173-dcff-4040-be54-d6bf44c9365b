<?php

namespace App\Filament\Resources\CertificateMasterResource\Pages;

use App\Filament\Resources\CertificateMasterResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListCertificateMasters extends ListRecords
{
    protected static string $resource = CertificateMasterResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
