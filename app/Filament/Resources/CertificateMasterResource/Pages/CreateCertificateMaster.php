<?php

namespace App\Filament\Resources\CertificateMasterResource\Pages;

use App\Filament\Resources\CertificateMasterResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateCertificateMaster extends CreateRecord
{
    protected static string $resource = CertificateMasterResource::class;
    protected static bool $canCreateAnother = false;

    //customize redirect after create
    public function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
