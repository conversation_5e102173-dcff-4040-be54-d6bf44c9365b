<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CertificateMasterResource\Pages;
use App\Filament\Resources\CertificateMasterResource\RelationManagers;
use App\Models\CertificateMaster;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use App\Models\ShipClass;
use App\Models\ShipType;
use App\Models\MasterEntry;
use App\Models\MasterType;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Forms\Components\Actions\CreateOptionAction;

class CertificateMasterResource extends Resource
{
    protected static ?string $model = CertificateMaster::class;

    protected static ?string $navigationGroup = 'Ship Management';
    protected static ?int $navigationSort = 70;
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->label('Certificate Name')
                    ->required()
                    ->maxLength(255),

                Forms\Components\Textarea::make('description')
                    ->label('Description')
                    ->columnSpanFull(),

                Forms\Components\Toggle::make('is_mandatory')
                    ->label('Mandatory')
                    ->default(true),

                Forms\Components\Toggle::make('requires_renewal')
                    ->label('Requires Renewal')
                    ->default(true),

                Forms\Components\Select::make('ship_type_ids')
                    ->label('Applicable Ship Types')
                    ->multiple()
                    ->searchable()
                    ->options(ShipType::pluck('name', 'id'))
                    ->suffixAction(
                        fn() => Forms\Components\Actions\Action::make('createShipType')
                            ->label('Add New')
                            ->icon('heroicon-m-plus')
                            ->url(route('filament.admin.resources.ship-types.create')) // opens in new tab
                            ->openUrlInNewTab()
                    )

                    ->helperText('Optional – leave empty if applies to all'),

                Forms\Components\Select::make('ship_class_ids')
                    ->label('Applicable Ship Classes')
                    ->multiple()
                    ->searchable()
                    ->options(ShipClass::pluck('name', 'id'))
                    ->suffixAction(
                        fn() => Forms\Components\Actions\Action::make('createShipClass')
                            ->label('Add New')
                            ->icon('heroicon-m-plus')
                            ->url(route('filament.admin.resources.ship-classes.create')) // opens in new tab
                            ->openUrlInNewTab()
                    )

                    ->helperText('Optional – leave empty if applies to all'),

                Forms\Components\Group::make([
                    Forms\Components\TextInput::make('validity_value')
                        ->label('Validity Duration')
                        ->numeric()
                        ->minValue(1)
                        ->suffix('Unit'),

                    Forms\Components\Select::make('validity_unit_id')
                        ->label('Validity Unit')
                        ->options(fn() => MasterEntry::whereHas(
                            'type',
                            fn($q) =>
                            $q->where('name', 'validity_unit')
                        )->pluck('name', 'id'))
                        ->searchable()
                        ->requiredWith('validity_value'),
                ])
                    ->columns(2)
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Certificate Name')
                    ->searchable(),

                Tables\Columns\IconColumn::make('is_mandatory')
                    ->label('Mandatory')
                    ->boolean(),

                Tables\Columns\IconColumn::make('requires_renewal')
                    ->label('Renewal')
                    ->boolean(),

                Tables\Columns\TextColumn::make('validity_value')
                    ->label('Validity'),

                Tables\Columns\TextColumn::make('validityUnit.name')
                    ->label('Unit'),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Created')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label('Updated')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),

            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCertificateMasters::route('/'),
            'create' => Pages\CreateCertificateMaster::route('/create'),
            'edit' => Pages\EditCertificateMaster::route('/{record}/edit'),
        ];
    }
}
