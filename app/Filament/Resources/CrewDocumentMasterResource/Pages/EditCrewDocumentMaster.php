<?php

namespace App\Filament\Resources\CrewDocumentMasterResource\Pages;

use App\Filament\Resources\CrewDocumentMasterResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditCrewDocumentMaster extends EditRecord
{
    protected static string $resource = CrewDocumentMasterResource::class;

    protected function getHeaderActions(): array
    {
        return [
        ];
    }

    //customize redirect after create
    public function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
