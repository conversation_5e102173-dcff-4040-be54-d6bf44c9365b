<?php

namespace App\Filament\Resources\CrewDocumentMasterResource\Pages;

use App\Filament\Resources\CrewDocumentMasterResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateCrewDocumentMaster extends CreateRecord
{
    protected static string $resource = CrewDocumentMasterResource::class;
    protected static bool $canCreateAnother = false;

    //customize redirect after create
    public function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
