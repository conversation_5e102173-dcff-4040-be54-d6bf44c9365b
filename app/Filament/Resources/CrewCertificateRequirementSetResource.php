<?php


namespace App\Filament\Resources;

use App\Filament\Resources\CrewCertificateRequirementSetResource\Pages;
use App\Models\CrewCertificateRequirementSet;
use App\Models\ShipCrewPositionRequirement;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class CrewCertificateRequirementSetResource extends Resource
{
    protected static ?string $model = CrewCertificateRequirementSet::class;
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $navigationGroup = 'Crew Compliance';

    public static function form(Form $form): Form
    {
        return $form->schema([
            Forms\Components\Select::make('ship_crew_position_requirement_id')
                ->label('Crew Position Requirement')
                ->relationship('positionRequirement', 'id')
                ->getOptionLabelFromRecordUsing(fn($record) => $record->display_name)
                ->searchable()
                ->getSearchResultsUsing(function (string $search) {
                    return \App\Models\ShipCrewPositionRequirement::with(['ship', 'shipMark', 'designation'])
                        ->get()
                        ->filter(function ($item) use ($search) {
                            return stripos($item->display_name, $search) !== false;
                        })
                        ->pluck('display_name', 'id');
                })
                ->optionsLimit(20)
                ->required(),

            Forms\Components\Radio::make('logic_type')
                ->label('Requirement Logic')
                ->options([
                    'ALL' => 'All of these are required',
                    'ANY' => 'Any N are required',
                ])
                // ->inline()
                ->required()
                ->reactive()
                ->default('ALL'),





            Forms\Components\TextInput::make('name')
                ->label('Set Name')
                ->required(),

            Forms\Components\Textarea::make('description')
                ->rows(2)
                ->nullable(),

            Forms\Components\TextInput::make('min_required')
                ->label('Min Certificates Required')
                ->numeric()
                ->default(0)
                ->nullable(),


        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('positionRequirement.ship.name')->label('Ship'),
                Tables\Columns\TextColumn::make('positionRequirement.shipMark.name')->label('Mark'),
                Tables\Columns\TextColumn::make('positionRequirement.designation.name')->label('Designation'),
                Tables\Columns\TextColumn::make('positionRequirement.display_name')->label('Position Requirement'),
                Tables\Columns\TextColumn::make('name')->label('Set Name')->sortable()->searchable(),
                Tables\Columns\BadgeColumn::make('logic_type')
                    ->label('Logic')
                    ->colors([
                        'primary' => 'ALL',
                        'success' => 'ANY',
                    ])
                    ->formatStateUsing(fn($state) => $state === 'ALL' ? 'All' : 'Any'),
                Tables\Columns\TextColumn::make('description')->limit(40)->default("-"),
                Tables\Columns\TextColumn::make('min_required')->default(0),

                Tables\Columns\TextColumn::make('created_at')->dateTime()->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')->dateTime()->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            // Add RelationManagers here, e.g. to manage certificate requirements within a set.
            // CrewCertificateRequirementsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCrewCertificateRequirementSets::route('/'),
            'create' => Pages\CreateCrewCertificateRequirementSet::route('/create'),
            'edit' => Pages\EditCrewCertificateRequirementSet::route('/{record}/edit'),
        ];
    }
}
