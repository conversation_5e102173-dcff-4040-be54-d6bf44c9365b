<?php

namespace App\Filament\Resources\PositionRequirementSetAssignmentResource\Pages;

use App\Filament\Resources\PositionRequirementSetAssignmentResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreatePositionRequirementSetAssignment extends CreateRecord
{
    protected static string $resource = PositionRequirementSetAssignmentResource::class;
    protected static bool $canCreateAnother = false;

    //customize redirect after create
    public function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
