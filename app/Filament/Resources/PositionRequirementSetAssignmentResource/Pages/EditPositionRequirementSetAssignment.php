<?php

namespace App\Filament\Resources\PositionRequirementSetAssignmentResource\Pages;

use App\Filament\Resources\PositionRequirementSetAssignmentResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditPositionRequirementSetAssignment extends EditRecord
{
    protected static string $resource = PositionRequirementSetAssignmentResource::class;

    protected function getHeaderActions(): array
    {
        return [
        ];
    }

    //customize redirect after create
    public function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
