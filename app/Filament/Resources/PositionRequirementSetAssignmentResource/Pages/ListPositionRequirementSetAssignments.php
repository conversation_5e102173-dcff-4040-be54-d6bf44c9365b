<?php

namespace App\Filament\Resources\PositionRequirementSetAssignmentResource\Pages;

use App\Filament\Resources\PositionRequirementSetAssignmentResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListPositionRequirementSetAssignments extends ListRecords
{
    protected static string $resource = PositionRequirementSetAssignmentResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
