<?php

namespace App\Filament\Resources;

use App\Filament\Resources\AuthorityLevelResource\Pages;
use App\Models\AuthorityLevel;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use  App\Filament\Pages\AssignAuthorityPermissions;

class AuthorityLevelResource extends Resource
{
    protected static ?string $model = AuthorityLevel::class;

    protected static ?string $navigationIcon = 'heroicon-o-lock-closed';
    protected static ?string $navigationGroup = 'Access Control';
    protected static ?string $navigationLabel = 'Authority Levels';
    protected static ?int $navigationSort = 10;

    public static function form(Form $form): Form
    {
        return $form->schema([
            Forms\Components\TextInput::make('name')
                ->label('Authority Level Name')
                ->placeholder('e.g. Captain, Engineer, Master')
                ->required()
                ->unique(AuthorityLevel::class, 'name', ignoreRecord: true)
                ->maxLength(255),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->sortable()
                    ->searchable()
                    ->label('Name')
                    ->badge(),

                Tables\Columns\TextColumn::make('permissions_count')
                    ->counts('permissions')
                    ->label('Total Permissions')
                    ->color('info')
                    ->sortable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Created')
                    ->dateTime('d M Y, H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label('Last Updated')
                    ->since()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                // Add filters if needed later
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),

                Tables\Actions\Action::make('assignPermissions')
                    ->label('Permissions')
                    ->icon('heroicon-o-key')
                    ->color('info')
                    ->url(fn ($record) =>
                        AssignAuthorityPermissions::getUrl(['record' => $record->id])
            ),
                    // ->openUrlInNewTab(),
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            // You can add RelationManagers if linking to permissions, users, etc.
        ];
    }

    public static function getPages(): array
    {
        return [
            'index'  => Pages\ListAuthorityLevels::route('/'),
            'create' => Pages\CreateAuthorityLevel::route('/create'),
            'edit'   => Pages\EditAuthorityLevel::route('/{record}/edit'),
        ];
    }
}
