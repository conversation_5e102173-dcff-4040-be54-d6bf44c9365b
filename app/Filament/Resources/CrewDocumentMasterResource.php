<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CrewDocumentMasterResource\Pages;
use App\Filament\Resources\CrewDocumentMasterResource\RelationManagers;
use App\Models\CrewDocumentMaster;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Models\Designation;
use App\Models\MasterEntry;
use App\Models\MasterType;

class CrewDocumentMasterResource extends Resource
{
    protected static ?string $model = CrewDocumentMaster::class;

     protected static ?string $navigationGroup = 'Crew';
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->required()
                    ->maxLength(100)
                    ->label('Document Name'),

                Forms\Components\Textarea::make('description')
                    ->label('Description')
                    ->columnSpanFull(),

                Forms\Components\Toggle::make('is_mandatory')
                    ->label('Is Mandatory')
                    ->default(true),

                Forms\Components\Group::make([
                    Forms\Components\TextInput::make('validity_value')
                        ->label('Validity Duration')
                        ->numeric()
                        ->minValue(1),

                    Forms\Components\Select::make('validity_unit_id')
                        ->label('Validity Unit')
                        ->options(fn() => MasterEntry::whereHas(
                            'type',
                            fn($q) =>
                            $q->where('name', 'validity_unit')
                        )->pluck('name', 'id'))
                        ->searchable()
                        ->requiredWith('validity_value'),
                ])->columns(2)->columnSpanFull()
                    ->visible(fn(Forms\Get $get) => $get('is_mandatory')),


                Forms\Components\Select::make('designation_ids')
                    ->label('Designations')
                    ->multiple()
                    ->searchable()
                    ->options(Designation::pluck('name', 'id'))
                    ->placeholder('Leave empty = applicable to all'),


                Forms\Components\CheckboxList::make('event_triggers')
                    ->label('Event Triggers')
                    ->options(function () {
                        $typeId = MasterType::where('name', 'crew_event_type')->value('id');
                        return MasterEntry::where('master_type_id', $typeId)->pluck('name', 'code');
                    })
                    ->columns(3)
                    ->searchable(),

                Forms\Components\TextInput::make('file_types')
                    ->label('Allowed File Types')
                    ->helperText('Comma-separated: pdf, jpg, png etc.')
                    ->placeholder('e.g. pdf,jpg,png')
                    ->maxLength(100),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')->label('Document Name')->searchable(),
                Tables\Columns\IconColumn::make('is_mandatory')->label('Mandatory')->boolean(),
                Tables\Columns\TextColumn::make('validity_period')->label('Validity'),
                Tables\Columns\TextColumn::make('file_types')->label('File Types')->wrap(),

                Tables\Columns\TextColumn::make('created_at')->label('Created')->dateTime()->sortable()->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')->label('Updated')->dateTime()->sortable()->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),

            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCrewDocumentMasters::route('/'),
            'create' => Pages\CreateCrewDocumentMaster::route('/create'),
            'edit' => Pages\EditCrewDocumentMaster::route('/{record}/edit'),
        ];
    }
}
