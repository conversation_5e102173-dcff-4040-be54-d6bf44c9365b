<?php

namespace App\Filament\Resources\ClassificationSocietyResource\Pages;

use App\Filament\Resources\ClassificationSocietyResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListClassificationSocieties extends ListRecords
{
    protected static string $resource = ClassificationSocietyResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
