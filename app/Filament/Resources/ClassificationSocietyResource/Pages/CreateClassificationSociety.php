<?php

namespace App\Filament\Resources\ClassificationSocietyResource\Pages;

use App\Filament\Resources\ClassificationSocietyResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateClassificationSociety extends CreateRecord
{
    protected static string $resource = ClassificationSocietyResource::class;
    protected static bool $canCreateAnother = false;

    //customize redirect after create
    public function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
