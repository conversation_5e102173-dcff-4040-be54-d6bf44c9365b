<?php

namespace App\Filament\Resources\ClassificationSocietyResource\Pages;

use App\Filament\Resources\ClassificationSocietyResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditClassificationSociety extends EditRecord
{
    protected static string $resource = ClassificationSocietyResource::class;

    protected function getHeaderActions(): array
    {
        return [
        ];
    }

    //customize redirect after create
    public function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
