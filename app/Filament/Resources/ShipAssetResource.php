<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ShipAssetResource\Pages;
use App\Filament\Resources\ShipAssetResource\RelationManagers\ChecklistItemsRelationManager;
use App\Filament\Resources\SpecsResource\RelationManagers\SpecsRelationManager;
use App\Filament\Resources\ShipAssetResource\RelationManagers\MetricsRelationManager;
use App\Filament\RelationManagers\CertificatesRelationManager;
use App\Models\Asset;
use App\Models\AssetSpecKey;
use App\Models\MasterEntry;
use App\Models\ShipAsset;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class ShipAssetResource extends Resource
{
    protected static ?string $model = ShipAsset::class;

         protected static ?string $navigationGroup = 'Ship Management';
    protected static ?string $navigationIcon = 'heroicon-o-cube';

    protected static ?int $navigationSort = 101;

    public static function form(Form $form): Form
    {
        return $form->schema([
            Forms\Components\Select::make('ship_id')
                ->relationship('ship', 'name')
                ->searchable()
                ->required(),
            Forms\Components\Select::make('location_id')
                ->relationship('location', 'name')
                ->nullable()
                ->searchable(),

            Forms\Components\Select::make('asset_id')
                ->relationship('asset', 'name')
                ->searchable()
                ->required()
                ->reactive()
                ->afterStateUpdated(function ($state, Set $set, Get $get) {
                    // Fetch all columns from selected asset, and set corresponding ship asset fields
                    $asset = Asset::find($state);
                    if ($asset) {

                        $set('is_compliance', $asset->is_compliance);
                        $set('is_fire_safety', $asset->is_fire_safety);
                        $set('survey_required', $asset->survey_required);
                        $set('override_spec', collect($asset->default_spec)->map(
                            fn($spec) => [
                                'key' => $spec['key'],
                                'value' => $spec['value'],
                            ]
                        )->values()->all());
                        // $set('override_image', $asset->image); // This can copy the path, or leave null for new upload
                    }
                }),



            Forms\Components\Repeater::make('override_spec')
                ->label('Specifications (Override or add)')
                ->schema([
                    Forms\Components\Select::make('key')
                        ->label('Key')
                        ->options(fn() => AssetSpecKey::all()->pluck('key', 'key'))
                        ->searchable()
                        ->required(),
                    Forms\Components\TextInput::make('value')->label('Value')->required(),
                ])
                ->columns(2)
                ->collapsible()
                ->addActionLabel('Add Specification')
                ->default([])
                ->dehydrated(true),

            Forms\Components\FileUpload::make('override_image')
                ->label('Asset Image')
                ->image()
                ->directory('ship-assets/images')
                ->nullable(),

            Forms\Components\Select::make('primary_metric_type_id')
                ->label('Primary Metric Type')
                ->options(\App\Models\MetricType::pluck('name', 'id'))
                ->searchable()
                ->nullable()
                ->helperText('Determines which metric from logs is used as the “Current Level” for this asset.'),

            Forms\Components\TextInput::make('current_level')->label('Current Level')->numeric()->nullable(),
            Forms\Components\TextInput::make('min_level')->label('Min Level')->numeric()->nullable(),
            Forms\Components\TextInput::make('max_level')->label('Max Level')->numeric()->nullable(),

            Forms\Components\TextInput::make('quantity')->numeric()->default(0),
            Forms\Components\Textarea::make('notes')->nullable(),
            Forms\Components\Toggle::make('is_compliance')->label('Compliance Asset')->default(false),
            Forms\Components\Toggle::make('is_fire_safety')->label('Fire Safety')->default(false),
            Forms\Components\Toggle::make('survey_required')->label('Survey Required')->default(true),
            Forms\Components\Hidden::make('created_by')
                ->default(fn () => auth()->user()?->id)
                ->dehydrated(),
            Forms\Components\Select::make('status_id')
                ->label('Status')
                ->options(
                    fn() => MasterEntry::whereHas('type', fn ($q) => $q->where('name', 'asset_status'))->pluck('name', 'id')
                )
                ->searchable()
                ->nullable(),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table->columns([
            Tables\Columns\TextColumn::make('ship.name')->label('Ship')->sortable(),
            Tables\Columns\TextColumn::make('asset.name')->label('Asset')->sortable(),
            Tables\Columns\TextColumn::make('location.name')->label('Location')->sortable(),
            Tables\Columns\ImageColumn::make('override_image')
            ->label('Asset Image')
            ->disk('public')
            ->circular()
            ->size(40),
            Tables\Columns\TextColumn::make('quantity')->sortable(),
            Tables\Columns\IconColumn::make('is_compliance')->boolean(),
            Tables\Columns\IconColumn::make('is_fire_safety')->boolean(),
            Tables\Columns\IconColumn::make('survey_required')->boolean(),
            Tables\Columns\TextColumn::make('status.name')->label('Status')->sortable(),
            Tables\Columns\TextColumn::make('created_at')->dateTime()->sortable(),
        ])
        ->defaultSort('created_at', 'desc')
        ->actions([
            Tables\Actions\EditAction::make(),
            Tables\Actions\DeleteAction::make(),
        ])
        ->bulkActions([
            Tables\Actions\DeleteBulkAction::make(),
        ]);
    }

    public static function getRelations(): array
    {
        return [
           ChecklistItemsRelationManager::class,
           SpecsRelationManager::class,
           CertificatesRelationManager::class,
           MetricsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListShipAssets::route('/'),
            'create' => Pages\CreateShipAsset::route('/create'),
            'edit' => Pages\EditShipAsset::route('/{record}/edit'),
        ];
    }
}
