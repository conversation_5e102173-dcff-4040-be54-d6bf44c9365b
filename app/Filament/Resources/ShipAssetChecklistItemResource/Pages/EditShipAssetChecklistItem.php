<?php

namespace App\Filament\Resources\ShipAssetChecklistItemResource\Pages;

use App\Filament\Resources\ShipAssetChecklistItemResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditShipAssetChecklistItem extends EditRecord
{
    protected static string $resource = ShipAssetChecklistItemResource::class;

    protected function getHeaderActions(): array
    {
        return [
        ];
    }

    //customize redirect after create
    public function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
