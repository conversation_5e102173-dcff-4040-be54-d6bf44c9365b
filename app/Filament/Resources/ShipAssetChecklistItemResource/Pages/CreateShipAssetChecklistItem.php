<?php

namespace App\Filament\Resources\ShipAssetChecklistItemResource\Pages;

use App\Filament\Resources\ShipAssetChecklistItemResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateShipAssetChecklistItem extends CreateRecord
{
    protected static string $resource = ShipAssetChecklistItemResource::class;
    protected static bool $canCreateAnother = false;

    //customize redirect after create
    public function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
