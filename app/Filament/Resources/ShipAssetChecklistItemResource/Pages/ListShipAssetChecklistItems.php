<?php

namespace App\Filament\Resources\ShipAssetChecklistItemResource\Pages;

use App\Filament\Resources\ShipAssetChecklistItemResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListShipAssetChecklistItems extends ListRecords
{
    protected static string $resource = ShipAssetChecklistItemResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
