<?php

namespace App\Filament\Resources\ShipAssetChecklistItemResource\RelationManagers;

use Filament\Forms;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;

class ChecklistItemMetricsRelationManager extends RelationManager
{
    protected static string $relationship = 'metrics';

    public function form(Forms\Form $form): Forms\Form
    {
        return $form->schema([
            Forms\Components\Select::make('metric_type_id')
                ->label('Metric Type')
                ->relationship('metricType', 'name')
                ->required()
                ->searchable(),
            Forms\Components\Toggle::make('is_required')->default(true),
            Forms\Components\TextInput::make('order')->numeric()->nullable(),
            Forms\Components\Textarea::make('notes')->nullable(),
        ]);
    }

    public function table(Tables\Table $table): Tables\Table
    {
        return $table->columns([
            Tables\Columns\TextColumn::make('metricType.name')->label('Metric'),
            Tables\Columns\IconColumn::make('is_required')->boolean(),
            Tables\Columns\TextColumn::make('order'),
            Tables\Columns\TextColumn::make('notes'),
            Tables\Columns\TextColumn::make('created_at')->dateTime()->sortable(),
        ])
        ->actions([
            Tables\Actions\EditAction::make(),
            Tables\Actions\DeleteAction::make(),
        ])
        ->headerActions([
            Tables\Actions\CreateAction::make(),
        ])
        ->bulkActions([
            Tables\Actions\DeleteBulkAction::make(),
        ]);
    }
}
