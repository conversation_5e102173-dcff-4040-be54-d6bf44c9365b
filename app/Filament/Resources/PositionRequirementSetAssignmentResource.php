<?php
// app/Filament/Resources/PositionRequirementSetAssignmentResource.php
// app/Filament/Resources/PositionRequirementSetAssignmentResource.php

namespace App\Filament\Resources;

use App\Filament\Resources\PositionRequirementSetAssignmentResource\Pages;
use App\Models\PositionRequirementSetAssignment;
use App\Models\ShipCrewPositionRequirement;
use App\Models\CrewCertificateRequirementSet;
use Filament\Resources\Resource;
use Filament\Forms;
use Filament\Tables;

class PositionRequirementSetAssignmentResource extends Resource
{
    protected static ?string $model = PositionRequirementSetAssignment::class;
    protected static ?string $navigationIcon = 'heroicon-o-link';
    protected static ?string $navigationLabel = 'Assign Sets to Position';
    protected static ?string $navigationGroup = 'Crew Compliance';

    public static function form(Forms\Form $form): Forms\Form
    {
        return $form->schema([
            Forms\Components\Select::make('ship_crew_position_requirement_id')
                ->label('Ship/Mark/Designation')
                ->options(
                    ShipCrewPositionRequirement::with(['ship', 'shipMark', 'designation'])->get()->mapWithKeys(
                        fn ($p) => [
                            $p->id => $p->ship->name
                                . ' - ' . $p->shipMark->name
                                . ' - ' . $p->designation->name
                                . " (min: {$p->min_required}, max: {$p->max_allowed})"
                        ]
                    )->toArray()
                )
                ->searchable()
                ->required(),

            Forms\Components\Select::make('requirement_set_id')
                ->label('Requirement Set')
                ->options(
                    CrewCertificateRequirementSet::pluck('name', 'id')->toArray()
                )
                ->searchable()
                ->required(),
        ]);
    }

    public static function table(Tables\Table $table): Tables\Table
    {
        return $table->columns([
            Tables\Columns\TextColumn::make('position')
                ->label('Ship / Mark / Designation')
                ->formatStateUsing(function ($record) {
                    $pos = $record->position;
                    return $pos
                        ? $pos->ship->name . ' - ' . $pos->shipMark->name . ' - ' . $pos->designation->name
                        : '-';
                }),
            Tables\Columns\TextColumn::make('requirementSet.name')
                ->label('Requirement Set'),
        ]);
    }

    public static function getPages(): array
    {
        return [
            'index'  => Pages\ListPositionRequirementSetAssignments::route('/'),
            'create' => Pages\CreatePositionRequirementSetAssignment::route('/create'),
            'edit'   => Pages\EditPositionRequirementSetAssignment::route('/{record}/edit'),
        ];
    }
}
