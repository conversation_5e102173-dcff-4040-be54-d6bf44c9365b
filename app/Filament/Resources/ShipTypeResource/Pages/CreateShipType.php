<?php

namespace App\Filament\Resources\ShipTypeResource\Pages;

use App\Filament\Resources\ShipTypeResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateShipType extends CreateRecord
{
    protected static string $resource = ShipTypeResource::class;
    protected static bool $canCreateAnother = false;

    //customize redirect after create
    public function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
