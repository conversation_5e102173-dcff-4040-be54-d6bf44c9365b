<?php

namespace App\Filament\Resources\CrewCertificateResource\Pages;

use App\Filament\Resources\CrewCertificateResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateCrewCertificate extends CreateRecord
{
    protected static string $resource = CrewCertificateResource::class;
    protected static bool $canCreateAnother = false;

    //customize redirect after create
    public function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
