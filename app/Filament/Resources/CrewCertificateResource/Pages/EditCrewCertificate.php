<?php

namespace App\Filament\Resources\CrewCertificateResource\Pages;

use App\Filament\Resources\CrewCertificateResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditCrewCertificate extends EditRecord
{
    protected static string $resource = CrewCertificateResource::class;

    protected function getHeaderActions(): array
    {
        return [
        ];
    }

    //customize redirect after create
    public function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
