<?php

namespace App\Filament\Resources\AssetLifecycleLogResource\Pages;

use App\Filament\Resources\AssetLifecycleLogResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateAssetLifecycleLog extends CreateRecord
{
    protected static string $resource = AssetLifecycleLogResource::class;
    protected static bool $canCreateAnother = false;

    //customize redirect after create
    public function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
