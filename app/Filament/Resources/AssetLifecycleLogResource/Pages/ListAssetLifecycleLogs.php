<?php

namespace App\Filament\Resources\AssetLifecycleLogResource\Pages;

use App\Filament\Resources\AssetLifecycleLogResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListAssetLifecycleLogs extends ListRecords
{
    protected static string $resource = AssetLifecycleLogResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
