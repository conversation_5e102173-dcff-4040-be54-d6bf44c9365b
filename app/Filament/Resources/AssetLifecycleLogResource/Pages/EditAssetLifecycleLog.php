<?php

namespace App\Filament\Resources\AssetLifecycleLogResource\Pages;

use App\Filament\Resources\AssetLifecycleLogResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditAssetLifecycleLog extends EditRecord
{
    protected static string $resource = AssetLifecycleLogResource::class;

    protected function getHeaderActions(): array
    {
        return [
        ];
    }

    //customize redirect after create
    public function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
