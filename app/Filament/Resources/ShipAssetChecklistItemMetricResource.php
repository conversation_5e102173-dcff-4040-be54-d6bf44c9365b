<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ShipAssetChecklistItemMetricResource\Pages;
use App\Models\ShipAssetChecklistItemMetric;
use App\Models\ShipAssetChecklistItem;
use App\Models\MetricType;
use Filament\Forms;
use Filament\Resources\Resource;
use Filament\Tables;

class ShipAssetChecklistItemMetricResource extends Resource
{
    protected static ?string $model = ShipAssetChecklistItemMetric::class;

    protected static ?string $navigationGroup = 'Ship Management';
    protected static ?string $navigationIcon = 'heroicon-o-clipboard-document-list';

    protected static ?int $navigationSort = 104;
    public static function form(Forms\Form $form): Forms\Form
    {
        return $form->schema([
            Forms\Components\Select::make('ship_asset_checklist_item_id')
                ->label('Checklist Item')
                ->relationship('checklistItem', 'id')
                ->required()
                ->searchable(),

            Forms\Components\Select::make('metric_type_id')
                ->label('Metric Type')
                ->relationship('metricType', 'name')
                ->required()
                ->searchable(),

            Forms\Components\Toggle::make('is_required')->default(true),
            Forms\Components\TextInput::make('order')->numeric()->nullable(),
            Forms\Components\Textarea::make('notes')->nullable(),
        ]);
    }

    public static function table(Tables\Table $table): Tables\Table
    {
        return $table->columns([
            Tables\Columns\TextColumn::make('checklistItem.id')->label('Checklist Item'),
            Tables\Columns\TextColumn::make('metricType.name')->label('Metric'),
            Tables\Columns\IconColumn::make('is_required')->boolean(),
            Tables\Columns\TextColumn::make('order'),
            Tables\Columns\TextColumn::make('notes'),
            Tables\Columns\TextColumn::make('created_at')->dateTime()->sortable(),
        ])
        ->filters([])
        ->actions([
            Tables\Actions\EditAction::make(),
            Tables\Actions\DeleteAction::make(),
        ])
        ->bulkActions([
            Tables\Actions\DeleteBulkAction::make(),
        ]);
    }

    public static function getRelations(): array
    {
        return [];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListShipAssetChecklistItemMetrics::route('/'),
            'create' => Pages\CreateShipAssetChecklistItemMetric::route('/create'),
            'edit' => Pages\EditShipAssetChecklistItemMetric::route('/{record}/edit'),
        ];
    }
}
