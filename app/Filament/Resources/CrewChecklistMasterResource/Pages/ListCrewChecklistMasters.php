<?php

namespace App\Filament\Resources\CrewChecklistMasterResource\Pages;

use App\Filament\Resources\CrewChecklistMasterResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListCrewChecklistMasters extends ListRecords
{
    protected static string $resource = CrewChecklistMasterResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
