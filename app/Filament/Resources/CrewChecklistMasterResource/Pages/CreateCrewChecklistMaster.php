<?php

namespace App\Filament\Resources\CrewChecklistMasterResource\Pages;

use App\Filament\Resources\CrewChecklistMasterResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateCrewChecklistMaster extends CreateRecord
{
    protected static string $resource = CrewChecklistMasterResource::class;
    protected static bool $canCreateAnother = false;

    //customize redirect after create
    public function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
