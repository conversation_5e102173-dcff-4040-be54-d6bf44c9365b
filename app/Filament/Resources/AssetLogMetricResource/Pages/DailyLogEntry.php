<?php

namespace App\Filament\Resources\AssetLogMetricResource\Pages;

use App\Filament\Resources\AssetLogMetricResource;
use Filament\Pages\Page;
use Filament\Forms;
use App\Models\Ship;
use App\Models\ShipAsset;
use App\Models\ShipAssetMetric;
use App\Models\DailyAssetLog;
use App\Models\AssetLogMetric;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\DB;
use Filament\Forms\Get;
use Filament\Forms\Set;

class DailyLogEntry extends Page
{
    protected static string $resource = AssetLogMetricResource::class;

    protected static string $view = 'filament.resources.asset-log-metric-resource.pages.daily-log-entry';
    protected static ?string $navigationIcon = 'heroicon-o-document-plus';
    protected static ?string $navigationGroup = 'Logs & Readings';
    protected static ?string $title = 'Daily Asset Log Entry';

    public ?int $ship_id = null;
    public ?int $ship_asset_id = null;
    public array $metrics = [];

    public function mount(): void
    {
        // Initialize
    }

    public static function shouldRegisterNavigation(): bool
    {
        return true; // or false, depending on whether you want this page in the sidebar
    }

    protected function getFormSchema(): array
    {
        return [
            Forms\Components\Select::make('ship_id')
                ->label('Ship')
                ->options(\App\Models\Ship::pluck('name', 'id'))
                ->reactive()
                ->required()
                ->afterStateUpdated(fn($state, Set $set) => $set('ship_asset_id', null)),

            Forms\Components\Select::make('ship_asset_id')
                ->label('Asset')
                ->options(function (Get $get) {
                    $shipId = $get('ship_id');
                    if (!$shipId) return [];
                    return \App\Models\ShipAsset::where('ship_id', $shipId)
                        ->with('asset')
                        ->get()
                        ->mapWithKeys(fn($a) => [$a->id => $a->asset->name . ' (ID ' . $a->id . ')']);
                })
                ->searchable()
                ->reactive()
                ->required()
                ->afterStateUpdated(function ($state, Set $set) {
                    // Clear the metric values if asset changes
                    $set('metrics', []);
                }),

            Forms\Components\Group::make(
                function (Get $get) {
                    $shipAssetId = $get('ship_asset_id');
                    if (!$shipAssetId) return [];

                    // Get all metrics for this asset
                    $metrics = \App\Models\ShipAssetMetric::where('ship_asset_id', $shipAssetId)
                        ->with('metricType')
                        ->get();

                    return $metrics->map(function ($metric) use ($shipAssetId) {

                        // Find last log value
                        $lastLog = \App\Models\AssetLogMetric::whereHas('dailyAssetLog', function ($q) use ($shipAssetId) {
                            $q->where('ship_asset_id', $shipAssetId);
                        })
                            ->where('ship_asset_metric_id', $metric->id)
                            ->orderByDesc('created_at')
                            ->first();

                        // Use a unique key for each metric's input field
                        $fieldName = "metrics.{$metric->id}";

                        return Forms\Components\Fieldset::make($metric->metricType->name)
                            ->schema([
                              Forms\Components\Placeholder::make("unit_{$metric->id}")
                                    ->label('Unit')
                                    ->content($metric->unit ?? $metric->metricType?->default_unit ?? '-'),
                                Forms\Components\Placeholder::make("old_value_{$metric->id}")
                                    ->label('Previous Value')
                                    ->content($lastLog?->value ?? '-'),
                                Forms\Components\TextInput::make($fieldName)
                                    ->label('Current Value')
                                    ->required()
                                    ->numeric(),
                            ]);
                    })->all();
                }
            ),
        ];
    }



    public function submit()
    {
        $data = $this->form->getState();

        DB::transaction(function () use ($data) {
            // 1. Create DailyAssetLog
            $log = DailyAssetLog::create([
                'ship_asset_id' => $data['ship_asset_id'],
                'log_time' => now(),
                'recorded_by' => auth()->id(),
            ]);

            // 2. Insert metrics
            foreach ($data['metrics'] as $metricId => $value) {
                $shipAssetMetric = ShipAssetMetric::find($metricId);
                if (!$shipAssetMetric) continue;

                AssetLogMetric::create([
                    'daily_asset_log_id'   => $log->id,
                    'ship_asset_metric_id' => $metricId,
                    'value'                => $value,
                    'unit'                 => $shipAssetMetric->unit, // fetch from db
                ]);
            }
        });

        Notification::make()
            ->success()
            ->title('Log saved!')
            ->body('Your daily asset log entry has been saved successfully.')
            ->send();

        $this->form->fill(); // reset form
    }

    protected function getFormActions(): array
    {
        return [
            Forms\Components\Actions\Action::make('Save Log')
                ->submit('submit')
        ];
    }
}
