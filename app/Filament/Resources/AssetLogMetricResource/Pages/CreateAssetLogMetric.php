<?php

namespace App\Filament\Resources\AssetLogMetricResource\Pages;

use App\Filament\Resources\AssetLogMetricResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateAssetLogMetric extends CreateRecord
{
    protected static string $resource = AssetLogMetricResource::class;
    protected static bool $canCreateAnother = false;

    //customize redirect after create
    public function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
