<?php

namespace App\Filament\Resources\AssetLogMetricResource\Pages;

use App\Filament\Resources\AssetLogMetricResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditAssetLogMetric extends EditRecord
{
    protected static string $resource = AssetLogMetricResource::class;

    protected function getHeaderActions(): array
    {
        return [
        ];
    }

    //customize redirect after create
    public function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
