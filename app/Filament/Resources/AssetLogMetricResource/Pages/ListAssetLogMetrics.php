<?php

namespace App\Filament\Resources\AssetLogMetricResource\Pages;

use App\Filament\Resources\AssetLogMetricResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListAssetLogMetrics extends ListRecords
{
    protected static string $resource = AssetLogMetricResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
