<?php

namespace App\Filament\Resources;

use App\Filament\Resources\AssetLifecycleLogResource\Pages;
use App\Models\AssetLifecycleLog;
use App\Models\ShipAsset;
use App\Models\MasterEntry;
use App\Models\User;
use Filament\Forms;
use Filament\Resources\Resource;
use Filament\Tables;

class AssetLifecycleLogResource extends Resource
{
    protected static ?string $model = AssetLifecycleLog::class;
     protected static ?string $navigationGroup = 'Ship Management';
    protected static ?string $navigationIcon = 'heroicon-o-archive-box';

    public static function form(Forms\Form $form): Forms\Form
    {
        return $form->schema([
            Forms\Components\Select::make('ship_asset_id')
                ->label('Ship Asset')
                ->options(ShipAsset::with('asset')->get()->mapWithKeys(function ($s) {
                    return [$s->id => $s->asset->name . ' (' . $s->ship->name . ')'];
                }))
                ->required()->searchable(),
            Forms\Components\Select::make('event_type_id')
                ->label('Event Type')
                ->options(fn() => MasterEntry::whereHas('type', fn($q) => $q->where('name','asset_status'))->pluck('name','id'))
                ->required()
                ->searchable(),
            Forms\Components\Textarea::make('remarks')->nullable(),
            Forms\Components\Select::make('recorded_by')
                ->label('Recorded By')
                ->options(User::pluck('name','id'))
                ->required()
                ->searchable(),
            Forms\Components\DateTimePicker::make('event_time')->required(),
        ]);
    }

    public static function table(Tables\Table $table): Tables\Table
    {
        return $table->columns([
            Tables\Columns\TextColumn::make('shipAsset.asset.name')->label('Asset'),
            Tables\Columns\TextColumn::make('eventType.name')->label('Event Type'),
            Tables\Columns\TextColumn::make('remarks'),
            Tables\Columns\TextColumn::make('recordedBy.name')->label('Recorded By'),
            Tables\Columns\TextColumn::make('event_time')->dateTime(),
            Tables\Columns\TextColumn::make('created_at')->dateTime()->sortable(),
        ])->filters([])->actions([
            Tables\Actions\EditAction::make(),
            Tables\Actions\DeleteAction::make(),
        ])->bulkActions([
            Tables\Actions\DeleteBulkAction::make(),
        ]);
    }

    public static function getRelations(): array { return []; }
    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAssetLifecycleLogs::route('/'),
            'create' => Pages\CreateAssetLifecycleLog::route('/create'),
            'edit' => Pages\EditAssetLifecycleLog::route('/{record}/edit'),
        ];
    }
}
