<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ShipMarkResource\Pages;
use App\Filament\Resources\ShipMarkResource\RelationManagers;
use App\Models\ShipMark;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ShipMarkResource extends Resource
{
    protected static ?string $model = ShipMark::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')->required()->maxLength(255)->unique(ignoreRecord: true),
                Forms\Components\TextInput::make('code')
                    ->required()->unique(ignoreRecord: true)->maxLength(255),
                Forms\Components\Textarea::make('description')->rows(2)
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable(),
                Tables\Columns\TextColumn::make('code')
                    ->searchable(),
                Tables\Columns\TextColumn::make('description')->limit(30)
                    ->searchable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),

            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            // \App\Filament\Resources\ShipMarkResource\RelationManagers\CrewPositionRequirementsRelationManager::class,
        ];
    }
    public static function getPages(): array
    {
        return [
            'index' => Pages\ListShipMarks::route('/'),
            'create' => Pages\CreateShipMark::route('/create'),
            'edit' => Pages\EditShipMark::route('/{record}/edit'),
        ];
    }
}
