<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ShipAssetMetricResource\Pages;
use App\Models\ShipAssetMetric;
use App\Models\ShipAsset;
use App\Models\MetricType;
use Filament\Forms;
use Filament\Resources\Resource;
use Filament\Tables;

class ShipAssetMetricResource extends Resource
{
    protected static ?string $model = ShipAssetMetric::class;
        protected static ?string $navigationGroup = 'Ship Management';
    protected static ?string $navigationIcon = 'heroicon-o-clipboard-document-list';
    protected static ?int $navigationSort = 102;

    public static function form(Forms\Form $form): Forms\Form
    {
        return $form->schema([
            Forms\Components\Select::make('ship_asset_id')
                ->label('Ship Asset')
                ->options(ShipAsset::with('asset', 'ship')->get()
                    ->mapWithKeys(fn($s) => [$s->id => $s->asset->name . ' (' . $s->ship->name . ')']))
                ->searchable()
                ->required()
                ->reactive(),

            Forms\Components\Select::make('metric_type_id')
                ->label('Metric Type')
                ->relationship('metricType', 'name')
                ->required()
                ->searchable(),

            Forms\Components\Toggle::make('is_required')->default(true),
            Forms\Components\Toggle::make('is_transactional')->default(true),
            Forms\Components\Toggle::make('is_rob')->default(true),
            Forms\Components\Toggle::make('is_primary')->default(false),
            Forms\Components\TextInput::make('unit')->maxLength(20),
            Forms\Components\TextInput::make('min_threshold')->numeric()->nullable(),
            Forms\Components\TextInput::make('max_threshold')->numeric()->nullable(),
            Forms\Components\Toggle::make('alert_on_min_breach'),
            Forms\Components\Toggle::make('alert_on_max_breach'),
            Forms\Components\TextInput::make('reminder_days')->nullable(),
            Forms\Components\Textarea::make('notes')->nullable(),
        ]);
    }

    public static function table(Tables\Table $table): Tables\Table
    {
        return $table->columns([
            Tables\Columns\TextColumn::make('shipAsset.asset.name')->label('Ship Asset'),
            Tables\Columns\TextColumn::make('metricType.name')->label('Metric'),
            Tables\Columns\IconColumn::make('is_primary')->boolean(),
            Tables\Columns\IconColumn::make('is_required')->boolean(),
            Tables\Columns\IconColumn::make('is_rob')->boolean(),
            Tables\Columns\IconColumn::make('is_transactional')->boolean(),
            Tables\Columns\TextColumn::make('unit'),
            Tables\Columns\TextColumn::make('min_threshold'),
            Tables\Columns\TextColumn::make('max_threshold'),
        ])->actions([
            Tables\Actions\EditAction::make(),
            Tables\Actions\DeleteAction::make(),
        ])->bulkActions([
            Tables\Actions\DeleteBulkAction::make(),
        ]);
    }

    public static function getRelations(): array
    {
        return [];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListShipAssetMetrics::route('/'),
            'create' => Pages\CreateShipAssetMetric::route('/create'),
            'edit' => Pages\EditShipAssetMetric::route('/{record}/edit'),
        ];
    }
}
