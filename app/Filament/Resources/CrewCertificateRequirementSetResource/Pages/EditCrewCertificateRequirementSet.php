<?php

namespace App\Filament\Resources\CrewCertificateRequirementSetResource\Pages;

use App\Filament\Resources\CrewCertificateRequirementSetResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditCrewCertificateRequirementSet extends EditRecord
{
    protected static string $resource = CrewCertificateRequirementSetResource::class;

    protected function getHeaderActions(): array
    {
        return [
        ];
    }

    //customize redirect after create
    public function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
