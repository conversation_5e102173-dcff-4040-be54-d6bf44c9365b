<?php

namespace App\Filament\Resources\CrewCertificateRequirementSetResource\Pages;

use App\Filament\Resources\CrewCertificateRequirementSetResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListCrewCertificateRequirementSets extends ListRecords
{
    protected static string $resource = CrewCertificateRequirementSetResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
