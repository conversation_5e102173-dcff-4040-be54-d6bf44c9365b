<?php

namespace App\Filament\Resources\CrewCertificateRequirementSetResource\Pages;

use App\Filament\Resources\CrewCertificateRequirementSetResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateCrewCertificateRequirementSet extends CreateRecord
{
    protected static string $resource = CrewCertificateRequirementSetResource::class;
    protected static bool $canCreateAnother = false;

    //customize redirect after create
    public function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
