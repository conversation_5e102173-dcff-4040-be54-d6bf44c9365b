<?php
namespace App\Filament\Resources;

use Filament\Forms;
use Filament\Tables;
use App\Models\CrewCertificate;
use App\Models\CrewCertificateType;
use App\Models\Crew;
use Filament\Resources\Resource;
use App\Filament\Resources\CrewCertificateResource\Pages;
class CrewCertificateResource extends Resource
{

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationGroup = 'Crew Compliance';
    protected static ?string $model = CrewCertificate::class;

    public static function form(Forms\Form $form): Forms\Form
    {
        return $form->schema([
       Forms\Components\Select::make('crew_id')
            ->relationship('crew', 'id') // Keep 'id' or any column, but not 'display_name'
            ->getOptionLabelFromRecordUsing(fn ($record) => $record->display_name)
            ->required(),
            Forms\Components\Select::make('crew_certificate_type_id')
                ->relationship('type', 'name')
                ->required(),
            Forms\Components\TextInput::make('certificate_number'),
            Forms\Components\DatePicker::make('issue_date'),
            Forms\Components\DatePicker::make('expiry_date'),
            Forms\Components\DatePicker::make('renewal_date'),
            Forms\Components\TextInput::make('issuing_authority'),
            Forms\Components\FileUpload::make('file')->directory('crew-certificates'),
            Forms\Components\Textarea::make('notes'),
        ]);
    }

    public static function table(Tables\Table $table): Tables\Table
    {
        return $table->columns([
            Tables\Columns\TextColumn::make('crew.display_name')->label('Crew'),
            Tables\Columns\TextColumn::make('type.name')->label('Certificate Type'),
            Tables\Columns\TextColumn::make('certificate_number'),
            Tables\Columns\TextColumn::make('expiry_date')->date(),
            Tables\Columns\TextColumn::make('renewal_date')->date(),
            Tables\Columns\TextColumn::make('file')->label('File')->url(fn ($record) => $record->file ? \Storage::url($record->file) : null)->openUrlInNewTab(),
        ]);
    }

    public static function getRelations(): array
    {
        return [];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCrewCertificates::route('/'),
            'create' => Pages\CreateCrewCertificate::route('/create'),
            'edit' => Pages\EditCrewCertificate::route('/{record}/edit'),
        ];
    }
}
