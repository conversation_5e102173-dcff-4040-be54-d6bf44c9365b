<?php

namespace App\Filament\Resources;

use App\Filament\Resources\LocationResource\Pages;
use App\Models\Location;
use App\Models\Ship;
use App\Models\MasterEntry;
use App\Models\LocationType;
use Filament\Forms;
use Filament\Resources\Resource;
use Filament\Tables;

class LocationResource extends Resource
{
    protected static ?string $model = Location::class;
    protected static ?string $navigationGroup = 'Ship Management';
    protected static ?int $navigationSort = 88;
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Forms\Form $form): Forms\Form
    {
        return $form->schema([
            Forms\Components\Select::make('ship_id')
                ->label('Ship')
                ->options(Ship::pluck('name', 'id'))
                ->required()
                ->searchable(),
            Forms\Components\TextInput::make('name')->required()->maxLength(100),
            Forms\Components\Select::make('parent_id')
                ->label('Parent Location')
                ->options(Location::pluck('name', 'id'))
                ->nullable()
                ->searchable(),
            Forms\Components\Select::make('location_type_id')
                ->label('Location Type')
                ->options(LocationType::pluck('name','id'))
                ->required()
                ->searchable(),
                Forms\Components\Select::make('purpose_id')
    ->label('Purpose')
    ->options(fn () => MasterEntry::whereHas('type', fn($q) => $q->where('name', 'location_purpose'))->pluck('name', 'id'))
    ->required(),
            Forms\Components\Textarea::make('notes')->nullable(),
            Forms\Components\FileUpload::make('image')
                ->directory('locations/images')->nullable(),
            Forms\Components\TextInput::make('path')->nullable(),
        ]);
    }

    public static function table(Tables\Table $table): Tables\Table
    {
        return $table->columns([
            Tables\Columns\TextColumn::make('ship.name')->sortable(),
            Tables\Columns\TextColumn::make('name')->searchable(),
            Tables\Columns\TextColumn::make('parent.name')->label('Parent')->sortable(),
            Tables\Columns\TextColumn::make('locationType.name')->label('Type')->sortable(),
            Tables\Columns\ImageColumn::make('image')->circular(),
            Tables\Columns\TextColumn::make('created_at')->dateTime()->sortable(),
        ])
        ->filters([])->actions([
            Tables\Actions\EditAction::make(),
            Tables\Actions\DeleteAction::make(),
        ])
        ->bulkActions([
            Tables\Actions\DeleteBulkAction::make(),
        ]);
    }

    public static function getRelations(): array { return []; }
    public static function getPages(): array
    {
        return [
            'index' => Pages\ListLocations::route('/'),
            'create' => Pages\CreateLocation::route('/create'),
            'edit' => Pages\EditLocation::route('/{record}/edit'),
        ];
    }
}
