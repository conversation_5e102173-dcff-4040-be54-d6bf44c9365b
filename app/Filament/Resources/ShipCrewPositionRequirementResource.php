<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ShipCrewPositionRequirementResource\Pages;
use App\Filament\Resources\ShipCrewPositionRequirementResource\RelationManagers;
use App\Models\ShipCrewPositionRequirement;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ShipCrewPositionRequirementResource extends Resource
{
    protected static ?string $model = ShipCrewPositionRequirement::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $navigationGroup = 'Crew Compliance';
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('ship_id')->relationship('ship', 'name')->required(),
                Forms\Components\Select::make('ship_mark_id')->relationship('mark', 'name')->required(),
                Forms\Components\Select::make('designation_id')->relationship('designation', 'name')->required(),
                Forms\Components\TextInput::make('min_required')
                    ->required()
                    ->numeric()
                    ->default(1),
                Forms\Components\TextInput::make('max_allowed')
                    ->numeric(),
                Forms\Components\Toggle::make('is_required')
                    ->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('ship.name')->sortable(),
                Tables\Columns\TextColumn::make('mark.name')->label('Mark'),
                Tables\Columns\TextColumn::make('designation.name')->sortable(),
                Tables\Columns\TextColumn::make('min_required')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('max_allowed')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\IconColumn::make('is_required')
                    ->boolean(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),

            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListShipCrewPositionRequirements::route('/'),
            'create' => Pages\CreateShipCrewPositionRequirement::route('/create'),
            'edit' => Pages\EditShipCrewPositionRequirement::route('/{record}/edit'),
        ];
    }
}
