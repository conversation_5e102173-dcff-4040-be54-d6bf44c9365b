<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CrewChecklistMasterResource\Pages;
use App\Filament\Resources\CrewChecklistMasterResource\RelationManagers;
use App\Models\CrewChecklistMaster;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Models\MasterEntry;
use App\Models\MasterType;
use App\Models\Designation;
use App\Forms\Components\FlatStringList;


class CrewChecklistMasterResource extends Resource
{
    protected static ?string $model = CrewChecklistMaster::class;
     protected static ?string $navigationGroup = 'Crew';
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Forms\Form $form): Forms\Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->required()
                    ->maxLength(100),

                Forms\Components\Select::make('event_type_id')
                    ->label('Event Type')
                    ->searchable()
                    ->options(fn() => MasterEntry::query()
                        ->whereHas('type', fn($q) => $q->where('name', 'crew_event_type'))
                        ->pluck('name', 'id'))
                    ->required(),

                Forms\Components\Select::make('designation_ids')
                    ->label('Designations')
                    ->multiple()
                    ->searchable()
                    ->options(Designation::pluck('name', 'id'))
                    ->placeholder('Leave empty = applicable to all'),

                Forms\Components\TextInput::make('question')
                    ->required()
                    ->maxLength(255),

                Forms\Components\Select::make('input_type_id')
                    ->label('Input Type')
                    ->searchable()
                    ->options(fn() => MasterEntry::query()
                        ->whereHas('type', fn($q) => $q->where('name', 'input_type'))
                        ->pluck('name', 'id'))
                    ->required()
                    ->reactive(),

                // Conditionally show Options field only for Selects



                Forms\Components\Repeater::make('options')
                    ->label('Options')
                    ->schema([
                        Forms\Components\TextInput::make('option')
                            ->label('Option')
                            ->required(),
                    ])
                    ->default([]) // ← for new create
                    ->columns(1)
                    ->addActionLabel('Add Option')
                    ->visible(fn(Forms\Get $get) => in_array(
                        optional(MasterEntry::find($get('input_type_id')))->code,
                        ['select_single', 'select_multiple', 'select']
                    ))
                    ->afterStateHydrated(function ($component, $state) {
                        // If it's a flat array (["A", "B"]), convert it to structured form
                        if (is_array($state) && collect($state)->every(fn($v) => is_string($v))) {
                            $component->state(
                                collect($state)
                                    ->map(fn($value) => ['option' => $value])
                                    ->values()
                                    ->all()
                            );
                        }
                    })
                    ->dehydrated(true),



                Forms\Components\Toggle::make('is_active')
                    ->label('Active')
                    ->default(true),
            ]);
    }



    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')->searchable(),

                Tables\Columns\TextColumn::make('eventType.name')
                    ->label('Event Type')
                    ->sortable(),

                Tables\Columns\TextColumn::make('question')->searchable(),

                Tables\Columns\TextColumn::make('inputType.name')
                    ->label('Input Type')
                    ->sortable(),

                Tables\Columns\IconColumn::make('is_active')
                    ->label('Active')
                    ->boolean(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Created')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label('Updated')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }


    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCrewChecklistMasters::route('/'),
            'create' => Pages\CreateCrewChecklistMaster::route('/create'),
            'edit' => Pages\EditCrewChecklistMaster::route('/{record}/edit'),
        ];
    }
}
