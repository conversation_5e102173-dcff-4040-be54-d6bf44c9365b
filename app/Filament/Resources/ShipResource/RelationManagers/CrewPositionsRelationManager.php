<?php
namespace App\Filament\Resources\ShipResource\RelationManagers;

use App\Models\MasterEntry;
use Filament\Forms;
use Filament\Tables;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables\Table;

class CrewPositionsRelationManager extends RelationManager
{
    protected static string $relationship = 'crewPositions';

    public function form(Form $form): Form
    {
        return $form->schema([
           Forms\Components\Select::make('designation_id')
    ->label('Designation')
    ->relationship('designation', 'name')
    ->searchable()
    ->required(),

            Forms\Components\TextInput::make('min_required')
                ->label('Minimum Required')
                ->integer()
                ->minValue(0)
                ->default(0)
                ->required(),

            Forms\Components\TextInput::make('max_allowed')
                ->label('Maximum Allowed')
                ->integer()
                ->minValue(1)
                ->nullable(),

            Forms\Components\Textarea::make('notes')->nullable(),
        ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('designation.name')->label('Designation')->sortable(),
                Tables\Columns\TextColumn::make('min_required')->label('Min'),
                Tables\Columns\TextColumn::make('max_allowed')->label('Max'),
                Tables\Columns\TextColumn::make('notes')->label('Notes')->limit(20),
                Tables\Columns\TextColumn::make('created_at')->dateTime()->sortable(),
            ])
            ->headerActions([Tables\Actions\CreateAction::make()])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([Tables\Actions\DeleteBulkAction::make()]);
    }
}
