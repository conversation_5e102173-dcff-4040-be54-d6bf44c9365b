<?php

namespace App\Filament\Api\Handlers\Auth;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\ValidationException;
use Lara<PERSON>\Sanctum\NewAccessToken;
use Rupadana\ApiService\Http\Handlers;
use Symfony\Component\HttpFoundation\Response;

class LoginHandler extends Handlers
{
    public static bool $public = true;

    public function handler(Request $request): Response
    {
        $credentials = $request->validate(config('api-service.login-rules'));

        if (!Auth::attempt($credentials)) {
            throw ValidationException::withMessages([
                'email' => ['The provided credentials are incorrect.a'],
            ]);
        }

        /** @var \App\Models\User $user */
        $user = Auth::user();
        $user->tokens()->delete();

        // 🔐 Sync roles + authority permissions
        $permissions = collect();

        foreach ($user->roles as $role) {
            $permissions = $permissions->merge($role->permissions);
        }

        foreach ($user->authorityLevels as $auth) {
            $permissions = $permissions->merge($auth->permissions);
        }

        if ($user->designation) {
            foreach ($user->designation->authorityLevels as $auth) {
                $permissions = $permissions->merge($auth->permissions);
            }
        }

        $user->syncPermissions($permissions->pluck('name')->unique());

        /** @var NewAccessToken $token */
        $token = $user->createToken('mobile_login');

        // 🔍 Modules & permissions
        $allPermissions = $user->getAllPermissions()->pluck('name');

        $modules = \App\Models\MobileModule::with('permissions')->get()->map(function ($module) use ($allPermissions) {
            $actions = [];

            foreach ($module->permissions as $perm) {
                if ($allPermissions->contains($perm->name)) {
                    $actions[] = str($perm->name)->after("{$module->key}.");
                }
            }

            return [
                'name' => $module->name,
                'key' => $module->key,
                'icon' => $module->icon,
                'permissions' => $actions,
            ];
        })->filter(fn($m) => !empty($m['permissions']))->values();

        return response()->json([
            'token' => $token->plainTextToken,
            'user' => $user,
            'roles' => $user->getRoleNames(),
            'authority_levels' => $user->authorityLevels()->pluck('name'),
            'designation' => $user->designation?->name,
            'modules' => $modules,
        ]);
    }
}
