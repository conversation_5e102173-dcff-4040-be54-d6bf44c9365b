<?php

namespace App\Providers;

use App\Models\User;
use Filament\Support\Facades\FilamentView;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\ServiceProvider;
use App\Models\AssetTransaction;
use App\Models\AssetLogMetric;
use App\Observers\AssetTransactionObserver;
use App\Observers\AssetLogMetricObserver;
use Rupadana\ApiService\Http\Controllers\AuthController as DefaultAuthController;
use App\Http\Controllers\Api\AuthController as CustomAuthController;
// use App\Providers\Livewire;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
        parent::register();
        FilamentView::registerRenderHook('panels::body.end', fn(): string => Blade::render("@vite('resources/js/app.js')"));
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        //
        AssetTransaction::observe(AssetTransactionObserver::class);
        AssetLogMetric::observe(AssetLogMetricObserver::class);
        Gate::define('viewApiDocs', function (User $user) {
            return true;
        });
        // Gate::policy()
   
        Event::listen(function (\SocialiteProviders\Manager\SocialiteWasCalled $event) {
            $event->extendSocialite('discord', \SocialiteProviders\Google\Provider::class);
        });
        Route::bind(DefaultAuthController::class, fn () => app(CustomAuthController::class));
    }
}
