<?php

namespace App\Observers;

use App\Models\AssetTransaction;
use App\Models\ShipAsset;

class AssetTransactionObserver
{
    /**
     * Handle the AssetTransaction "created" event.
     */
    public function created(AssetTransaction $transaction)
    {
        // Example: Update ship asset's current_level after transaction
        // On create, update ship_asset current_level for this metric if is_primary
       $shipAsset = $transaction->shipAsset;

        // Condition 1: asset table's primary_metric_type_id
        $isPrimaryOnAsset = $shipAsset && $shipAsset->primary_metric_type_id == $transaction->metric_type_id;

        // Condition 2: pivot table's is_primary flag
        $isPrimaryOnMetric = \App\Models\ShipAssetMetric::where('ship_asset_id', $transaction->ship_asset_id)
            ->where('metric_type_id', $transaction->metric_type_id)
            ->where('is_primary', true)
            ->exists(); // use exists() for efficiency

        if ($isPrimaryOnAsset || $isPrimaryOnMetric) {
            // This is a primary metric by either definition, so update
            $shipAsset->current_level = $transaction->new_level;
            $shipAsset->last_measured_at = now();
            $shipAsset->save();
        }

    }

    /**
     * Handle the AssetTransaction "updated" event.
     */
    public function updated(AssetTransaction $assetTransaction): void
    {
        //
    }

    /**
     * Handle the AssetTransaction "deleted" event.
     */
    public function deleted(AssetTransaction $assetTransaction): void
    {
        //
    }

    /**
     * Handle the AssetTransaction "restored" event.
     */
    public function restored(AssetTransaction $assetTransaction): void
    {
        //
    }

    /**
     * Handle the AssetTransaction "force deleted" event.
     */
    public function forceDeleted(AssetTransaction $assetTransaction): void
    {
        //
    }
}
