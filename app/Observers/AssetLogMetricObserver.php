<?php

namespace App\Observers;

use App\Models\AssetLogMetric;
use App\Models\ShipAsset;
use App\Models\ShipAssetMetric;
use App\Models\AssetTransaction;
use App\Models\MasterEntry;

class AssetLogMetricObserver
{
    public function created(AssetLogMetric $metric)
    {
        $this->handleMetricChange($metric);
    }

    public function updated(AssetLogMetric $metric)
    {
        $this->handleMetricChange($metric);
    }

    protected function handleMetricChange(AssetLogMetric $metric)
    {
        // dd($metric);
        // Make sure relationships are loaded
        $metric->loadMissing(['shipAssetMetric', 'shipAssetMetric.shipAsset', 'shipAssetMetric.metricType', 'dailyAssetLog']);

        $shipAssetMetric = $metric->shipAssetMetric;
        if (! $shipAssetMetric) {
            return;
        }
        $shipAsset = $shipAssetMetric->shipAsset;
        if (! $shipAsset) {
            return;
        }

        // If this is the "primary" metric for the asset, update ship_asset.current_level
        if ($shipAssetMetric->is_primary) {
            $oldLevel = $shipAsset->current_level;
            $newLevel = $metric->value;

            // Update only if changed
            if ($oldLevel != $newLevel) {
                $shipAsset->current_level = $newLevel;
                $shipAsset->last_measured_at = now();
                $shipAsset->save();

                // Optional: log an 'adjustment' transaction if difference is above threshold
                $threshold = $shipAssetMetric->min_threshold ?? 0.01; // You can tune this or get from config
                if (abs(floatval($oldLevel) - floatval($newLevel)) > $threshold) {
                    // Get adjustment type ID
                    $adjustmentTypeId = MasterEntry::whereHas('type', function($q) {
                        $q->where('name', 'asset_transaction_type');
                    })->where('code', 'adjustment')->value('id');

                    if ($adjustmentTypeId) {
                        AssetTransaction::create([
                            'ship_asset_id'        => $shipAsset->id,
                            'transaction_type_id'  => $adjustmentTypeId,
                            'metric_type_id'       => $shipAssetMetric->metric_type_id,
                            'quantity'             => $newLevel - $oldLevel,
                            'old_level'            => $oldLevel,
                            'new_level'            => $newLevel,
                            'recorded_by'          => $metric->dailyAssetLog->recorded_by ?? auth()->id(),
                            'notes'                => 'Auto adjustment from daily log reading.',
                            'transaction_time'     => $metric->created_at ?? now(),
                        ]);
                    }
                }
            }
        }

        // (Optional) Alert logic: if min/max breached on this metric, notify user/crew
        if ($shipAssetMetric->alert_on_min_breach && $metric->value < $shipAssetMetric->min_threshold) {
            // TODO: Send min threshold alert/notification
        }
        if ($shipAssetMetric->alert_on_max_breach && $metric->value > $shipAssetMetric->max_threshold) {
            // TODO: Send max threshold alert/notification
        }
    }
}
