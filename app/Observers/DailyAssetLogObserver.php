<?php

namespace App\Observers;

use App\Models\DailyAssetLog;

class DailyAssetLogObserver
{
    /**
     * Handle the DailyAssetLog "created" event.
     */
    public function created(DailyAssetLog $dailyAssetLog): void
    {
        //
    }

    /**
     * Handle the DailyAssetLog "updated" event.
     */
    public function updated(DailyAssetLog $dailyAssetLog): void
    {
        //
    }

    /**
     * Handle the DailyAssetLog "deleted" event.
     */
    public function deleted(DailyAssetLog $dailyAssetLog): void
    {
        //
    }

    /**
     * Handle the DailyAssetLog "restored" event.
     */
    public function restored(DailyAssetLog $dailyAssetLog): void
    {
        //
    }

    /**
     * Handle the DailyAssetLog "force deleted" event.
     */
    public function forceDeleted(DailyAssetLog $dailyAssetLog): void
    {
        //
    }
}
