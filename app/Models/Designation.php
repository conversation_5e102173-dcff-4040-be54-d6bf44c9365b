<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Designation extends Model
{
    protected $fillable = ['name', 'description', 'requires_login'];
    protected $casts = ['requires_login' => 'boolean'];

    public function crew(): Has<PERSON><PERSON>
    {
        return $this->hasMany(Crew::class);
    }

    public function featurePermissions(): HasMany
    {
        return $this->hasMany(DesignationFeaturePermission::class);
    }

    public function authorityLevels()
    {
        return $this->belongsToMany(\App\Models\AuthorityLevel::class, 'designation_authority_levels');
    }

}
