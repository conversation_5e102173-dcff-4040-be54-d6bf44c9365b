<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;

class Crew extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'user_id', 'unique_crew_id', 'aadhaar_number', 'pan_number',
        'passport_number', 'status_id',
        // Remove: 'current_ship_id', 'designation_id', 'onboarded_at', 'offboarded_at'
    ];

    protected $casts = [
        // Remove: 'onboarded_at' => 'datetime', 'offboarded_at' => 'datetime',
    ];

    protected $appends = ['display_name', 'current_assignment', 'current_ship', 'current_designation', 'onboarded_at', 'offboarded_at'];

    // --- Relationships ---

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function status()
    {
        return $this->belongsTo(MasterEntry::class, 'status_id');
    }

    public function documents(): HasMany
    {
        return $this->hasMany(CrewDocument::class);
    }

    public function history(): HasMany
    {
        return $this->hasMany(CrewHistory::class);
    }

    public function drillParticipations(): HasMany
    {
        return $this->hasMany(MockDrillParticipant::class);
    }

    public function assignments(): HasMany
    {
        return $this->hasMany(CrewAssignment::class);
    }

    public function requirements()
    {
        return $this->hasMany(CrewCertificateRequirement::class);
    }

    public function certificates()
    {
        return $this->hasMany(CrewCertificate::class);
    }

    // --- Accessors for current assignment, ship, designation, period ---

    public function getCurrentAssignmentAttribute()
    {
        return $this->assignments()
            ->whereNull('relieved_at')
            ->latest('assigned_at')
            ->first();
    }

    public function getCurrentShipAttribute()
    {
        return $this->current_assignment?->shipMark?->ship;
    }

    public function getCurrentDesignationAttribute()
    {
        return $this->current_assignment?->designation;
    }

    public function getOnboardedAtAttribute()
    {
        return $this->current_assignment?->assigned_at;
    }

    public function getOffboardedAtAttribute()
    {
        return $this->current_assignment?->relieved_at;
    }

    public function getDisplayNameAttribute()
    {
        return $this->user ? $this->user->name : 'Crew #'.$this->id;
    }

    // --- Crew full assignment history ---
    public function getAssignmentHistory()
    {
        return $this->assignments()->with(['shipMark.ship', 'designation'])->orderByDesc('assigned_at')->get();
    }


    public function currentAssignment() {
        return $this->hasOne(CrewAssignment::class)->where('status', 'assigned');
    }

}
