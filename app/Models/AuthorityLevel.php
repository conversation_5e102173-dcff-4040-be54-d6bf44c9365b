<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Spatie\Permission\Models\Permission;

class AuthorityLevel extends Model
{
    use HasFactory;

    protected $fillable = ['name'];

    public function permissions()
    {
        return $this->belongsToMany(Permission::class, 'authority_level_permission');
    }

    public function users()
    {
        return $this->belongsToMany(User::class, 'user_authority_levels');
    }

    public function designations()
    {
        return $this->belongsToMany(Designation::class, 'designation_authority_levels');
    }
}
