<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;

class PreventiveMaintenance extends Model
{
    use HasFactory;

    protected $fillable = [
        'ship_asset_id', 'checklist_item_id', 'trigger_type', 'time_interval',
        'usage_metric', 'usage_threshold', 'condition', 'reminder_settings',
        'current_value', 'last_performed_at', 'next_due_date', 'next_due_value', 'is_active'
    ];

    protected $casts = [
        'reminder_settings' => 'array',
        'next_due_date' => 'datetime',
        'last_performed_at' => 'datetime',
    ];

    public function shipAsset(): BelongsTo
    {
        return $this->belongsTo(ShipAsset::class);
    }

    public function checklistItem(): BelongsTo
    {
        return $this->belongsTo(AssetChecklistMaster::class, 'checklist_item_id');
    }

    public function reminders(): MorphMany
    {
        return $this->morphMany(Reminder::class, 'remindable');
    }
}
