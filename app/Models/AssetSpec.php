<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AssetSpec extends Model
{
    protected $fillable = ['asset_id', 'spec_key_id', 'value'];

    public function asset(): BelongsTo
    {
        return $this->belongsTo(Asset::class);
    }

    public function key(): BelongsTo
    {
        return $this->belongsTo(AssetSpecKey::class, 'spec_key_id');
    }
}
