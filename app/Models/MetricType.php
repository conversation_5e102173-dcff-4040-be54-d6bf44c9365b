<?php

// app/Models/MetricType.php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class MetricType extends Model
{
    protected $fillable = [
        'code', 'name', 'default_unit', 'data_type', 'description',
    ];

    public function shipAssetMetrics(): HasMany
    {
        return $this->hasMany(ShipAssetMetric::class);
    }
}
