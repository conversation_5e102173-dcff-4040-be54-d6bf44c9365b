<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Approval extends Model
{
    protected $fillable = [
        'module', 'record_id', 'sequence_order', 'approval_rule_id',
        'user_id', 'status_id', 'remarks', 'approved_at'
    ];

    protected $casts = ['approved_at' => 'datetime'];

    public function approvalRule(): BelongsTo
    {
        return $this->belongsTo(ApprovalRule::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function status(): BelongsTo
    {
        return $this->belongsTo(MasterEntry::class, 'status_id');
    }
}

