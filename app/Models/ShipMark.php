<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ShipMark extends Model
{
    protected $fillable = ['name', 'code', 'description'];

    public function crewPositionRequirements()
    {
        return $this->hasMany(ShipCrewPositionRequirement::class);
    }


    // Optionally, you can define relationships to other models if needed
    // For example, if a ShipMark can have many ships:
    public function ships()
    {
        return $this->hasMany(Ship::class, 'current_mark_id');
    }
}
