<?php

namespace App\Models;
use Illuminate\Database\Eloquent\Model;

class CrewCertificateType extends Model
{
    protected $fillable = ['name', 'code', 'description', 'is_mandatory'];

    public function certificates()
    {
        return $this->hasMany(CrewCertificate::class);
    }

    // Requirement rows referencing this type
    public function requirements()
    {
        return $this->hasMany(CrewCertificateRequirement::class, 'certificate_type_id');
    }
}


