<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class CrewHistory extends Model
{
    protected $fillable = [
        'crew_id', 'ship_id', 'designation_id', 'from_date',
        'to_date', 'event_type_id', 'notes'
    ];

    protected $casts = [
        'from_date' => 'datetime',
        'to_date' => 'datetime',
    ];

    public function crew(): BelongsTo
    {
        return $this->belongsTo(Crew::class);
    }

    public function ship(): BelongsTo
    {
        return $this->belongsTo(Ship::class);
    }

    public function designation(): BelongsTo
    {
        return $this->belongsTo(Designation::class);
    }

    public function eventType(): BelongsTo
    {
        return $this->belongsTo(MasterEntry::class, 'event_type_id');
    }

    public function checklists(): HasMany
    {
        return $this->hasMany(CrewChecklist::class, 'crew_history_id');
    }
}

