<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class UserAuthorityLevel extends Model
{
    use HasFactory;

    protected $table = 'user_authority_levels';
    protected $fillable = ['user_id', 'authority_level_id'];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function authorityLevel()
    {
        return $this->belongsTo(AuthorityLevel::class);
    }
}
