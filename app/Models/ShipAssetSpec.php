<?php
// app/Models/ShipAssetSpec.php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ShipAssetSpec extends Model
{
    protected $fillable = ['ship_asset_id', 'spec_key_id', 'value'];

    public function shipAsset(): BelongsTo
    {
        return $this->belongsTo(ShipAsset::class);
    }

    public function specKey(): BelongsTo
    {
        return $this->belongsTo(AssetSpecKey::class, 'spec_key_id');
    }
}
