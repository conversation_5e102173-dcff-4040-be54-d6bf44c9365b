<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class SurveySchedule extends Model
{
    protected $fillable = [
        'ship_id', 'target_type', 'target_id', 'frequency_id',
        'start_date', 'next_due_date', 'is_active'
    ];

    protected $casts = [
        'start_date' => 'date',
        'next_due_date' => 'date',
        'is_active' => 'boolean',
    ];

    public function ship(): BelongsTo
    {
        return $this->belongsTo(Ship::class);
    }

    public function frequency(): BelongsTo
    {
        return $this->belongsTo(MasterEntry::class, 'frequency_id');
    }

    public function surveys(): HasMany
    {
        return $this->hasMany(Survey::class);
    }
}
