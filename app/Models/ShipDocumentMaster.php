<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ShipDocumentMaster extends Model
{
    use HasFactory;

    protected $fillable = ['name', 'description', 'is_required', 'ship_type_ids', 'file_types'];

    protected $casts = [
        'ship_type_ids' => 'array',
    ];

    public function documents(): HasMany
    {
        return $this->hasMany(ShipDocument::class);
    }
}
