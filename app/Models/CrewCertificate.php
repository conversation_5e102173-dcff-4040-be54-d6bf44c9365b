<?php

namespace App\Models;
use Illuminate\Database\Eloquent\Model;

class CrewCertificate extends Model
{
    protected $fillable = [
        'crew_id',
        'crew_certificate_type_id',
        'certificate_number',
        'issue_date',
        'expiry_date',
        'renewal_date',
        'issuing_authority',
        'file',
        'notes'
    ];

    public function crew()
    {
        return $this->belongsTo(Crew::class);
    }

    public function type()
    {
        return $this->belongsTo(CrewCertificateType::class, 'crew_certificate_type_id');
    }

    public function certificateType()
{
    return $this->belongsTo(CrewCertificateType::class, 'crew_certificate_type_id');
}
}

// php artisan make:filament-resource CrewCertificateType --generate
// php artisan make:filament-resource CrewCertificate --generate
