<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
class CrewDocumentMaster extends Model
{
    protected $fillable = [
        'name', 'description', 'is_mandatory', 'validity_unit_id',
        'designation_ids', 'event_triggers', 'file_types'
    ];

    protected $casts = [
        'is_mandatory' => 'boolean',
        'designation_ids' => 'array',
        'event_triggers' => 'array',
    ];

    public function validityUnit(): BelongsTo
    {
        return $this->belongsTo(MasterEntry::class, 'validity_unit_id');
    }

    public function documents(): HasMany
    {
        return $this->hasMany(CrewDocument::class, 'document_master_id');
    }
}
