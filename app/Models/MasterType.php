<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class MasterType extends Model
{
    //

    // add fillable
     protected $fillable = ['name', 'description'];
    // add guaded
    protected $guarded = ['id'];
    // add hidden
    protected $hidden = ['created_at', 'updated_at'];

    public function entries(): HasMany
    {
        return $this->hasMany(MasterEntry::class);
    }
}
