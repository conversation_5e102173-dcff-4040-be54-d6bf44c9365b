<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class AssetCategory extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'name', 'is_compliance', 'is_fire_safety', 'survey_required'
    ];

    protected $casts = [
        'is_compliance' => 'boolean',
        'is_fire_safety' => 'boolean',
        'survey_required' => 'boolean',
    ];

    public function assets(): HasMany
    {
        return $this->hasMany(Asset::class);
    }

    public function checklists(): HasMany
    {
        return $this->hasMany(AssetChecklistMaster::class);
    }
}
