<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ShipCrewPosition extends Model
{
    protected $fillable = [
        'ship_id', 'designation_id', 'min_required', 'max_allowed', 'notes'
    ];

    public function ship(): BelongsTo
    {
        return $this->belongsTo(Ship::class);
    }

    public function designation(): BelongsTo
    {
        return $this->belongsTo(Designation::class, 'designation_id');
    }
}
