<?php
// app/Models/AssetLifecycleLog.php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AssetLifecycleLog extends Model
{
    protected $fillable = [
        'ship_asset_id', 'event_type_id', 'remarks', 'recorded_by', 'event_time'
    ];

    protected $casts = [
        'event_time' => 'datetime',
    ];

    public function shipAsset(): BelongsTo
    {
        return $this->belongsTo(ShipAsset::class);
    }

    public function eventType(): BelongsTo
    {
        return $this->belongsTo(MasterEntry::class, 'event_type_id');
    }

    public function recordedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'recorded_by');
    }
}
