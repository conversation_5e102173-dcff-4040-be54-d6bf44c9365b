<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class MockDrillParticipant extends Model
{
    protected $fillable = ['drill_id', 'crew_id', 'participated', 'remarks'];
    protected $casts = ['participated' => 'boolean'];

    public function drill(): BelongsTo
    {
        return $this->belongsTo(Drill::class);
    }

    public function crew(): BelongsTo
    {
        return $this->belongsTo(Crew::class);
    }
}
