<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class CrewCertificateRequirementSet extends Model
{
    protected $fillable = [
        // 'ship_crew_position_requirement_id',
        'logic_type',
        'min_required',
        'name'
    ];

    // public function positionRequirement()
    // {
    // //    return $this->belongsTo(ShipCrewPositionRequirement::class);
    //       return $this->belongsTo(ShipCrewPositionRequirement::class, 'ship_crew_position_requirement_id');
    // }

    public function requirements()
    {
        return $this->certificates();
    }
    public function certificates()
    {
        return $this->hasMany(CrewCertificateRequirement::class, 'requirement_set_id');
    }



    public function positions()
    {
        return $this->belongsToMany(
            ShipCrewPositionRequirement::class,
            'position_requirement_set_assignments',
            'requirement_set_id',
            'ship_crew_position_requirement_id'
        );
    }

    public function groups()
    {
        return $this->belongsToMany(
            RequirementSetGroup::class,
            'requirement_set_group_sets',
            'requirement_set_id',
            'requirement_set_group_id'
        );
    }
}
