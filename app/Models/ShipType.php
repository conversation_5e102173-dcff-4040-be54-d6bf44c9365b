<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class ShipType extends Model
{
    use SoftDeletes;

    protected $fillable = ['code', 'name', 'description', 'imo_category', 'is_active'];
    protected $casts = ['is_active' => 'boolean'];

    public function ships(): HasMany
    {
        return $this->hasMany(Ship::class);
    }
}
