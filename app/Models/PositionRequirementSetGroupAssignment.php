<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class PositionRequirementSetGroupAssignment extends Model
{
    //

    // add fillable

    protected $fillable = [
        'ship_crew_position_requirement_id',
        'requirement_set_group_id',
    ];

    public function position()
    {
        return $this->belongsTo(ShipCrewPositionRequirement::class, 'ship_crew_position_requirement_id');
    }

    public function requirementSetGroup()
    {
        return $this->belongsTo(RequirementSetGroup::class, 'requirement_set_group_id');
    }

    public function group()
    {
         return $this->requirementSetGroup();
    }
    // add guaded
    protected $guarded = ['id'];
    // add hidden
    protected $hidden = ['created_at', 'updated_at'];
}
