<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CrewDocument extends Model
{
    protected $fillable = [
        'crew_id', 'document_master_id', 'document_path',
        'expiry_date', 'verified'
    ];

    protected $casts = [
        'expiry_date' => 'date',
        'verified' => 'boolean',
    ];

    public function crew(): BelongsTo
    {
        return $this->belongsTo(Crew::class);
    }

    public function master(): BelongsTo
    {
        return $this->belongsTo(CrewDocumentMaster::class, 'document_master_id');
    }
}
