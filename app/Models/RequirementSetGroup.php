<?php

// app/Models/RequirementSetGroup.php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class RequirementSetGroup extends Model
{
    protected $fillable = ['name', 'description'];

    // Sets in this group (AND logic within group)
    public function sets()
    {
        return $this->belongsToMany(
            CrewCertificateRequirementSet::class,
            'requirement_set_group_sets',
            'requirement_set_group_id',
            'requirement_set_id'
        );
    }

    // app/Models/RequirementSetGroup.php

public function setGroupPositions()
{
    return $this->belongsToMany(
        \App\Models\ShipCrewPositionRequirement::class,
        'position_requirement_set_group_assignments',
        'requirement_set_group_id',
        'ship_crew_position_requirement_id'
    );
}

    // Positions assigned this group (OR logic between groups for a position)
    public function positions()
    {
        return $this->belongsToMany(
            ShipCrewPositionRequirement::class,
            'position_requirement_set_group_assignments',
            'requirement_set_group_id',
            'ship_crew_position_requirement_id'
        );
    }
}
