<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class RequirementSetGroupSet extends Model
{
    protected $table = 'requirement_set_group_sets';

    protected $fillable = [
        'requirement_set_group_id',
        'requirement_set_id',
    ];

    public $timestamps = true;

    // Relationships (optional for clarity)
    public function group()
    {
        return $this->belongsTo(RequirementSetGroup::class, 'requirement_set_group_id');
    }

    public function set()
    {
        return $this->belongsTo(CrewCertificateRequirementSet::class, 'requirement_set_id');
    }
}
