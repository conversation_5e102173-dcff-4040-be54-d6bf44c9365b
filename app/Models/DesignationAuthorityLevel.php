<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class DesignationAuthorityLevel extends Model
{
    use HasFactory;

    protected $table = 'designation_authority_levels';
    protected $fillable = ['designation_id', 'authority_level_id'];

    public function designation()
    {
        return $this->belongsTo(Designation::class);
    }

    public function authorityLevel()
    {
        return $this->belongsTo(AuthorityLevel::class);
    }
}
