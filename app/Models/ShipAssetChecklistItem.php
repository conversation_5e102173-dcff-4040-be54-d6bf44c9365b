<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ShipAssetChecklistItem extends Model
{
    protected $fillable = [
        'ship_asset_id', 'checklist_master_id', 'is_for_survey'
    ];

    protected $casts = ['is_for_survey' => 'boolean'];

    public function shipAsset(): BelongsTo
    {
        return $this->belongsTo(ShipAsset::class);
    }

    public function master(): BelongsTo
    {
        return $this->belongsTo(AssetChecklistMaster::class, 'checklist_master_id');
    }

    public function answers(): HasMany
    {
        return $this->hasMany(ShipAssetChecklistAnswer::class, 'checklist_item_id');
    }

    public function metrics()
    {
        return $this->hasMany(ShipAssetChecklistItemMetric::class);
    }

}
