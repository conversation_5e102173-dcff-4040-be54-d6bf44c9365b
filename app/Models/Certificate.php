<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class Certificate extends Model
{
    use HasFactory;

    protected $fillable = [
        'certificate_master_id', 'certifiable_type', 'certifiable_id',
        'certificate_number', 'issue_date', 'expiry_date', 'renewal_date',
        'issuing_authority', 'notes'
    ];

    protected $dates = ['issue_date', 'expiry_date', 'renewal_date'];

    public function master(): BelongsTo
    {
        return $this->belongsTo(CertificateMaster::class, 'certificate_master_id');
    }

    public function certificateMaster(): BelongsTo
    {
        return $this->belongsTo(CertificateMaster::class, 'certificate_master_id');
    }
    public function certifiable(): MorphTo
    {
        return $this->morphTo();
    }

    public function reminders(): MorphMany
    {
        return $this->morphMany(Reminder::class, 'remindable');
    }
}
