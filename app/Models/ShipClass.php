<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class ShipClass extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'code', 'name', 'description', 'classification_society_id', 'is_active'
    ];

    protected $casts = ['is_active' => 'boolean'];

    public function society(): BelongsTo
    {
        return $this->belongsTo(ClassificationSociety::class, 'classification_society_id');
    }

    public function ships(): HasMany
    {
        return $this->hasMany(Ship::class);
    }
}
