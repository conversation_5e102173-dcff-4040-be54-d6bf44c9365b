<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Spatie\Permission\Models\Permission;

class MobileModule extends Model
{
    use HasFactory;

    protected $fillable = ['name', 'key', 'icon', 'is_active'];

    public function permissions()
    {
        return $this->belongsToMany(Permission::class, 'mobile_module_permissions');
    }
}
