<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class DailyAssetLog extends Model
{
    protected $fillable = [
        'ship_asset_id', 'log_time', 'notes', 'recorded_by',
    ];

    protected $dates = ['log_time'];

    public function shipAsset(): BelongsTo
    {
        return $this->belongsTo(ShipAsset::class);
    }

    public function metrics(): HasMany
    {
        return $this->hasMany(AssetLogMetric::class);
    }

    public function recorder(): BelongsTo
    {
        return $this->belongsTo(User::class, 'recorded_by');
    }
}
