<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ShipDocument extends Model
{
    use HasFactory;

    protected $fillable = ['ship_id', 'document_master_id', 'document_path', 'notes'];

    public function ship(): BelongsTo
    {
        return $this->belongsTo(Ship::class);
    }

    public function master(): BelongsTo
    {
        return $this->belongsTo(ShipDocumentMaster::class, 'document_master_id');
    }
}
