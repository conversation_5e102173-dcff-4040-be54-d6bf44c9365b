<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CrewChecklist extends Model
{
    protected $fillable = [
        'crew_history_id', 'checklist_master_id', 'value', 'image',
        'completed_by', 'completed_at'
    ];

    protected $casts = [
        'completed_at' => 'datetime',
    ];

    public function history(): BelongsTo
    {
        return $this->belongsTo(CrewHistory::class, 'crew_history_id');
    }

    public function master(): BelongsTo
    {
        return $this->belongsTo(CrewChecklistMaster::class, 'checklist_master_id');
    }

    public function completedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'completed_by');
    }
}
