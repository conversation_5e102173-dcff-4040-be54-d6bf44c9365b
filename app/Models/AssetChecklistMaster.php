<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class AssetChecklistMaster extends Model
{
    protected $fillable = [
        'category_id', 'question', 'input_type_id', 'options',
        'image_required', 'is_for_survey', 'default_tag_id'
    ];

    protected $casts = [
        'options' => 'json',
        'image_required' => 'boolean',
        'is_for_survey' => 'boolean',
    ];

    public function category(): BelongsTo
    {
        return $this->belongsTo(AssetCategory::class);
    }

    public function inputType(): BelongsTo
    {
        return $this->belongsTo(MasterEntry::class, 'input_type_id');
    }

    public function defaultTag(): BelongsTo
    {
        return $this->belongsTo(Tag::class);
    }

    public function items(): HasMany
    {
        return $this->hasMany(ShipAssetChecklistItem::class, 'checklist_master_id');
    }
}
