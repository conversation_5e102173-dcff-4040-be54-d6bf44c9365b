<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AssetTransaction extends Model
{
   protected $fillable = [
        'ship_asset_id', 'transaction_type_id', 'metric_type_id',
        'quantity', 'old_level', 'new_level', 'recorded_by', 'notes', 'transaction_time'
    ];

    protected $casts = [
        'quantity'      => 'decimal:4',
        'old_level'     => 'decimal:4',
        'new_level'     => 'decimal:4',
        'transaction_time' => 'datetime',
    ];

    public function transactionType(): BelongsTo
    {
        return $this->belongsTo(MasterEntry::class, 'transaction_type_id');
    }

    public function shipAsset(): BelongsTo
    {
        return $this->belongsTo(ShipAsset::class);
    }

    public function recorder(): BelongsTo
    {
        return $this->belongsTo(User::class, 'recorded_by');
    }

     public function metricType(): BelongsTo
    {
        return $this->belongsTo(MetricType::class, 'metric_type_id');
    }






    public function recordedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'recorded_by');
    }

}
