<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ShipAssetChecklistRun extends Model
{
    protected $fillable = [
        'ship_asset_id', 'survey_id', 'performed_by', 'type_id',
        'notes', 'submitted_at'
    ];

    protected $casts = ['submitted_at' => 'datetime'];

    public function type(): BelongsTo
    {
        return $this->belongsTo(MasterEntry::class, 'type_id');
    }

    public function shipAsset(): BelongsTo
    {
        return $this->belongsTo(ShipAsset::class);
    }

    public function survey(): BelongsTo
    {
        return $this->belongsTo(Survey::class);
    }

    public function performer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'performed_by');
    }

    public function answers(): HasMany
    {
        return $this->hasMany(ShipAssetChecklistAnswer::class, 'run_id');
    }
}
