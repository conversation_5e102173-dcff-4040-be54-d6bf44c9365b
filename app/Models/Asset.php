<?php

namespace App\Models;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class Asset extends Model implements HasMedia
{
    use HasFactory, SoftDeletes, InteractsWithMedia;

    protected $fillable = [
        'name', 'type_id', 'category_id', 'is_compliance',
        'is_fire_safety', 'survey_required', 'unit',
        'consumable_type_id', 'default_spec', 'image'
    ];

    protected $casts = [
        'is_compliance' => 'boolean',
        'is_fire_safety' => 'boolean',
        'survey_required' => 'boolean',
        'default_spec' => 'json',
    ];

    public function type(): BelongsTo
    {
        return $this->belongsTo(MasterEntry::class, 'type_id');
    }

    public function consumableType(): BelongsTo
    {
        return $this->belongsTo(MasterEntry::class, 'consumable_type_id');
    }

    public function category(): BelongsTo
    {
        return $this->belongsTo(AssetCategory::class);
    }

    public function shipAssets(): HasMany
    {
        return $this->hasMany(ShipAsset::class);
    }

    public function specs(): HasMany
    {
        return $this->hasMany(AssetSpec::class);
    }

    public function movements(): HasMany
    {
        return $this->hasMany(AssetMovement::class);
    }

	  public function documents(): HasMany
    {
        return $this->hasMany(AssetDocument::class);
    }

}
