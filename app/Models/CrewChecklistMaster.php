<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class CrewChecklistMaster extends Model
{
    protected $fillable = [
        'name', 'event_type_id', 'designation_ids', 'question',
        'input_type_id', 'options', 'is_active', 'allow_multiple'
    ];

    protected $casts = [
        'designation_ids' => 'array',
        'options' => 'array',
        'is_active' => 'boolean',
        'allow_multiple' => 'boolean',
    ];

    public function eventType(): BelongsTo
    {
        return $this->belongsTo(MasterEntry::class, 'event_type_id');
    }

    public function inputType(): BelongsTo
    {
        return $this->belongsTo(MasterEntry::class, 'input_type_id');
    }

    public function checklists(): HasMany
    {
        return $this->hasMany(CrewChecklist::class, 'checklist_master_id');
    }
}
