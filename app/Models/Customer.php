<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Customer extends Model
{
    protected $fillable = [
        'user_id', 'company_name', 'gst_no', 'address',
        'country', 'contact_person', 'contact_email', 'contact_phone'
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function towageRequests(): HasMany
    {
        return $this->hasMany(TowageRequest::class, 'customer_id');
    }
}
