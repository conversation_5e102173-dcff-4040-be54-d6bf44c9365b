<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class MobileModulePermission extends Model
{
    use HasFactory;

    protected $table = 'mobile_module_permissions';
    protected $fillable = ['mobile_module_id', 'permission_id'];

    public function module()
    {
        return $this->belongsTo(MobileModule::class, 'mobile_module_id');
    }
    public function mobileModule()
    {
        return $this->belongsTo(MobileModule::class, 'mobile_module_id');
    }
    public function permission()
    {
        return $this->belongsTo(\Spatie\Permission\Models\Permission::class);
    }
}
