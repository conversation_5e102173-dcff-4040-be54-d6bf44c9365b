<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Survey extends Model
{
    protected $fillable = [
        'type_id', 'ship_id', 'schedule_id', 'assigned_to',
        'status_id', 'started_at', 'due_date', 'completed_at'
    ];

    protected $casts = [
        'started_at' => 'datetime',
        'due_date' => 'datetime',
        'completed_at' => 'datetime',
    ];

    public function type(): BelongsTo
    {
        return $this->belongsTo(MasterEntry::class, 'type_id');
    }

    public function status(): BelongsTo
    {
        return $this->belongsTo(MasterEntry::class, 'status_id');
    }

    public function ship(): BelongsTo
    {
        return $this->belongsTo(Ship::class);
    }

    public function schedule(): BelongsTo
    {
        return $this->belongsTo(SurveySchedule::class);
    }

    public function assignedTo(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    public function checklistRuns(): HasMany
    {
        return $this->hasMany(ShipAssetChecklistRun::class);
    }
}
