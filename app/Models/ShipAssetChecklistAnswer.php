<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ShipAssetChecklistAnswer extends Model
{
    protected $fillable = [
        'run_id', 'checklist_item_id', 'value',
        'image_before', 'image_after', 'notes', 'tag_id'
    ];

    public function run(): BelongsTo
    {
        return $this->belongsTo(ShipAssetChecklistRun::class, 'run_id');
    }

    public function item(): BelongsTo
    {
        return $this->belongsTo(ShipAssetChecklistItem::class, 'checklist_item_id');
    }

    public function tag(): BelongsTo
    {
        return $this->belongsTo(Tag::class);
    }
}
