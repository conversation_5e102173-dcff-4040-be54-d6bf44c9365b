<?php

// app/Models/ShipAsset.php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\MediaLibrary\HasMedia;
use <PERSON><PERSON>\MediaLibrary\InteractsWithMedia;

class ShipAsset extends Model  implements HasMedia
{
    use SoftDeletes, InteractsWithMedia;

    protected $fillable = [
        'ship_id',
        'asset_id',
        'location_id',
        'override_image',
        'override_spec',
        'quantity',
        'current_level',
        'last_measured_at',
        'primary_metric_type_id',
        'cert_info',
        'is_compliance',
        'is_fire_safety',
        'survey_required',
        'notes',
        'created_by'
    ];

    protected $casts = [
        'override_spec' => 'json',
        'cert_info' => 'json',
        'is_compliance' => 'boolean',
        'is_fire_safety' => 'boolean',
        'survey_required' => 'boolean',
        'last_measured_at' => 'datetime',
        'current_level' => 'decimal:4',
    ];

    public function ship(): BelongsTo
    {
        return $this->belongsTo(Ship::class);
    }

    public function asset(): BelongsTo
    {
        return $this->belongsTo(Asset::class);
    }

    public function location(): BelongsTo
    {
        return $this->belongsTo(Location::class);
    }

    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function transactions(): HasMany
    {
        return $this->hasMany(AssetTransaction::class);
    }

    public function checklistItems(): HasMany
    {
        return $this->hasMany(ShipAssetChecklistItem::class);
    }

    public function checklistRuns(): HasMany
    {
        return $this->hasMany(ShipAssetChecklistRun::class);
    }
    public function status(): BelongsTo
    {
        return $this->belongsTo(MasterEntry::class, 'status_id');
    }

    // app/Models/ShipAsset.php

    public function specs()
    {
        return $this->hasMany(ShipAssetSpec::class);
    }

    public function certificates()
    {
        return $this->morphMany(\App\Models\Certificate::class, 'certifiable');
    }

    public function primaryMetricType()
{
    return $this->belongsTo(MetricType::class, 'primary_metric_type_id');
}
public function metrics()
{
    return $this->hasMany(ShipAssetMetric::class);
}
}
