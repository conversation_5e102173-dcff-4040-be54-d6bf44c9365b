<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Ship extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'imo_number', 'official_number', 'name', 'call_sign', 'ship_type_id',
        'ship_class_id', 'company_id', 'length_overall', 'breadth', 'depth',
        'draft', 'gross_tonnage', 'net_tonnage', 'deadweight', 'year_built',
        'builder', 'hull_number', 'commission_date', 'decommission_date',
        'flag_id', 'port_of_registry', 'notes', 'status_id'
    ];

    protected $casts = [
        'length_overall' => 'decimal:2',
        'breadth' => 'decimal:2',
        'depth' => 'decimal:2',
        'draft' => 'decimal:2',
        'gross_tonnage' => 'decimal:2',
        'net_tonnage' => 'decimal:2',
        'deadweight' => 'decimal:2',
        'commission_date' => 'date',
        'decommission_date' => 'date',
    ];

    public function status(): BelongsTo
    {
        return $this->belongsTo(MasterEntry::class, 'status_id');
    }

    public function type(): BelongsTo
    {
        return $this->belongsTo(ShipType::class);
    }

    public function shipClass(): BelongsTo
    {
        return $this->belongsTo(ShipClass::class);
    }

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    public function flag(): BelongsTo
    {
        return $this->belongsTo(Country::class, 'flag_id');
    }

    public function locations(): HasMany
    {
        return $this->hasMany(Location::class);
    }

    public function assets(): HasMany
    {
        return $this->hasMany(ShipAsset::class);
    }

    public function currentCrew(): HasMany
    {
        return $this->hasMany(Crew::class, 'current_ship_id');
    }

    public function certificates()
{
    return $this->morphMany(\App\Models\Certificate::class, 'certifiable');
}

public function crewPositions()
{
    return $this->hasMany(ShipCrewPosition::class);
}

    public function documents()
    {
        return $this->hasMany(ShipDocument::class);
    }

    public function reminders()
    {
        return $this->morphMany(Reminder::class, 'remindable');
    }
}
