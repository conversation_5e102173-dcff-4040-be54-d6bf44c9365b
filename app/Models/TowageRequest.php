<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class TowageRequest extends Model
{
    protected $fillable = [
        'customer_id', 'ship_id', 'purpose', 'eta', 'anchor_point',
        'location_details', 'status_id', 'assigned_tug_id', 'fleet_supervisor_id'
    ];

    protected $casts = ['eta' => 'datetime'];

    public function status(): BelongsTo
    {
        return $this->belongsTo(MasterEntry::class, 'status_id');
    }

    public function customer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'customer_id');
    }

    public function ship(): BelongsTo
    {
        return $this->belongsTo(Ship::class);
    }

    public function assignedTug(): BelongsTo
    {
        return $this->belongsTo(Ship::class, 'assigned_tug_id');
    }

    public function fleetSupervisor(): BelongsTo
    {
        return $this->belongsTo(User::class, 'fleet_supervisor_id');
    }
}
