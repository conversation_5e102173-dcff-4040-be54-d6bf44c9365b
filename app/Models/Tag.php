<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphToMany;

class Tag extends Model
{
    protected $fillable = ['name', 'description', 'module'];

    public function shipAssets(): MorphToMany
    {
        return $this->morphedByMany(ShipAsset::class, 'taggable');
    }

    public function surveys(): MorphToMany
    {
        return $this->morphedByMany(Survey::class, 'taggable');
    }

    public function checklistAnswers(): MorphToMany
    {
        return $this->morphedByMany(ShipAssetChecklistAnswer::class, 'taggable');
    }
}
