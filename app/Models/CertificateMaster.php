<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
class CertificateMaster extends Model
{
    use HasFactory;

    protected $fillable = [
        'name', 'description', 'is_mandatory', 'ship_type_ids',
        'ship_class_ids', 'validity_period', 'requires_renewal', 'validity_unit_id','validity_value'
    ];
    protected $table = 'certificate_masters';
    protected $casts = [
           'is_mandatory' => 'boolean',
    'requires_renewal' => 'boolean',
        'ship_type_ids' => 'array',
        'ship_class_ids' => 'array',
    ];

    public function certificates(): HasMany
    {
        return $this->hasMany(Certificate::class);
    }

    public function validityUnit(): BelongsTo
{
    return $this->belongsTo(MasterEntry::class, 'validity_unit_id');
}
}
