<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Drill extends Model
{
    protected $fillable = [
        'schedule_id', 'ship_id', 'drill_type', 'start_time', 'end_time', 'images', 'notes'
    ];

    protected $casts = [
        'start_time' => 'datetime',
        'end_time' => 'datetime',
        'images' => 'json',
    ];

    public function schedule(): BelongsTo
    {
        return $this->belongsTo(DrillSchedule::class);
    }

    public function ship(): BelongsTo
    {
        return $this->belongsTo(Ship::class);
    }

    public function participants(): HasMany
    {
        return $this->hasMany(MockDrillParticipant::class);
    }
}
