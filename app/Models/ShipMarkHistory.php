<?php

// app/Models/ShipMarkHistory.php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ShipMarkHistory extends Model
{
    protected $fillable = ['ship_id', 'ship_mark_id', 'changed_at', 'changed_by', 'notes'];

    public function ship()      { return $this->belongsTo(Ship::class); }
    public function mark()      { return $this->belongsTo(ShipMark::class, 'ship_mark_id'); }
    public function changer()   { return $this->belongsTo(User::class, 'changed_by'); }
}

