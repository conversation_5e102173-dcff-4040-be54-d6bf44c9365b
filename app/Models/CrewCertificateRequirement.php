<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class CrewCertificateRequirement extends Model
{
    protected $fillable = [
        // 'ship_crew_position_requirement_id',
        'certificate_type_id',
        'requirement_set_id',   // nullable, for future grouping
        'is_primary',           // if you want to use it (boolean)
        'remarks'
    ];

    /**
     * Relationship: Certificate Type
     */
    public function certificateType()
    {
        return $this->belongsTo(CrewCertificateType::class, 'certificate_type_id');
    }

    /**
     * Relationship: Requirement Set (can be null)
     */
    public function requirementSet()
    {
        return $this->belongsTo(CrewCertificateRequirementSet::class, 'requirement_set_id');
    }

    /**
     * Relationship: Ship Crew Position Requirement
     */
    // public function positionRequirement()
    // {
    //     return $this->belongsTo(ShipCrewPositionRequirement::class, 'ship_crew_position_requirement_id');
    // }

    public function set()
    {
        return $this->belongsTo(CrewCertificateRequirementSet::class, 'requirement_set_id');
    }
}
