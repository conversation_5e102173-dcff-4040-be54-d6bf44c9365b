<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ShipAssetMetric extends Model
{
    protected $fillable = [
        'is_rob',
        'ship_asset_id',
        'metric_type_id',
        'is_required',
        'is_transactional',
        'is_primary',
        'min_threshold',
        'max_threshold',
        'alert_on_min_breach',
        'alert_on_max_breach',
        'alert_type',
        'reminder_days',
        'unit',
        'notes',
    ];

    protected $casts = [
        'is_required'         => 'boolean',
        'is_primary'          => 'boolean',
        'is_transactional'    => 'boolean',
        'is_rob'              => 'boolean',
        'alert_on_min_breach' => 'boolean',
        'alert_on_max_breach' => 'boolean',
        'min_threshold'       => 'decimal:4',
        'max_threshold'       => 'decimal:4',
        'reminder_days'       => 'array', // stored as JSON, e.g. [1,7,30]
    ];

    // --- RELATIONSHIPS ---

    public function shipAsset(): BelongsTo
    {
        return $this->belongsTo(ShipAsset::class);
    }

   public function metricType()
    {
        return $this->belongsTo(MetricType::class, 'metric_type_id');
    }


    public function alertType(): BelongsTo
    {
        return $this->belongsTo(\App\Models\MasterEntry::class, 'alert_type');
    }
}
