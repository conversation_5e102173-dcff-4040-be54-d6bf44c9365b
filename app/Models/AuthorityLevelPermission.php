<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class AuthorityLevelPermission extends Model
{
    use HasFactory;

    protected $table = 'authority_level_permission';
    protected $fillable = ['authority_level_id', 'permission_id'];

    public function authorityLevel()
    {
        return $this->belongsTo(AuthorityLevel::class);
    }

    public function permission()
    {
        return $this->belongsTo(\Spatie\Permission\Models\Permission::class);
    }
}
