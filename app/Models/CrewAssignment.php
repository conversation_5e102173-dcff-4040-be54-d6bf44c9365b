<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class CrewAssignment extends Model
{
    protected $fillable = [
        'crew_id',
        'ship_id',
        'ship_mark_id',
        'ship_crew_position_requirement_id',
        'assigned_at',
        'relieved_at',
        'status',
    ];

    protected $casts = [
        'assigned_at' => 'datetime',
        'relieved_at' => 'datetime',
        'status' => 'string',
    ];

    // Relationships

    public function crew()
    {
        return $this->belongsTo(Crew::class);
    }

    public function ship()
    {
        return $this->belongsTo(Ship::class);
    }

    public function shipMark()
    {
        return $this->belongsTo(ShipMark::class, 'ship_mark_id');
    }

    public function positionRequirement()
    {
        return $this->belongsTo(ShipCrewPositionRequirement::class, 'ship_crew_position_requirement_id');
    }

    // Optional shortcut to designation (via requirement)
    public function designation()
    {
        return $this->hasOneThrough(
            Designation::class,
            ShipCrewPositionRequirement::class,
            'id',                 // Foreign key on requirement
            'id',                 // Foreign key on designation
            'ship_crew_position_requirement_id', // Local FK
            'designation_id'      // Requirement -> designation_id
        );
    }
}
