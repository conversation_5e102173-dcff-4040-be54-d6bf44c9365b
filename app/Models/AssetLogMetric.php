<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AssetLogMetric extends Model
{
    protected $fillable = [
        'daily_asset_log_id',
        'ship_asset_metric_id',
        'value',
    ];

    public function dailyAssetLog(): BelongsTo
    {
        return $this->belongsTo(DailyAssetLog::class);
    }

    // App\Models\AssetLogMetric.php
    public function shipAssetMetric()
    {
        return $this->belongsTo(\App\Models\ShipAssetMetric::class, 'ship_asset_metric_id');
    }

    public function metricType()
    {
        // Through ShipAssetMetric
        return $this->hasOneThrough(
            \App\Models\MetricType::class,  // Or MasterEntry if you use master_entries
            \App\Models\ShipAssetMetric::class,
            'id', // ShipAssetMetric PK
            'id', // MetricType PK
            'ship_asset_metric_id', // Foreign key on AssetLogMetric
            'metric_type_id' // Foreign key on ShipAssetMetric
        );
    }
}
