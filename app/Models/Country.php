<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Country extends Model
{
    protected $fillable = [
        'name', 'iso2', 'iso3', 'phone_code',
        'currency_code', 'currency_name', 'emoji', 'active'
    ];

    protected $casts = ['active' => 'boolean'];

    public function ships(): HasMany
    {
        return $this->hasMany(Ship::class, 'flag_id');
    }
}
