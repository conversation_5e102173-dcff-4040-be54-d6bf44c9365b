<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class MasterEntry extends Model
{
    protected $fillable = [
        'master_type_id', 'code', 'name', 'meta',
        'order', 'is_default', 'is_active'
    ];

    protected $casts = [
        'meta' => 'array',
        'is_default' => 'boolean',
        'is_active' => 'boolean'
    ];

    public function type(): BelongsTo
    {
        return $this->belongsTo(MasterType::class, 'master_type_id');
    }

    protected static function booted()
    {
        static::addGlobalScope('active', function (Builder $builder) {
            $builder->where('is_active', true);
        });
    }
}
