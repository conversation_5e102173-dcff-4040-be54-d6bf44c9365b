<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class DesignationFeaturePermission extends Model
{
    protected $fillable = ['designation_id', 'feature_key', 'allowed'];
    protected $casts = ['allowed' => 'boolean'];

    public function designation(): BelongsTo
    {
        return $this->belongsTo(Designation::class);
    }
}

