<?php
// app/Models/PositionRequirementSetAssignment.php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class PositionRequirementSetAssignment extends Model
{
    protected $fillable = [
        'ship_crew_position_requirement_id',
        'requirement_set_id',
    ];

    public function position()
    {
        return $this->belongsTo(ShipCrewPositionRequirement::class, 'ship_crew_position_requirement_id');
    }

    public function requirementSet()
    {
        return $this->belongsTo(CrewCertificateRequirementSet::class, 'requirement_set_id');
    }
}
