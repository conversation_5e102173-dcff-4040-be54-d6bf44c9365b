<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ShipCrewPositionRequirement extends Model
{
    protected $fillable = [
        'ship_id', 'ship_mark_id', 'designation_id', 'min_required', 'max_allowed'
    ];

    protected $appends = ['display_name'];


    public function ship()
    {
        return $this->belongsTo(Ship::class);
    }
    public function shipMark()
    {
        return $this->belongsTo(ShipMark::class, 'ship_mark_id');
    }
    public function mark()
    {
        return $this->belongsTo(ShipMark::class, 'ship_mark_id');
    }

    public function designation()
    {
        return $this->belongsTo(Designation::class);
    }


    public function getDisplayNameAttribute()
    {
        return ($this->ship?->name ?? '-') . ' — ' .
            ($this->shipMark?->name ?? '-') . ' — ' .
            ($this->designation?->name ?? '-');
    }

    public function getDisplayMarkAttribute()
    {
        return ($this->shipMark?->name ?? '-');
    }

    public function getDisplayDesignationAttribute()
    {
        return ($this->designation?->name ?? '-');
    }
    public function getDisplayMinCountAttribute()
    {
        return ($this->min_count ?? '-');
    }
    public function getDisplayMaxCountAttribute()
    {
        return ($this->max_count ?? '-');
    }


    public function requirementSets()
    {
        return $this->belongsToMany(
            CrewCertificateRequirementSet::class,
            'position_requirement_set_assignments',
            'ship_crew_position_requirement_id',
            'requirement_set_id'
        );
    }

    public function requirementSetGroups()
    {
        return $this->belongsToMany(
            RequirementSetGroup::class,
            'position_requirement_set_group_assignments',
            'ship_crew_position_requirement_id',
            'requirement_set_group_id'
        );
    }

    public function requirementSetGroupAssignments()
    {
        return $this->hasMany(\App\Models\PositionRequirementSetGroupAssignment::class, 'ship_crew_position_requirement_id');
    }
}
