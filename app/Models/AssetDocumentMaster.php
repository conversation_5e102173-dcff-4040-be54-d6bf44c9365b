<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class AssetDocumentMaster extends Model
{
    use HasFactory;

    protected $fillable = ['name', 'description', 'is_required', 'asset_category_ids', 'file_types'];

    protected $casts = [
        'asset_category_ids' => 'array',
    ];

    public function documents(): HasMany
    {
        return $this->hasMany(AssetDocument::class);
    }
}
