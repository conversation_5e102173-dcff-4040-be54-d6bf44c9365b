<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AssetDocument extends Model
{
    use HasFactory;

    protected $fillable = ['ship_asset_id', 'document_master_id', 'document_path', 'notes'];

    public function shipAsset()
    {
        return $this->belongsTo(ShipAsset::class, 'ship_asset_id');
    }

    public function master(): BelongsTo
    {
        return $this->belongsTo(AssetDocumentMaster::class, 'document_master_id');
    }
}
