<?php


namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ShipAssetChecklistItemMetric extends Model
{
    protected $fillable = [
        'ship_asset_checklist_item_id',
        'metric_type_id',
        'is_required',
        'order',
        'notes',
    ];

    public function checklistItem()
    {
        return $this->belongsTo(ShipAssetChecklistItem::class);
    }

    public function metricType()
    {
        return $this->belongsTo(MetricType::class);
    }
}
