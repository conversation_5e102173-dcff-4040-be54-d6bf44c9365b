<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Reminder extends Model
{
    protected $fillable = [
        'remindable_type', 'remindable_id', 'type', 'message',
        'remind_at', 'is_sent', 'sent_at', 'user_id', 'notes'
    ];

    public function remindable(): MorphTo
    {
        return $this->morphTo();
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function sendNotification()
    {
        $user = $this->user;
        $user->notify(
            // new GenericReminderNotification($this) need to generate this as well to with laravel notifcation and filament nodtification
        );
        $this->update(['is_sent' => true, 'sent_at' => now()]);
    }
}
