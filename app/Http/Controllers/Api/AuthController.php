<?php

namespace App\Http\Controllers\Api;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Routing\Controller;
use Illuminate\Validation\ValidationException;

class AuthController extends Controller
{
    /**
     * Login user and return token with enriched user profile.
     */
    public function login(Request $request)
    {
        $request->validate([
            'email'    => 'required|email',
            'password' => 'required',
        ]);

        if (!Auth::attempt($request->only('email', 'password'))) {
            throw ValidationException::withMessages([
                'email' => ['The provided credentials are incorrect.'],
            ]);
        }

        /** @var User $user */
        $user = User::where('email', $request->email)->firstOrFail();

        // Clear existing tokens
        $user->tokens()->delete();

        // Sync merged permissions
        $this->syncUserPermissions($user);

        // Generate token
        $token = $user->createToken('mobile_login')->plainTextToken;

        return response()->json([
            'token' => $token,
            'user' => [
                'id'               => $user->id,
                'name'             => $user->name,
                'email'            => $user->email,
                'roles'            => $user->roles->pluck('name'),
                'designation'      => optional($user->designation)->name,
                'authority_levels' => $user->authorityLevels->pluck('name'),
                'modules'          => $this->getUserModulePermissions($user),
            ],
        ]);
    }

    /**
     * Logout and delete all tokens.
     */
    public function logout()
    {
        Auth::user()->tokens()->delete();

        return response()->json([
            'success' => true,
            'message' => 'Logged out successfully.'
        ]);
    }

    /**
     * Sync permissions from role + authority level + designation.
     */
    protected function syncUserPermissions(User $user): void
    {
        $permissions = collect();

        foreach ($user->authorityLevels as $auth) {
            $permissions = $permissions->merge($auth->permissions);
        }

        if ($user->designation) {
            foreach ($user->designation->authorityLevels as $auth) {
                $permissions = $permissions->merge($auth->permissions);
            }
        }

        foreach ($user->roles as $role) {
            $permissions = $permissions->merge($role->permissions);
        }

        $user->syncPermissions($permissions->pluck('name')->unique());
    }

    /**
     * Build module-permission list.
     */
    protected function getUserModulePermissions(User $user): array
    {
        $userPermissions = $user->getAllPermissions()->pluck('name');

        return \App\Models\MobileModule::with('permissions')->get()->map(function ($module) use ($userPermissions) {
            $actions = [];

            foreach ($module->permissions as $perm) {
                if ($userPermissions->contains($perm->name)) {
                    $actions[] = str($perm->name)->after("{$module->key}.");
                }
            }

            return [
                'name'        => $module->name,
                'key'         => $module->key,
                'icon'        => $module->icon,
                'permissions' => $actions,
            ];
        })->filter(fn ($m) => !empty($m['permissions']))->values()->toArray();
    }
}
