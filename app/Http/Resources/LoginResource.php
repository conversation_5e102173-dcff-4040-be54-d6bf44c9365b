<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class LoginResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'token' => $this['token'],
            'user' => [
                'id' => $this['user']->id,
                'name' => $this['user']->name,
                'email' => $this['user']->email,
                'roles' => $this['user']->roles->pluck('name'),
                'designation' => $this['user']->designation?->name,
                'authority_levels' => $this['user']->authorityLevels->pluck('name'),
            ],
            'modules' => $this['modules'], // List of modules and allowed permissions
        ];
    }
}
