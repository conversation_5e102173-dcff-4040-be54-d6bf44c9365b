# 🚢 Marine360 Admin Panel (Built on Kaido Kit)

A production-ready FilamentPHP Admin Panel for the **Marine Industry**, tailored for fleet, asset, and crew management — built on top of [Kaido Kit](https://github.com/siubie/kaido-kit) for rapid, modular development.

> ⚓ Powered by Laravel 12, PHP 8.3+, and FilamentPHP 3.2+

![PHP Version](https://img.shields.io/badge/PHP-8.3-blue?style=flat-square&logo=php)
![Laravel Version](https://img.shields.io/badge/Laravel-12.0-red?style=flat-square&logo=laravel)
![Filament Version](https://img.shields.io/badge/Filament-3.2-purple?style=flat-square)
![License](https://img.shields.io/badge/License-MIT-blue?style=flat-square)
![Status](https://img.shields.io/badge/Status-In%20Development-yellow?style=flat-square)

---

## 🌊 About Marine360

Marine360 is a smart admin solution for managing:

- 🛠️ **Ship Assets & Maintenance**
- 🧑‍✈️ **Crew Management**
- 📦 **Towage & Operations**
- 📋 **Checklists, Logs, Surveys**
- 📡 **API Support for Mobile Apps**

Built on **Kaido Kit**, it comes bundled with advanced plugins, RBAC, API layer, and pre-configured dev tools.

---

## ✨ Features (Inherited from Kaido Kit)

- 🧱 Rapid CRUD generation
- 🛡️ Role & Permission (via Filament Shield)
- 📤 Export / 📥 Import support
- 🔐 Social Login (Google)
- 🔁 User Impersonation
- 🧾 Dynamic Settings (via Spatie Settings)
- 📁 Media Manager
- 📡 Ready-to-use API (Sanctum + Shield)
- 🧩 Plugin Ready Architecture

---

## 🚀 Quick Start (Local)

1. **Clone project**
    ```bash
    git clone https://github.com/YOUR-USERNAME/marine360.git
    cd marine360
    ```

2. **Install PHP deps**
    ```bash
    composer install
    ```

3. **Install JS deps**
    ```bash
    npm install && npm run dev
    ```

4. **Copy & update `.env`**
    ```bash
    cp .env.example .env
    # Edit DB config + optional GOOGLE & RESEND keys
    ```

5. **Run migrations**
    ```bash
    php artisan migrate --seed
    ```

6. **Serve app**
    ```bash
    php artisan serve
    ```

7. **Generate permissions + set super admin**
    ```bash
    php artisan shield:generate --all
    php artisan shield:super-admin
    ```

---

## 🐳 Run with Laravel Sail (Docker)

```bash
# 1. Clone + setup .env
git clone https://github.com/YOUR-USERNAME/marine360.git
cd marine360
cp .env.example .env

# 2. Install deps
composer install

# 3. Sail setup
composer require laravel/sail --dev
php artisan sail:install
./vendor/bin/sail up -d

# 4. DB + Key
./vendor/bin/sail artisan key:generate
./vendor/bin/sail artisan migrate --seed
