<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ship_asset_metric_schedules', function (Blueprint $table) {
            $table->id();
            $table->foreignId('ship_asset_id')->constrained()->onDelete('cascade');
            $table->foreignId('metric_type_id')->constrained('master_entries');
            $table->foreignId('frequency_type_id')->constrained('master_entries'); // daily, weekly, etc.
            $table->timestamp('last_logged_at')->nullable();
            $table->boolean('is_mandatory')->default(false);
            $table->text('notes')->nullable();
            $table->foreignId('created_by')->nullable()->constrained('users')->nullOnDelete();
            $table->timestamps();
        });
        // Index for faster lookups

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ship_asset_metric_schedules');
    }
};
