<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customers', function (Blueprint $table) {
             $table->id();
    $table->foreignId('user_id')->constrained()->unique();
    $table->string('company_name', 150);
    $table->string('gst_no', 50)->nullable();
    $table->text('address')->nullable();
    $table->string('country', 100)->nullable();
    $table->string('contact_person', 100);
    $table->string('contact_email', 150);
    $table->string('contact_phone', 20);
    $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customers');
    }
};
