<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // create_ship_marks_table.php
        Schema::create('ship_marks', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // e.g., 'Harbour', 'IV', 'OceanDeep'
            $table->string('code')->unique(); // 'harbour', 'iv', 'oceandeep'
            $table->string('description')->nullable();
            $table->timestamps();
        });

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ship_marks');
    }
};
