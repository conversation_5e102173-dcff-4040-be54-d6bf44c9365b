<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        //
        Schema::table('ship_asset_metrics', function (Blueprint $table) {
            $table->dropForeign(['metric_type_id']); // Remove old master_entries relation if any
            $table->unsignedBigInteger('metric_type_id')->change(); // Make sure it's bigInt
            $table->foreign('metric_type_id')->references('id')->on('metric_types');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
