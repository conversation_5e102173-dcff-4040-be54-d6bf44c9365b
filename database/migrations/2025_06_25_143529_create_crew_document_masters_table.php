<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('crew_document_masters', function (Blueprint $table) {
             $table->id();
    $table->string('name', 100);
    $table->text('description')->nullable();
    $table->boolean('is_mandatory')->default(true);
    $table->string('validity_period', 50)->nullable()->comment('e.g., 1 year, 6 months');
    $table->json('designation_ids')->nullable()->comment('Null = all designations');
    $table->json('event_triggers')->nullable()->comment('onboard, offboard, promotion');
    $table->string('file_types', 100)->nullable();
    $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('crew_document_masters');
    }
};
