<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ship_asset_metrics', function (Blueprint $table) {
    $table->id();
    $table->foreignId('ship_asset_id')->constrained()->onDelete('cascade');
    $table->foreignId('metric_type_id')->constrained('master_entries');
    $table->boolean('is_required')->default(true);
    $table->boolean('is_primary')->default(false);
    $table->decimal('min_threshold', 12, 4)->nullable();
    $table->decimal('max_threshold', 12, 4)->nullable();
    $table->boolean('alert_on_min_breach')->default(false);
    $table->boolean('alert_on_max_breach')->default(false);
    $table->foreignId('alert_type_id')->nullable()->constrained('master_entries')->nullOnDelete();

    $table->json('reminder_days')->nullable(); // e.g. [1,7,30]
    $table->string('unit', 20)->nullable();
    $table->text('notes')->nullable();
    $table->timestamps();
    $table->unique(['ship_asset_id', 'metric_type_id']);
});
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ship_asset_metrics');
    }
};
