<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('mock_drill_participants', function (Blueprint $table) {
           $table->id();
    $table->foreignId('drill_id')->constrained();
    $table->foreignId('crew_id')->constrained();
    $table->boolean('participated')->default(true);
    $table->text('remarks')->nullable();
    $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('mock_drill_participants');
    }
};
