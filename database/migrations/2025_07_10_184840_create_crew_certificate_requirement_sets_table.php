<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 1. The "set" (requirement bundle) table:
        Schema::create('crew_certificate_requirement_sets', function (Blueprint $table) {
    $table->id();
    $table->unsignedBigInteger('ship_crew_position_requirement_id');
    $table->enum('logic_type', ['ALL', 'ANY']); // ALL = must have all, ANY = any N
    $table->integer('min_required')->nullable(); // if ANY, how many
    $table->string('name')->nullable(); // 'Radio Certs', etc.
    $table->timestamps();

    // Use a short name for the foreign key constraint
    $table->foreign('ship_crew_position_requirement_id', 'ccr_set_pos_req_fk')
        ->references('id')
        ->on('ship_crew_position_requirements')
        ->onDelete('cascade');
});


    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('crew_certificate_requirement_sets');
    }
};
