<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('asset_log_metrics', function (Blueprint $table) {
            //
              // Drop old FK constraint
            $table->dropForeign(['metric_type_id']);
            // Add new FK constraint to metric_types
            $table->foreign('metric_type_id')->references('id')->on('metric_types');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('asset_log_metrics', function (Blueprint $table) {
            //
                  // Drop new FK constraint
            $table->dropForeign(['metric_type_id']);
            // Restore FK to master_entries
            $table->foreign('metric_type_id')->references('id')->on('master_entries');
        });
    }
};
