<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('crew_assignments', function (Blueprint $table) {
            // Drop designation_id if it exists (to avoid redundancy)
            if (Schema::hasColumn('crew_assignments', 'designation_id')) {
                $table->dropForeign(['designation_id']);
                $table->dropColumn('designation_id');
            }

            // Add ship_crew_position_requirement_id if not already present
            if (!Schema::hasColumn('crew_assignments', 'ship_crew_position_requirement_id')) {
                $table->unsignedBigInteger('ship_crew_position_requirement_id')
                      ->after('ship_mark_id');

                $table->foreign('ship_crew_position_requirement_id', 'crew_assignments_position_requirement_fk')
                      ->references('id')
                      ->on('ship_crew_position_requirements')
                      ->onDelete('cascade');
            }

            // Add status if missing
            if (!Schema::hasColumn('crew_assignments', 'status')) {
                $table->string('status')->default('assigned')->after('ship_crew_position_requirement_id');
            }

            // Add assigned_at if missing
            if (!Schema::hasColumn('crew_assignments', 'assigned_at')) {
                $table->timestamp('assigned_at')->nullable()->after('status');
            }

            // Add relieved_at if missing
            if (!Schema::hasColumn('crew_assignments', 'relieved_at')) {
                $table->timestamp('relieved_at')->nullable()->after('assigned_at');
            }
        });
    }

    public function down(): void
    {
        Schema::table('crew_assignments', function (Blueprint $table) {
            if (Schema::hasColumn('crew_assignments', 'relieved_at')) {
                $table->dropColumn('relieved_at');
            }

            if (Schema::hasColumn('crew_assignments', 'assigned_at')) {
                $table->dropColumn('assigned_at');
            }

            if (Schema::hasColumn('crew_assignments', 'status')) {
                $table->dropColumn('status');
            }

            if (Schema::hasColumn('crew_assignments', 'ship_crew_position_requirement_id')) {
                $table->dropForeign('crew_assignments_position_requirement_fk');
                $table->dropColumn('ship_crew_position_requirement_id');
            }

            // Optional: Restore designation_id if needed
            if (!Schema::hasColumn('crew_assignments', 'designation_id')) {
                $table->foreignId('designation_id')
                      ->nullable()
                      ->constrained()
                      ->onDelete('set null');
            }
        });
    }
};
