<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('preventive_maintenance_rules', function (Blueprint $table) {
            $table->id();
            $table->foreignId('ship_asset_id')->constrained('ship_assets');
            $table->foreignId('trigger_type_id')->constrained('master_entries'); // time, usage, condition
            $table->integer('time_interval')->nullable();
            $table->foreignId('metric_type_id')->nullable()->constrained('master_entries');
            $table->decimal('usage_threshold', 12, 4)->nullable();
            $table->string('condition')->nullable();
            $table->json('reminder_settings')->nullable();
            $table->foreignId('checklist_master_id')->nullable()->constrained('asset_checklist_masters');
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
         Schema::dropIfExists('preventive_maintenance_rules');
    }
};
