<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('ship_crew_positions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('ship_id')->constrained()->onDelete('cascade');
            $table->foreignId('designation_id')->constrained('master_entries'); // designation master
            $table->unsignedInteger('min_required')->default(0);
            $table->unsignedInteger('max_allowed')->nullable();
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->unique(['ship_id', 'designation_id']);
        });
    }
    public function down(): void
    {
        Schema::dropIfExists('ship_crew_positions');
    }
};
