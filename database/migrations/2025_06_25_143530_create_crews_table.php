<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('crews', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->unique();
            $table->string('unique_crew_id', 50)->unique()->comment('System-generated permanent ID');
            $table->string('aadhaar_number', 20)->unique()->nullable();
            $table->string('pan_number', 20)->unique()->nullable();
            $table->string('passport_number', 50)->unique()->nullable();
            $table->foreignId('current_ship_id')->nullable()->constrained('ships');
            $table->foreignId('status_id')->constrained('master_entries');
            $table->foreignId('designation_id')->nullable()->constrained();
            $table->datetime('onboarded_at')->nullable();
            $table->datetime('offboarded_at')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('crews');
    }
};
