<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('asset_movements', function (Blueprint $table) {
             $table->id();
    $table->foreignId('asset_id')->constrained('assets');
    $table->foreignId('ship_id')->constrained('ships');
    $table->foreignId('from_location_id')->nullable()->constrained('locations');
    $table->foreignId('to_location_id')->nullable()->constrained('locations');
    $table->integer('quantity')->default(1);
    $table->foreignId('movement_type_id')->constrained('master_entries');
    $table->text('notes')->nullable();
    $table->foreignId('moved_by')->constrained('users');
    $table->timestamp('moved_at')->useCurrent();
    $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('asset_movements');
    }
};
