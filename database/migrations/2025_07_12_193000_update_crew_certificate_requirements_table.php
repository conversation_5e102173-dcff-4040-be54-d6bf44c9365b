<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up()
    {
        Schema::table('crew_certificate_requirements', function (Blueprint $table) {
            if (Schema::hasColumn('crew_certificate_requirements', 'ship_crew_position_requirement_id')) {
                // Drop foreign key constraint by name
                $table->dropForeign('ccr_cert_type_fk');
                // Drop the column
                $table->dropColumn('ship_crew_position_requirement_id');
            }
        });
    }

    public function down()
    {
        Schema::table('crew_certificate_requirements', function (Blueprint $table) {
            $table->unsignedBigInteger('ship_crew_position_requirement_id')->nullable()->after('requirement_set_id');
            $table->foreign('ship_crew_position_requirement_id', 'ccr_req_set_fk')
                ->references('id')
                ->on('ship_crew_position_requirements')
                ->onDelete('cascade');
        });
    }
};
