<?php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        // Drop FK
        Schema::table('ship_assets', function (Blueprint $table) {
            $table->dropForeign(['primary_metric_type_id']);
        });

        // Drop column
        Schema::table('ship_assets', function (Blueprint $table) {
            $table->dropColumn('primary_metric_type_id');
        });

        // Add column back
        Schema::table('ship_assets', function (Blueprint $table) {
            $table->unsignedBigInteger('primary_metric_type_id')->nullable()->after('asset_id');
            $table->foreign('primary_metric_type_id')->references('id')->on('metric_types');
        });
    }

    public function down(): void
    {
        // Always add logic to reverse above if possible
        Schema::table('ship_assets', function (Blueprint $table) {
            $table->dropForeign(['primary_metric_type_id']);
            $table->dropColumn('primary_metric_type_id');
        });
    }
};
