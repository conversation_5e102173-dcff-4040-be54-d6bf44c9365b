<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('asset_log_metrics', function (Blueprint $table) {
            // Add the new FK to ship_asset_metrics
            $table->foreignId('ship_asset_metric_id')
                ->after('daily_asset_log_id')
                ->constrained('ship_asset_metrics')
                ->onDelete('cascade');

            // Drop old FK and columns if no longer needed
            $table->dropForeign(['metric_type_id']); // drop the foreign key constraint
            $table->dropColumn('metric_type_id');     // remove the column

            $table->dropColumn('unit');               // remove the unit column
        });
    }

    public function down(): void
    {
        Schema::table('asset_log_metrics', function (Blueprint $table) {
            // Remove the new FK
            $table->dropForeign(['ship_asset_metric_id']);
            $table->dropColumn('ship_asset_metric_id');

            // Re-add old columns (adjust type as before)
            $table->unsignedBigInteger('metric_type_id')->nullable();
            $table->string('unit')->nullable();

            // Re-add old foreign key (if needed)
            $table->foreign('metric_type_id')->references('id')->on('metric_types');
        });
    }
};

