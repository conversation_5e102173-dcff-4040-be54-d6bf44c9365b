<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ship_asset_checklist_item_metrics', function (Blueprint $table) {
            $table->id();

            $table->unsignedBigInteger('ship_asset_checklist_item_id');
            $table->unsignedBigInteger('metric_type_id');

            $table->boolean('is_required')->default(true);
            $table->integer('order')->nullable();
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->foreign('ship_asset_checklist_item_id', 'saci_metrics_item_fk')
                ->references('id')->on('ship_asset_checklist_items')->onDelete('cascade');

            $table->foreign('metric_type_id', 'saci_metrics_type_fk')
                ->references('id')->on('metric_types')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ship_asset_checklist_item_metrics');
    }
};
