<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('drill_schedules', function (Blueprint $table) {
            $table->id();
    $table->foreignId('ship_id')->constrained();
    $table->string('drill_type', 100);
    $table->foreignId('frequency_id')->constrained('master_entries');
    $table->date('start_date');
    $table->date('next_due_date');
    $table->boolean('is_active')->default(true);
    $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('drill_schedules');
    }
};
