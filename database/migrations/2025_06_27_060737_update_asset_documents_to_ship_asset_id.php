<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
         Schema::table('asset_documents', function (Blueprint $table) {
            $table->dropForeign(['asset_id']);
            $table->dropColumn('asset_id');
            $table->foreignId('ship_asset_id')->after('id')->constrained('ship_assets');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
         Schema::table('asset_documents', function (Blueprint $table) {
            $table->dropForeign(['ship_asset_id']);
            $table->dropColumn('ship_asset_id');
            $table->foreignId('asset_id')->after('id')->constrained('assets');
        });
    }
};
