<?php

// database/migrations/xxxx_xx_xx03_create_requirement_set_groups_table.php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up() {
        // Set groups
        Schema::create('requirement_set_groups', function (Blueprint $table) {
            $table->id();
            $table->string('name');
              $table->string('description')->nullable();
            $table->timestamps();
        });

        // Group <-> Set mapping
        Schema::create('requirement_set_group_sets', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('requirement_set_group_id');
            $table->unsignedBigInteger('requirement_set_id');
            $table->timestamps();

            $table->unique(['requirement_set_group_id', 'requirement_set_id'], 'group_set_unique');
            $table->foreign('requirement_set_group_id')
                ->references('id')->on('requirement_set_groups')->onDelete('cascade');
            $table->foreign('requirement_set_id')
                ->references('id')->on('crew_certificate_requirement_sets')->onDelete('cascade');
        });

        // Position <-> Group mapping
        Schema::create('position_requirement_set_group_assignments', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('ship_crew_position_requirement_id');
            $table->unsignedBigInteger('requirement_set_group_id');
            $table->timestamps();

            $table->unique(['ship_crew_position_requirement_id', 'requirement_set_group_id'], 'position_group_unique');


            $table->foreign('ship_crew_position_requirement_id','prsga_scpr_id')
                ->references('id')->on('ship_crew_position_requirements')->onDelete('cascade');
            $table->foreign('requirement_set_group_id','prsga_rsg_id')
                ->references('id')->on('requirement_set_groups')->onDelete('cascade');
        });
    }

    public function down() {
        Schema::dropIfExists('position_requirement_set_group_assignments');
        Schema::dropIfExists('requirement_set_group_sets');
        Schema::dropIfExists('requirement_set_groups');
    }
};
