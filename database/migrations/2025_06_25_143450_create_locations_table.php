<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('locations', function (Blueprint $table) {
              $table->id();
    $table->foreignId('ship_id')->constrained();
    $table->string('name', 100);
    $table->foreignId('parent_id')->nullable()->constrained('locations');
    $table->foreignId('type_id')->constrained('master_entries');
    $table->string('image')->nullable();
    $table->text('notes')->nullable();
    $table->text('path')->nullable();
    $table->timestamps();
    $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('locations');
    }
};
