<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('crew_histories', function (Blueprint $table) {
            $table->id();
    $table->foreignId('crew_id')->constrained();
    $table->foreignId('ship_id')->constrained();
    $table->foreignId('designation_id')->constrained();
    $table->datetime('from_date');
    $table->datetime('to_date')->nullable();
    $table->foreignId('event_type_id')->constrained('master_entries');
    $table->text('notes')->nullable();
    $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('crew_history');
    }
};
