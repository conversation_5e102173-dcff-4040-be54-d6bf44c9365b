<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('designation_feature_permissions', function (Blueprint $table) {
          $table->id();
        $table->foreignId('designation_id')->constrained();
        $table->string('feature_key', 100);
        $table->boolean('allowed')->default(true);
        $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('designation_feature_permissions');
    }
};
