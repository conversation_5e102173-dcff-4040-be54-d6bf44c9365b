<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        //
        Schema::table('crew_certificate_requirements', function (Blueprint $table) {
            // Remove if you do not need min/max and is_mandatory per certificate
            if (Schema::hasColumn('crew_certificate_requirements', 'min_count')) {
                $table->dropColumn('min_count');
            }
            if (Schema::hasColumn('crew_certificate_requirements', 'max_count')) {
                $table->dropColumn('max_count');
            }
            if (Schema::hasColumn('crew_certificate_requirements', 'is_mandatory')) {
                $table->dropColumn('is_mandatory');
            }

            // Add requirement_set_id if not exists (nullable for future use)
            if (!Schema::hasColumn('crew_certificate_requirements', 'requirement_set_id')) {
                Schema::table('crew_certificate_requirements', function (Blueprint $table) {
                    $table->unsignedBigInteger('requirement_set_id')->nullable()->after('certificate_type_id');
                    $table->foreign('requirement_set_id')->references('id')->on('crew_certificate_requirement_sets')->onDelete('cascade');
                });
            } else {
                // If exists but not nullable, make nullable
                Schema::table('crew_certificate_requirements', function (Blueprint $table) {
                    $table->unsignedBigInteger('requirement_set_id')->nullable()->change();
                });
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
        // Add columns back if rollback
        Schema::table('crew_certificate_requirements', function (Blueprint $table) {
            if (!Schema::hasColumn('crew_certificate_requirements', 'min_count')) {
                $table->integer('min_count')->nullable();
            }
            if (!Schema::hasColumn('crew_certificate_requirements', 'max_count')) {
                $table->integer('max_count')->nullable();
            }
            if (!Schema::hasColumn('crew_certificate_requirements', 'is_mandatory')) {
                $table->boolean('is_mandatory')->default(true);
            }

            // Remove the foreign key and column for rollback
            if (Schema::hasColumn('crew_certificate_requirements', 'requirement_set_id')) {
                Schema::table('crew_certificate_requirements', function (Blueprint $table) {
                    $table->dropForeign(['requirement_set_id']);
                    $table->dropColumn('requirement_set_id');
                });
            }
        });
    }
};
