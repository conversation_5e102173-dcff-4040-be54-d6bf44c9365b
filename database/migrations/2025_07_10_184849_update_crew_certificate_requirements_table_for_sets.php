<?php
// database/migrations/xxxx_xx_xx_update_crew_certificate_requirements.php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        // Drop foreign key and column first if exists
        if (Schema::hasColumn('crew_certificate_requirements', 'certificate_master_id')) {
            // First drop FK, then drop column
            Schema::table('crew_certificate_requirements', function (Blueprint $table) {
                // If you gave the FK a short name, use it here, else default: table_column_foreign

                 $table->dropForeign('ccr_cert_master_id_fk');
                $table->dropForeign('ccr_ship_crew_pos_req_id_fk');
                $table->dropColumn('certificate_master_id');
            });
        }

        // Add new FKs
        Schema::table('crew_certificate_requirements', function (Blueprint $table) {
            if (!Schema::hasColumn('crew_certificate_requirements', 'certificate_type_id')) {
                $table->unsignedBigInteger('certificate_type_id')->after('ship_crew_position_requirement_id');
            }
            if (!Schema::hasColumn('crew_certificate_requirements', 'requirement_set_id')) {
                $table->unsignedBigInteger('requirement_set_id')->nullable()->after('certificate_type_id');
            }
        });

        // Now add the foreign keys (after columns are created)
        Schema::table('crew_certificate_requirements', function (Blueprint $table) {
            if (Schema::hasColumn('crew_certificate_requirements', 'certificate_type_id')) {
                $table->foreign('certificate_type_id', 'ccr_cert_type_fk')
                    ->references('id')
                    ->on('crew_certificate_types')
                    ->onDelete('cascade');
            }
            if (Schema::hasColumn('crew_certificate_requirements', 'requirement_set_id')) {
                $table->foreign('requirement_set_id', 'ccr_req_set_fk')
                    ->references('id')
                    ->on('crew_certificate_requirement_sets')
                    ->onDelete('cascade');
            }
        });
    }

    public function down()
    {
        // Drop foreign keys first
        Schema::table('crew_certificate_requirements', function (Blueprint $table) {
            if (Schema::hasColumn('crew_certificate_requirements', 'requirement_set_id')) {
                $table->dropForeign('ccr_req_set_fk');
            }
            if (Schema::hasColumn('crew_certificate_requirements', 'certificate_type_id')) {
                $table->dropForeign('ccr_cert_type_fk');
            }
        });

        // Then drop columns
        Schema::table('crew_certificate_requirements', function (Blueprint $table) {
            if (Schema::hasColumn('crew_certificate_requirements', 'requirement_set_id')) {
                $table->dropColumn('requirement_set_id');
            }
            if (Schema::hasColumn('crew_certificate_requirements', 'certificate_type_id')) {
                $table->dropColumn('certificate_type_id');
            }
        });

        // Optionally restore old column if needed
    }
};
