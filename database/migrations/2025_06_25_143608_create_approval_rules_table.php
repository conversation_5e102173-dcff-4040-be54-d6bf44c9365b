<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('approval_rules', function (Blueprint $table) {
             $table->id();
    $table->string('module', 100);
    $table->unsignedInteger('sequence_order');
    $table->foreignId('role_id')->nullable()->constrained('roles');
    $table->foreignId('designation_id')->nullable()->constrained();
    $table->foreignId('user_id')->nullable()->constrained('users');
    $table->foreignId('ship_id')->nullable()->constrained('ships');
    $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('approval_rules');
    }
};
