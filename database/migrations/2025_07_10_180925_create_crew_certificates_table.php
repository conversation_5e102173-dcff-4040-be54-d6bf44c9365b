<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
      Schema::create('crew_certificates', function (Blueprint $table) {
            $table->id();
            $table->foreignId('crew_id')->constrained();
            $table->foreignId('crew_certificate_type_id')->constrained();
            $table->string('certificate_number')->nullable();
            $table->date('issue_date')->nullable();
            $table->date('expiry_date')->nullable();
            $table->date('renewal_date')->nullable();
            $table->string('issuing_authority')->nullable();
            $table->string('file')->nullable(); // path to file
            $table->text('notes')->nullable();
            $table->timestamps();
            // For history, you can make a separate table, or just keep each renewal as a new row.
    });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('crew_certificates');
    }
};
