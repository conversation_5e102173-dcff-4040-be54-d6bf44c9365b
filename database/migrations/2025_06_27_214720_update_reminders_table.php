<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */

      public function up(): void
    {
       Schema::table('reminders', function (Blueprint $table) {
            // $table->morphs('remindable');
            // $table->string('type');
            // $table->text('message');
            // $table->dateTime('remind_at');
            // $table->boolean('is_sent')->default(false);
            // $table->dateTime('sent_at')->nullable();
            // $table->foreignId('user_id')->constrained();
            // $table->text('notes')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */




    public function down(): void
    {
        //
    }
};
