<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ship_asset_specs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('ship_asset_id')->constrained();
            $table->foreignId('spec_key_id')->constrained('asset_spec_keys');
            $table->string('value');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ship_asset_specs');
    }
};
