<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('ship_assets', function (Blueprint $table) {
        $table->foreignId('primary_metric_type_id')
            ->nullable()
            ->constrained('master_entries')
            ->after('asset_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('ship_assets', function (Blueprint $table) {
            //
        });
    }
};
