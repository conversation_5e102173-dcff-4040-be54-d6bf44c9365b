<?php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('asset_transactions', function (Blueprint $table) {
            // Add metric_type_id (nullable at first)
            $table->foreignId('metric_type_id')->nullable()->after('transaction_type_id')->constrained('metric_types');

            // Add old_level/new_level if missing
            if (!Schema::hasColumn('asset_transactions', 'old_level')) {
                $table->decimal('old_level', 12, 4)->nullable()->after('quantity');
            }
            if (!Schema::hasColumn('asset_transactions', 'new_level')) {
                $table->decimal('new_level', 12, 4)->nullable()->after('old_level');
            }
        });
    }

    public function down(): void
    {
        Schema::table('asset_transactions', function (Blueprint $table) {
            $table->dropForeign(['metric_type_id']);
            $table->dropColumn('metric_type_id');
            $table->dropColumn('old_level');
            $table->dropColumn('new_level');
        });
    }
};
