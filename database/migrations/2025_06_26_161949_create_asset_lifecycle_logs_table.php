<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('asset_lifecycle_logs', function (Blueprint $table) {
           $table->id();
            $table->foreignId('ship_asset_id')->constrained()->onDelete('cascade');
            $table->foreignId('event_type_id')->constrained('master_entries'); // replaces enum
            $table->text('remarks')->nullable();
            $table->foreignId('recorded_by')->constrained('users');
            $table->timestamp('event_time')->useCurrent();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('asset_lifecycle_logs');
    }
};
