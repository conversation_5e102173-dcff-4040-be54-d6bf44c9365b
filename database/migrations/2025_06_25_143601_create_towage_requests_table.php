<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('towage_requests', function (Blueprint $table) {
           $table->id();
    $table->foreignId('customer_id')->constrained('users');
    $table->foreignId('ship_id')->constrained('ships');
    $table->text('purpose')->nullable();
    $table->datetime('eta')->nullable();
    $table->string('anchor_point', 100)->nullable();
    $table->text('location_details')->nullable();
    $table->foreignId('status_id')->constrained('master_entries');
    $table->foreignId('assigned_tug_id')->nullable()->constrained('ships');
    $table->foreignId('fleet_supervisor_id')->nullable()->constrained('users');
    $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('towage_requests');
    }
};
