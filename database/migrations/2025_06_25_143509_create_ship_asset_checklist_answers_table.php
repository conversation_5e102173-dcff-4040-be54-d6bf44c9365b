<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ship_asset_checklist_answers', function (Blueprint $table) {
              $table->id();
    $table->foreignId('run_id')->constrained('ship_asset_checklist_runs')->onDelete('cascade');
    $table->foreignId('checklist_item_id')->constrained('ship_asset_checklist_items')->onDelete('cascade');
    $table->string('value')->nullable();
    $table->string('image_before')->nullable();
    $table->string('image_after')->nullable();
    $table->text('notes')->nullable();
    $table->foreignId('tag_id')->nullable()->constrained('tags');
    $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ship_asset_checklist_answers');
    }
};
