<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('crew_checklist_masters', function (Blueprint $table) {
            $table->id();
            $table->string('name', 100);
            $table->foreignId('event_type_id')->constrained('master_entries');
            $table->json('designation_ids')->nullable()->comment('Null = common for all');
            $table->string('question');
            $table->foreignId('input_type_id')->constrained('master_entries');
            $table->json('options')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('crew_checklist_masters');
    }
};
