<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('ship_asset_metrics', function (Blueprint $table) {
            //
            // In migration file:
            $table->boolean('is_transactional')->default(false);

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('ship_asset_metrics', function (Blueprint $table) {
            //
        });
    }
};
