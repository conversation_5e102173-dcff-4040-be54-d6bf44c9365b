<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
         Schema::table('ship_asset_checklist_runs', function (Blueprint $table) {
            $table->foreignId('pm_rule_id')
                ->nullable()
                ->after('survey_id')
                ->constrained('preventive_maintenance_rules')
                ->nullOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
       Schema::table('ship_asset_checklist_runs', function (Blueprint $table) {
            $table->dropForeign(['pm_rule_id']);
            $table->dropColumn('pm_rule_id');
        });
    }
};
