<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('assets', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->foreignId('type_id')->constrained('master_entries');
            $table->foreignId('category_id')->nullable()->constrained('asset_categories');
            $table->boolean('is_compliance')->default(false);
            $table->boolean('is_fire_safety')->default(false);
            $table->boolean('survey_required')->default(false);
            $table->string('unit', 20)->nullable();
            $table->foreignId('consumable_type_id')->constrained('master_entries');
            $table->json('default_spec')->nullable();
            $table->string('image')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('assets');
    }
};
