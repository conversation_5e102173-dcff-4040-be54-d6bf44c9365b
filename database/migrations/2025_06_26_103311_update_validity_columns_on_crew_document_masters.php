<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('crew_document_masters', function (Blueprint $table) {
            $table->dropColumn('validity_period');
            $table->unsignedInteger('validity_value')->nullable();
            $table->foreignId('validity_unit_id')->nullable()->constrained('master_entries');
        });


    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
