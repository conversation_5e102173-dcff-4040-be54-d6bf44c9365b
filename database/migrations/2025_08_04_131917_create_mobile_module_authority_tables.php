<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        // 1. mobile_modules
        Schema::create('mobile_modules', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('key')->unique(); // e.g., 'survey', 'maintenance'
            $table->string('icon')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });

        // 2. authority_levels
        Schema::create('authority_levels', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique(); // e.g., 'Master', 'Owner', 'Surveyor'
            $table->timestamps();
        });

        // 3. mobile_module_permissions
        Schema::create('mobile_module_permissions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('mobile_module_id')->constrained()->cascadeOnDelete();
            $table->foreignId('permission_id')->constrained('permissions')->cascadeOnDelete();
            $table->timestamps();
        });

        // 4. authority_level_permission (core authority access)
        Schema::create('authority_level_permission', function (Blueprint $table) {
            $table->id();
            $table->foreignId('authority_level_id')->constrained()->cascadeOnDelete();
            $table->foreignId('permission_id')->constrained('permissions')->cascadeOnDelete();
            $table->timestamps();
        });

        // 5. user_authority_levels (optional - can have multiple)
        Schema::create('user_authority_levels', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->cascadeOnDelete();
            $table->foreignId('authority_level_id')->constrained()->cascadeOnDelete();
            $table->timestamps();
        });

        // 6. designation_authority_levels (optional default per designation)
        Schema::create('designation_authority_levels', function (Blueprint $table) {
            $table->id();
            $table->foreignId('designation_id')->constrained()->cascadeOnDelete();
            $table->foreignId('authority_level_id')->constrained()->cascadeOnDelete();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('designation_authority_levels');
        Schema::dropIfExists('user_authority_levels');
        Schema::dropIfExists('authority_level_permission');
        Schema::dropIfExists('mobile_module_permissions');
        Schema::dropIfExists('authority_levels');
        Schema::dropIfExists('mobile_modules');
    }
};
