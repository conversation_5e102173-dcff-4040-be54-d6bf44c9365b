<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ship_asset_checklist_runs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('ship_asset_id')->constrained('ship_assets');
            $table->foreignId('survey_id')->nullable()->constrained('surveys')->nullOnDelete();
            $table->foreignId('performed_by')->constrained('users');
            $table->foreignId('type_id')->constrained('master_entries');
            $table->text('notes')->nullable();
            $table->timestamp('submitted_at')->useCurrent();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ship_asset_checklist_runs');
    }
};
