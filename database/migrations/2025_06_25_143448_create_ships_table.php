<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ships', function (Blueprint $table) {
           $table->id();
    $table->string('imo_number', 15)->unique();
    $table->string('official_number', 50)->nullable();
    $table->string('name', 150);
    $table->string('call_sign', 50)->nullable();
    $table->foreignId('ship_type_id')->constrained();
    $table->foreignId('ship_class_id')->constrained();
    $table->foreignId('company_id')->constrained();

    // Dimensions
    $table->decimal('length_overall', 8, 2)->nullable();
    $table->decimal('breadth', 8, 2)->nullable();
    $table->decimal('depth', 8, 2)->nullable();
    $table->decimal('draft', 8, 2)->nullable();
    $table->decimal('gross_tonnage', 10, 2)->nullable();
    $table->decimal('net_tonnage', 10, 2)->nullable();
    $table->decimal('deadweight', 10, 2)->nullable();

    // Construction
    $table->year('year_built')->nullable();
    $table->string('builder', 150)->nullable();
    $table->string('hull_number', 50)->nullable();

    // Status
    $table->foreignId('status_id')->constrained('master_entries');
    $table->date('commission_date')->nullable();
    $table->date('decommission_date')->nullable();

    // Flags
    $table->foreignId('flag_id')->constrained('countries');
    $table->string('port_of_registry', 100)->nullable();

    $table->text('notes')->nullable();
    $table->timestamps();
    $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ships');
    }
};
