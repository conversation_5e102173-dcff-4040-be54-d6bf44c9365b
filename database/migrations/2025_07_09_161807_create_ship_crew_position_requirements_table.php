<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
       // create_ship_crew_position_requirements_table.php
        Schema::create('ship_crew_position_requirements', function (Blueprint $table) {
            $table->id();
            $table->foreignId('ship_id')->constrained();
            $table->foreignId('ship_mark_id')->constrained('ship_marks');
            $table->foreignId('designation_id')->constrained('designations');
            $table->integer('min_required')->default(0);   // Minimum number of crew for this position+mark
            $table->integer('max_allowed')->nullable();    // Maximum allowed (optional)
            $table->boolean('is_required')->default(false); // If required at all
            $table->timestamps();
            $table->unique(['ship_id', 'ship_mark_id', 'designation_id'], 'uniq_ship_mark_designation');
        });

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ship_crew_position_requirements');
    }
};
