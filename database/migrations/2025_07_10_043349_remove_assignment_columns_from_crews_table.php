<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class RemoveAssignmentColumnsFromCrewsTable extends Migration
{
    public function up()
    {
        Schema::table('crews', function (Blueprint $table) {
            $table->dropForeign(['current_ship_id']);
            $table->dropForeign(['designation_id']);
            $table->dropColumn(['current_ship_id', 'designation_id', 'onboarded_at', 'offboarded_at']);
        });
    }

    public function down()
    {
        Schema::table('crews', function (Blueprint $table) {
            $table->foreignId('current_ship_id')->nullable()->constrained('ships');
            $table->foreignId('designation_id')->nullable()->constrained('designations');
            $table->dateTime('onboarded_at')->nullable();
            $table->dateTime('offboarded_at')->nullable();
        });
    }
}
