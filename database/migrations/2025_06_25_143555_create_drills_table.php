<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('drills', function (Blueprint $table) {
            $table->id();
    $table->foreignId('schedule_id')->nullable()->constrained('drill_schedules');
    $table->foreignId('ship_id')->constrained();
    $table->string('drill_type', 100);
    $table->datetime('start_time');
    $table->datetime('end_time');
    $table->json('images')->nullable();
    $table->text('notes')->nullable();
    $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('drills');
    }
};
