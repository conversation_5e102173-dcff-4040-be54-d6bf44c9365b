<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ship_classes', function (Blueprint $table) {
             $table->id();
    $table->string('code', 10)->unique();
    $table->string('name', 100);
    $table->text('description')->nullable();
    $table->foreignId('classification_society_id')->nullable()->constrained();
    $table->boolean('is_active')->default(true);
    $table->timestamps();
    $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ship_classes');
    }
};
