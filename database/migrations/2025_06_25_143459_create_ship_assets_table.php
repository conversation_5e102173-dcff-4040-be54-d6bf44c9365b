<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ship_assets', function (Blueprint $table) {
            $table->id();
            $table->foreignId('ship_id')->constrained();
            $table->foreignId('asset_id')->constrained('assets');
            $table->foreignId('location_id')->nullable()->constrained();
            $table->string('override_image')->nullable();
            $table->json('override_spec')->nullable();
            $table->decimal('current_level', 12, 4)->nullable();
            $table->timestamp('last_measured_at')->nullable();
            $table->json('cert_info')->nullable();
            $table->boolean('is_compliance')->default(false);
            $table->boolean('is_fire_safety')->default(false);
            $table->boolean('survey_required')->default(true);
            $table->integer('quantity')->default(0);
            $table->text('notes')->nullable();
            $table->foreignId('created_by')->constrained('users');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ship_assets');
    }
};
