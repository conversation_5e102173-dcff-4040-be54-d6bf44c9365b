<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('crew_certificate_requirements', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('ship_crew_position_requirement_id');
            $table->unsignedBigInteger('certificate_master_id');
            $table->boolean('is_primary')->default(false);
            $table->boolean('is_mandatory')->default(true);
            $table->integer('min_count')->nullable();
            $table->integer('max_count')->nullable();
            $table->string('remarks')->nullable();
            $table->timestamps();

            // Add foreign key with short name
            $table->foreign('ship_crew_position_requirement_id', 'ccr_ship_crew_pos_req_id_fk')
                ->references('id')
                ->on('ship_crew_position_requirements')
                ->onDelete('cascade');

            $table->foreign('certificate_master_id', 'ccr_cert_master_id_fk')
                ->references('id')
                ->on('certificate_masters')
                ->onDelete('cascade');
        });

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('crew_certificate_requirements');
    }
};
