<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('asset_checklist_masters', function (Blueprint $table) {
             $table->id();
            $table->foreignId('category_id')->nullable()->constrained('asset_categories');
            $table->string('question');
            $table->foreignId('input_type_id')->constrained('master_entries');
            $table->json('options')->nullable();
            $table->boolean('image_required')->default(false);
            $table->boolean('is_for_survey')->default(false);
            $table->foreignId('default_tag_id')->nullable()->constrained('tags');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('asset_checklist_masters');
    }
};
