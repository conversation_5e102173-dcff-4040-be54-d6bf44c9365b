<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('preventive_maintenances', function (Blueprint $table) {
    $table->id();
    $table->foreignId('ship_asset_id')->constrained();
    $table->foreignId('checklist_item_id')->constrained('asset_checklist_masters');
    $table->string('trigger_type')->comment('time, usage, or condition');
    $table->integer('time_interval')->nullable()->comment('In days');
    $table->string('usage_metric')->nullable()->comment('hours, liters, etc.');
    $table->decimal('usage_threshold', 12, 4)->nullable();
    $table->string('condition')->nullable()->comment('e.g., < 2000, > 400');
    $table->json('reminder_settings')->comment('JSON: {"30": true, "15": false, "7": true}');
    $table->decimal('current_value', 12, 4)->default(0);
    $table->dateTime('last_performed_at')->nullable();
    $table->dateTime('next_due_date')->nullable();
    $table->decimal('next_due_value', 12, 4)->nullable();
    $table->boolean('is_active')->default(true);
    $table->timestamps();
});
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('preventive_maintenances');
    }
};
