<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('crew_assignments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('crew_id')->constrained();
            $table->foreignId('ship_id')->constrained();
            $table->foreignId('designation_id')->constrained('designations');
            $table->foreignId('ship_mark_id')->constrained('ship_marks');
            // $table->foreignId('primary_certificate_id')->nullable()->constrained('crew_certificates');
            $table->enum('status', ['assigned', 'unassigned', 'relieved'])->default('assigned');
            $table->timestamp('assigned_at')->nullable();
            $table->timestamp('relieved_at')->nullable();
            $table->timestamps();
            $table->index(['ship_id', 'ship_mark_id', 'designation_id']);
        });


    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('crew_assignments');
    }
};
