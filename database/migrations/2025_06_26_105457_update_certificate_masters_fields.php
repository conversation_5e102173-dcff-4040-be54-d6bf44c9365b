<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
       Schema::table('certificate_masters', function (Blueprint $table) {
            $table->json('ship_type_ids')->nullable()->change(); // if existing
            $table->json('ship_class_ids')->nullable()->change();
            $table->unsignedInteger('validity_value')->nullable();
            $table->foreignId('validity_unit_id')->nullable()->constrained('master_entries');
        });

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
