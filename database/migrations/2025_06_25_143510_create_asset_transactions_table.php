<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('asset_transactions', function (Blueprint $table) {
              $table->id();
                $table->foreignId('ship_asset_id')->constrained('ship_assets');
                $table->foreignId('transaction_type_id')->constrained('master_entries');
                $table->decimal('quantity', 12, 4);
                $table->decimal('new_level', 12, 4)->nullable();
                $table->foreignId('recorded_by')->constrained('users');
                $table->text('notes')->nullable();
                $table->timestamp('transaction_time')->useCurrent();
                $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('asset_transactions');
    }
};
