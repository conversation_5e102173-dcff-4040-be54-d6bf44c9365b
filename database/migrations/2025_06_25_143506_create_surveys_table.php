<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('surveys', function (Blueprint $table) {
            $table->id();
            $table->foreignId('type_id')->constrained('master_entries');
            $table->foreignId('ship_id')->constrained();
            $table->foreignId('schedule_id')->nullable()->constrained('survey_schedules')->nullOnDelete();
            $table->foreignId('assigned_to')->nullable()->constrained('users');
            $table->foreignId('status_id')->constrained('master_entries');
            $table->datetime('started_at')->nullable();
            $table->datetime('due_date')->nullable();
            $table->datetime('completed_at')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('surveys');
    }
};
