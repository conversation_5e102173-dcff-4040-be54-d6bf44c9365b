<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('crew_checklist_masters', function (Blueprint $table) {
            //
             $table->boolean('allow_multiple')->default(false)->after('input_type_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('crew_checklist_masters', function (Blueprint $table) {
            //
            $table->dropColumn('allow_multiple');
        });
    }
};
