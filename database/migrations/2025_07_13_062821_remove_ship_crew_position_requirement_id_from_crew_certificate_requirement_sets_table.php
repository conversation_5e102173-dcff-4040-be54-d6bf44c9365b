<?php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('crew_certificate_requirement_sets', function (Blueprint $table) {
            if (Schema::hasColumn('crew_certificate_requirement_sets', 'ship_crew_position_requirement_id')) {
                // Drop by constraint name, not array
                $table->dropForeign('ccr_set_pos_req_fk');
                $table->dropColumn('ship_crew_position_requirement_id');
            }
        });
    }

    public function down()
    {
        Schema::table('crew_certificate_requirement_sets', function (Blueprint $table) {
            $table->unsignedBigInteger('ship_crew_position_requirement_id')->nullable();
            $table->foreign('ship_crew_position_requirement_id', 'ccr_set_pos_req_fk')
                ->references('id')
                ->on('ship_crew_position_requirements')
                ->onDelete('cascade');
        });
    }
};
