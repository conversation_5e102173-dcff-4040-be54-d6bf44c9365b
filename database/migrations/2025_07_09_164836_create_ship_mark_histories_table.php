<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ship_mark_histories', function (Blueprint $table) {
            $table->id();
            $table->foreignId('ship_id')->constrained();
            $table->foreignId('ship_mark_id')->constrained('ship_marks');
            $table->timestamp('changed_at');           // When the mark was applied
            $table->foreignId('changed_by')->nullable()->constrained('users'); // Who changed (optional)
            $table->string('notes')->nullable();
            $table->timestamps();
            // Ensure unique mark history per ship and mark
            $table->unique(['ship_id', 'ship_mark_id', 'changed_at'], 'uniq_ship_mark_history');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ship_mark_histories');
    }
};
