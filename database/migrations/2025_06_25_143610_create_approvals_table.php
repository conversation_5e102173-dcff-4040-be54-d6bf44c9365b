<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('approvals', function (Blueprint $table) {
            $table->id();
    $table->string('module', 100);
    $table->unsignedBigInteger('record_id');
    $table->unsignedInteger('sequence_order');
    $table->foreignId('approval_rule_id')->constrained('approval_rules');
    $table->foreignId('user_id')->nullable()->constrained('users');
    $table->foreignId('status_id')->constrained('master_entries');
    $table->text('remarks')->nullable();
    $table->timestamp('approved_at')->nullable();
    $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('approvals');
    }
};
