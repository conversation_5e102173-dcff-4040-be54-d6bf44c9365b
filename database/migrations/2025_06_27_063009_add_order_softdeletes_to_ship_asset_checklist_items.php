<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('ship_asset_checklist_items', function (Blueprint $table) {
            $table->integer('order')->default(0)->after('is_for_survey');
            // Already has softDeletes per your schema
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
         Schema::table('ship_asset_checklist_items', function (Blueprint $table) {
            $table->dropColumn('order');
        });
    }
};
