<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('crew_checklists', function (Blueprint $table) {
           $table->id();
    $table->foreignId('crew_history_id')->constrained();
    $table->foreignId('checklist_master_id')->constrained('crew_checklist_masters');
    $table->string('value')->nullable();
    $table->string('image')->nullable();
    $table->foreignId('completed_by')->constrained('users');
    $table->timestamp('completed_at')->nullable();
    $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('crew_checklists');
    }
};
