<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('ship_assets', function (Blueprint $table) {
            //
             $table->decimal('min_level', 12, 4)->nullable()->after('current_level');
             $table->decimal('max_level', 12, 4)->nullable()->after('min_level');
             $table->foreignId('status_id')->nullable()->constrained('master_entries')->after('quantity');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('ship_assets', function (Blueprint $table) {
            //
        });
    }
};
