<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('asset_log_metrics', function (Blueprint $table) {
            $table->id();
            $table->foreignId('daily_asset_log_id')->constrained()->onDelete('cascade');
            $table->foreignId('metric_type_id')->constrained('master_entries');
            $table->decimal('value', 14, 4)->nullable();
            $table->string('unit')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('asset_log_metrics');
    }
};
