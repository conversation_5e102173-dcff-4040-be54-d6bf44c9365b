<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        //
        Schema::table('surveys', function (Blueprint $table) {
            $table->foreignId('survey_template_id')->nullable()->after('id')->constrained('survey_templates');
            $table->foreignId('created_by')->nullable()->after('assigned_to')->constrained('users');
            $table->text('notes')->nullable()->after('completed_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
         Schema::table('surveys', function (Blueprint $table) {
            $table->dropForeign(['survey_template_id']);
            $table->dropForeign(['created_by']);
            $table->dropColumn(['survey_template_id', 'created_by', 'notes']);
        });
    }
};
