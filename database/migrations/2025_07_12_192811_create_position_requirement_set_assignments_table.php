<?php
// database/migrations/xxxx_xx_xx02_create_position_requirement_set_assignments_table.php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up() {
        Schema::create('position_requirement_set_assignments', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('ship_crew_position_requirement_id');
            $table->unsignedBigInteger('requirement_set_id');
            $table->timestamps();

            $table->unique(['ship_crew_position_requirement_id', 'requirement_set_id'], 'position_req_set_unique');
            $table->foreign('ship_crew_position_requirement_id', 'prsa_pos_req_fk')
                ->references('id')->on('ship_crew_position_requirements')->onDelete('cascade');
            $table->foreign('requirement_set_id', 'prsa_req_set_fk')
                ->references('id')->on('crew_certificate_requirement_sets')->onDelete('cascade');
        });
    }

    public function down() {
        Schema::dropIfExists('position_requirement_set_assignments');
    }
};
