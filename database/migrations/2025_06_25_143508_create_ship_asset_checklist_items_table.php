<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ship_asset_checklist_items', function (Blueprint $table) {
             $table->id();
                $table->foreignId('ship_asset_id')->constrained('ship_assets');
                $table->foreignId('checklist_master_id')->constrained('asset_checklist_masters');
                $table->boolean('is_for_survey')->default(false);
                $table->timestamps();
                $table->softDeletes();

              });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ship_asset_checklist_items');
    }
};
