<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
         Schema::create('preventive_maintenance_events', function (Blueprint $table) {
            $table->id();
            $table->foreignId('rule_id')->constrained('preventive_maintenance_rules');
            $table->timestamp('performed_at');
            $table->foreignId('performed_by')->nullable()->constrained('users');
            $table->text('notes')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
       Schema::dropIfExists('preventive_maintenance_events');
    }
};
