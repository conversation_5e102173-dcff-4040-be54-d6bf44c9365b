<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class AssetSpecKeysSeeder extends Seeder
{
    public function run()
    {
        $specKeys = [
            ['key' => 'weight', 'unit' => 'kg', 'description' => 'Total weight of the asset'],
            ['key' => 'dimensions', 'unit' => 'm', 'description' => 'LxWxH dimensions'],
            ['key' => 'material', 'unit' => null, 'description' => 'Construction material'],
            ['key' => 'manufacturer', 'unit' => null, 'description' => 'Manufacturer name'],
            ['key' => 'model', 'unit' => null, 'description' => 'Model number'],
            ['key' => 'serial_number', 'unit' => null, 'description' => 'Unique serial identifier'],
            ['key' => 'pressure_rating', 'unit' => 'bar', 'description' => 'Maximum pressure capacity'],
            ['key' => 'voltage', 'unit' => 'V', 'description' => 'Electrical requirement'],
            ['key' => 'flow_rate', 'unit' => 'm³/h', 'description' => 'Fluid flow capacity'],
            ['key' => 'temperature_range', 'unit' => '°C', 'description' => 'Operating temperature limits'],
        ];

        DB::table('asset_spec_keys')->insert($specKeys);
    }
}
