<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class MasterEntrySeeder_UpdateAssetTransactionTypes extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        //
        $assetTransactionTypeId = DB::table('master_types')->where('name', 'asset_transaction_type')->value('id');

        DB::table('master_entries')->insert([
            ['master_type_id' => $assetTransactionTypeId, 'code' => 'refill', 'name' => 'Refill'],
            ['master_type_id' => $assetTransactionTypeId, 'code' => 'running_hours', 'name' => 'Running Hours'],
        ]);

        $assetStatusTypeId = DB::table('master_types')->insertGetId([
            'name' => 'asset_status',
            'created_at' => now(),
            'updated_at' => now()
        ]);

        DB::table('master_entries')->insert([
            ['master_type_id' => $assetStatusTypeId, 'code' => 'active', 'name' => 'Active'],
            ['master_type_id' => $assetStatusTypeId, 'code' => 'damaged', 'name' => 'Damaged'],
            ['master_type_id' => $assetStatusTypeId, 'code' => 'replaced', 'name' => 'Replaced'],
            ['master_type_id' => $assetStatusTypeId, 'code' => 'removed', 'name' => 'Removed'],
            ['master_type_id' => $assetStatusTypeId, 'code' => 'discontinued', 'name' => 'Discontinued'],
        ]);


        $metricTypeId = DB::table('master_types')->insertGetId([
            'name' => 'asset_metric_type',
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        DB::table('master_entries')->insert([
            ['master_type_id' => $metricTypeId, 'code' => 'fuel_level', 'name' => 'Fuel Level'],
            ['master_type_id' => $metricTypeId, 'code' => 'engine_hours', 'name' => 'Engine Hours'],
            ['master_type_id' => $metricTypeId, 'code' => 'coolant_level', 'name' => 'Coolant Level'],
            ['master_type_id' => $metricTypeId, 'code' => 'lubricant_used', 'name' => 'Lubricant Used'],
            ['master_type_id' => $metricTypeId, 'code' => 'temperature', 'name' => 'Temperature'],
        ]);


    }
}
