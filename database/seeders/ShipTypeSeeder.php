<?php

namespace Database\Seeders;

use App\Models\ShipType;
use Illuminate\Database\Seeder;

class ShipTypeSeeder extends Seeder
{
    public function run()
    {
        $types = [
            ['code' => 'TANK', 'name' => 'Tanker', 'imo_category' => 'A'],
            ['code' => 'CAR', 'name' => 'Cargo Ship', 'imo_category' => 'B'],
            ['code' => 'CONT', 'name' => 'Container Ship', 'imo_category' => 'C'],
        ];

        foreach ($types as $type) {
            ShipType::create($type);
        }
    }
}
