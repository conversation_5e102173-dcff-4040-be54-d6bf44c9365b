<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\MasterEntry;
use App\Models\MasterType;
class MasterEntrySeeder_Type extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        //
          $alertTypeTypeId = MasterType::firstOrCreate(['name' => 'alert_type']);

        // Add common location purposes
        $purposes = [
             ['master_type_id' => $alertTypeTypeId->id, 'code' => 'info', 'name' => 'Info'],
            ['master_type_id' => $alertTypeTypeId->id, 'code' => 'warning', 'name' => 'Warning'],
            ['master_type_id' => $alertTypeTypeId->id, 'code' => 'critical', 'name' => 'Critical'],
        ];
        foreach ($purposes as $entry) {
            MasterEntry::firstOrCreate([
                'master_type_id' => $alertTypeTypeId->id,
                'code' => $entry['code'],
            ], [
                'name' => $entry['name'],
                'is_default' => $entry['is_default'] ?? false,
            ]);
        }
    }
}
