<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class AssetCategoriesSeeder extends Seeder
{
    public function run()
    {
        $categories = [
            // Navigation & Communication
            [
                'name' => 'Navigation Equipment',
                'is_compliance' => true,
                'is_fire_safety' => false,
                'survey_required' => true
            ],
            [
                'name' => 'Communication Systems',
                'is_compliance' => true,
                'is_fire_safety' => false,
                'survey_required' => true
            ],

            // Safety Equipment
            [
                'name' => 'Life Saving Appliances',
                'is_compliance' => true,
                'is_fire_safety' => false,
                'survey_required' => true
            ],
            [
                'name' => 'Fire Fighting Equipment',
                'is_compliance' => true,
                'is_fire_safety' => true,
                'survey_required' => true
            ],
            [
                'name' => 'Personal Protective Equipment',
                'is_compliance' => true,
                'is_fire_safety' => true,
                'survey_required' => true
            ],

            // Engine & Mechanical
            [
                'name' => 'Engine Components',
                'is_compliance' => false,
                'is_fire_safety' => false,
                'survey_required' => true
            ],
            [
                'name' => 'Pump Systems',
                'is_compliance' => false,
                'is_fire_safety' => false,
                'survey_required' => true
            ],
            [
                'name' => 'Deck Machinery',
                'is_compliance' => false,
                'is_fire_safety' => false,
                'survey_required' => true
            ],

            // Electrical
            [
                'name' => 'Electrical Systems',
                'is_compliance' => true,
                'is_fire_safety' => true,
                'survey_required' => true
            ],
            [
                'name' => 'Lighting Systems',
                'is_compliance' => true,
                'is_fire_safety' => true,
                'survey_required' => true
            ],

            // Environmental & Compliance
            [
                'name' => 'Pollution Control Equipment',
                'is_compliance' => true,
                'is_fire_safety' => false,
                'survey_required' => true
            ],
            [
                'name' => 'MARPOL Compliance Equipment',
                'is_compliance' => true,
                'is_fire_safety' => false,
                'survey_required' => true
            ],

            // General
            [
                'name' => 'Medical Equipment',
                'is_compliance' => true,
                'is_fire_safety' => false,
                'survey_required' => true
            ],
            [
                'name' => 'Galley Equipment',
                'is_compliance' => false,
                'is_fire_safety' => true,
                'survey_required' => false
            ],
            [
                'name' => 'General Spares',
                'is_compliance' => false,
                'is_fire_safety' => false,
                'survey_required' => false
            ]
        ];

        DB::table('asset_categories')->insert($categories);
    }
}
