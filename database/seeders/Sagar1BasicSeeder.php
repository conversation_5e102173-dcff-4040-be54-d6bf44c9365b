<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use App\Models\{Ship, LocationType, Location, AssetCategory, Asset, ShipAsset, MasterEntry};

class Sagar1BasicSeeder extends Seeder
{
    public function run(): void
    {
        DB::transaction(function () {
            // 1. Ship
            $ship = Ship::firstOrCreate([
                'imo_number' => '7603356', // Use real IMO to match existing
            ], [
                'official_number' => '3331/AUQU',
                'name' => 'SAGAR I',
                'call_sign' => 'SAGAR1',
                'length_overall' => 0,
                'breadth' => 0,
                'depth' => 0,
                'draft' => 0,
                'gross_tonnage' => 303.0,
                'net_tonnage' => null,
                'deadweight' => null,
                'year_built' => null,
                'builder' => null,
                'port_of_registry' => 'Mumbai',
                'commission_date' => '2024-01-08',
                'flag_id' => 1,
                'ship_type_id' => 7,
                'ship_class_id' => 2,
                'company_id' => 1,
                'status_id' => 1,
            ]);

            // 2. Location Purpose

            // 3. Full Location/Asset Structure (map of name => [type, asset array])
            $structure = require __DIR__ . '/fixtures/sagar1_structure.php'; // move the big map to separate file for cleanliness

            $locationTypeIds = [];
            $assetCategoryIds = [];

            foreach ($structure as $deckName => $data) {
                // 3a. Auto-detect Location Type
                $typeGuess = $this->guessLocationType($deckName);
                $locationTypeIds[$typeGuess] = $locationTypeIds[$typeGuess]
                    ?? LocationType::firstOrCreate(['name' => $typeGuess])->id;

                // 3b. Create Location (Deck)
                $deckLocation = Location::firstOrCreate([
                    'ship_id' => $ship->id,
                    'name' => $deckName,
                ], [
                    'location_type_id' => $locationTypeIds[$typeGuess],
                    'purpose_id' => $this->guessLocationPurposeId($deckName),

                ]);

                // 3c. Assets under the location
                foreach ($data['assets'] as $itemNameRaw) {
                    $qty = $this->extractQty($itemNameRaw);
                    $itemName = $this->cleanAssetName($itemNameRaw);

                    // 3d. AI-detect safety flags
                    $flags = $this->detectFlags($itemName);

                    // 3e. Smart asset category
                    $categoryKey = $flags['category'];
                    $assetCategoryIds[$categoryKey] = $assetCategoryIds[$categoryKey]
                        ?? AssetCategory::firstOrCreate(['name' => $categoryKey], [
                            'is_compliance' => $flags['is_compliance'],
                            'is_fire_safety' => $flags['is_fire_safety'],
                            'survey_required' => $flags['survey_required'],
                        ])->id;

                    // 3f. Create asset
                    $asset = Asset::firstOrCreate(['name' => $itemName], [
                        'type_id' => $this->guessAssetTypeId($itemName),
                        'consumable_type_id' => $this->guessConsumableTypeId($itemName),
                        'category_id' => $assetCategoryIds[$categoryKey],
                        'unit' => 'unit',
                        'is_fire_safety' => $flags['is_fire_safety'],
                        'is_compliance' => $flags['is_compliance'],
                        'survey_required' => $flags['survey_required'],
                    ]);


                    // 3g. Assign asset to ship
                    ShipAsset::firstOrCreate([
                        'ship_id' => $ship->id,
                        'asset_id' => $asset->id,
                        'location_id' => $deckLocation->id,
                    ], [
                        'quantity' => $qty,
                        'created_by' => 1,
                        'is_compliance' => $flags['is_compliance'],
                        'is_fire_safety' => $flags['is_fire_safety'],
                        'survey_required' => $flags['survey_required'],
                    ]);
                }
            }

            $this->command->info("✅ Sagar 1 ship, locations, assets, and assignments seeded successfully.");
        });
    }

    private function guessLocationType(string $name): string
    {
        $name = strtolower($name);
        return match (true) {
            str_contains($name, 'cabin') => 'Cabin',
            str_contains($name, 'deck') => 'Deck',
            str_contains($name, 'store') => 'Store',
            str_contains($name, 'engine') => 'Engine',
            str_contains($name, 'room') => 'Room',
            str_contains($name, 'bridge') => 'Control',
            str_contains($name, 'kitchen') => 'Room',
            default => 'Deck',
        };
    }

    private function extractQty(string $str): int
    {
        if (preg_match('/(\d+)\s?(NOS|LTR|LTRS|PCS|NO\.?|PCS\.?)/i', $str, $match)) {
            return (int)$match[1];
        }
        return 1;
    }

    private function cleanAssetName(string $str): string
    {
        return trim(preg_replace('/\d+\s?(NOS|LTR|LTRS|PCS|NO\.?|PCS\.?)/i', '', $str));
    }

    private function detectFlags(string $name): array
    {
        $name = strtolower($name);

        $is_fire_safety = str_contains($name, 'fire')
            || str_contains($name, 'extinguisher')
            || str_contains($name, 'foam')
            || str_contains($name, 'hose');

        $is_compliance = str_contains($name, 'plan')
            || str_contains($name, 'alarm')
            || str_contains($name, 'detector');

        $survey_required = str_contains($name, 'life')
            || str_contains($name, 'raft')
            || str_contains($name, 'epirb');

        $category = match (true) {
            $is_fire_safety => 'Fire Safety',
            $survey_required => 'Survey Equipment',
            $is_compliance => 'Compliance',
            default => 'General Equipment',
        };

        return [
            'is_fire_safety' => $is_fire_safety,
            'is_compliance' => $is_compliance,
            'survey_required' => $survey_required,
            'category' => $category,
        ];
    }

    private function guessLocationPurposeId(string $name): ?int
    {
        $name = strtolower($name);

        $map = [
            'engine' => 'engine',
            'bridge' => 'bridge',
            'crew' => 'crew_quarters',
            'cabin' => 'crew_quarters',
            'kitchen' => 'galley',
            'mess' => 'galley',
            'store' => 'storage',
            'bunker' => 'storage',
            'water' => 'storage',
            'deck' => 'general',
            'room' => 'general',
        ];

        foreach ($map as $keyword => $code) {
            if (str_contains($name, $keyword)) {
                return MasterEntry::whereHas('type', fn($q) => $q->where('name', 'location_purpose'))
                    ->where('code', $code)
                    ->value('id');
            }
        }

        // fallback to general
        return MasterEntry::whereHas('type', fn($q) => $q->where('name', 'location_purpose'))
            ->where('code', 'general')
            ->value('id');
    }


    private function guessAssetTypeId(string $name): int
    {
        $name = strtolower($name);

        // Heuristics: anything with "filter", "oil", "grease", etc. → consumable
        if (str_contains($name, 'filter') || str_contains($name, 'oil') || str_contains($name, 'grease') || str_contains($name, 'foam')) {
            $code = 'consumable';
        } else {
            $code = 'equipment';
        }

        return MasterEntry::whereHas('type', fn($q) => $q->where('name', 'asset_type'))
            ->where('code', $code)
            ->value('id');
    }
    private function guessConsumableTypeId(string $name): int
    {
        $name = strtolower($name);

        $code = match (true) {
            str_contains($name, 'oil'), str_contains($name, 'fuel'), str_contains($name, 'diesel') => 'quantity',
            str_contains($name, 'filter'), str_contains($name, 'grease') => 'hours',
            default => 'none',
        };

        return MasterEntry::whereHas('type', fn($q) => $q->where('name', 'asset_consumable_type'))
            ->where('code', $code)
            ->value('id');
    }
}
