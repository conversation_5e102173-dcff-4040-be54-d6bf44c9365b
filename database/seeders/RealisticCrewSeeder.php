<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Carbon\Carbon;
use Spatie\Permission\Models\Role;

use App\Models\{
    User, Crew, CrewCertificate,
    ShipCrewPositionRequirement, MasterEntry
};

class RealisticCrewSeeder extends Seeder
{
    public function run()
    {
        DB::transaction(function () {
            // 1. Create 120 users
            User::factory()->count(120)->create();

            // 2. Assign Crew Role
            $crewRole = Role::where('name', 'Crew')->first();
            User::all()->each(fn($user) => $user->assignRole($crewRole));

            // 3. Create Crew profiles
               $statusId = 25;
            User::all()->each(function ($user, $i) use ($statusId) {
                Crew::create([
                    'user_id' => $user->id,
                    'unique_crew_id' => 'CRW' . str_pad($i + 1, 4, '0', STR_PAD_LEFT),
                    'aadhaar_number' => '99998888' . str_pad($i, 4, '0', STR_PAD_LEFT),
                    'status_id' => $statusId,
                ]);
            });

            $crews = Crew::all();
            $crewIndex = 0;

            // 4. For each position, prepare a crew with valid certificates
            ShipCrewPositionRequirement::all()->each(function ($position) use (&$crews, &$crewIndex) {
                $crew = $crews[$crewIndex++] ?? null;
                if (!$crew) return;

                foreach ($position->requirementSetGroups as $group) {
                    $sets = $group->sets()->inRandomOrder()->take(rand(1, 2))->get();
                    foreach ($sets as $set) {
                        foreach ($set->certificates as $requirement) {
                            if (!$requirement->certificateType) continue;

                            CrewCertificate::updateOrCreate([
                                'crew_id' => $crew->id,
                                'crew_certificate_type_id' => $requirement->certificate_type_id,
                            ], [
                                'certificate_number' => strtoupper(Str::random(10)),
                                'issue_date' => now()->subYears(rand(1, 5))->toDateString(),
                                'expiry_date' => now()->addYears(rand(1, 5))->toDateString(),
                                'renewal_date' => now()->addYears(rand(1, 4))->toDateString(),
                                'issuing_authority' => fake()->company(),
                                'notes' => 'Auto-seeded',
                            ]);
                        }
                    }
                }
            });

            // 5. Remaining crews: create with random certs (optional)
            $remainingCrews = $crews->slice($crewIndex);

            foreach ($remainingCrews as $crew) {
                $types = \App\Models\CrewCertificateType::inRandomOrder()->take(rand(2, 6))->get();

                foreach ($types as $type) {
                    CrewCertificate::updateOrCreate([
                        'crew_id' => $crew->id,
                        'crew_certificate_type_id' => $type->id,
                    ], [
                        'certificate_number' => strtoupper(Str::random(10)),
                        'issue_date' => now()->subYears(rand(1, 5))->toDateString(),
                        'expiry_date' => now()->addYears(rand(1, 5))->toDateString(),
                        'renewal_date' => now()->addYears(rand(1, 4))->toDateString(),
                        'issuing_authority' => fake()->company(),
                        'notes' => 'Random-seeded',
                    ]);
                }
            }
        });
    }
}
