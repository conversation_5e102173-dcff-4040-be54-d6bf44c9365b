<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class DesignationsSeeder extends Seeder
{
    public function run()
    {
        $designations = [
            [
                'name' => 'Captain',
                'description' => 'Ship commanding officer',
                'requires_login' => true
            ],
            [
                'name' => 'Chief Engineer',
                'description' => 'Head of engineering department',
                'requires_login' => true
            ],
            [
                'name' => 'Deck Officer',
                'description' => 'Navigation and cargo operations',
                'requires_login' => true
            ],
            [
                'name' => 'Engineer',
                'description' => 'Machinery maintenance',
                'requires_login' => true
            ],
            [
                'name' => 'Bosun',
                'description' => 'Deck crew supervisor',
                'requires_login' => true
            ],
            [
                'name' => 'Able Seaman',
                'description' => 'General deck duties',
                'requires_login' => true
            ],
            [
                'name' => 'Fleet Manager',
                'description' => 'Oversee vessel operations',
                'requires_login' => true
            ],
            [
                'name' => 'Surveyor',
                'description' => 'Inspection specialist',
                'requires_login' => true
            ],
            [
                'name' => 'Customer',
                'description' => 'External client',
                'requires_login' => false
            ],
            [
                'name' => 'Technical Superintendent',
                'description' => 'Technical operations manager',
                'requires_login' => true
            ],
        ];

        DB::table('designations')->insert($designations);
    }
}
