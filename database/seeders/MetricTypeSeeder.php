<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\MetricType;

class MetricTypeSeeder extends Seeder
{
    public function run()
    {
        $metrics = [
            // Fuel Tanks / Engine
            [
                'code' => 'fuel_level',
                'name' => 'Fuel Level',
                'default_unit' => 'L', // Litres
                'data_type' => 'decimal',
                'description' => 'Measures tank fuel volume',
            ],
            [
                'code' => 'water_level',
                'name' => 'Water Level',
                'default_unit' => 'L', // Litres
                'data_type' => 'decimal',
                'description' => 'Water tank volume (fresh/ballast/etc)',
            ],
            [
                'code' => 'engine_hours',
                'name' => 'Engine Hours',
                'default_unit' => 'hr',
                'data_type' => 'decimal',
                'description' => 'Tracks total running hours for engines/generators',
            ],
            [
                'code' => 'lubricant_used',
                'name' => 'Lubricant Used',
                'default_unit' => 'L',
                'data_type' => 'decimal',
                'description' => 'Lubricant consumption',
            ],
            [
                'code' => 'coolant_level',
                'name' => 'Coolant Level',
                'default_unit' => 'L',
                'data_type' => 'decimal',
                'description' => 'Coolant volume in engine/auxiliary systems',
            ],
            [
                'code' => 'temperature',
                'name' => 'Temperature',
                'default_unit' => '°C',
                'data_type' => 'decimal',
                'description' => 'Temperature reading (engine/room/compartment)',
            ],
            [
                'code' => 'pressure',
                'name' => 'Pressure',
                'default_unit' => 'bar',
                'data_type' => 'decimal',
                'description' => 'Pressure (air/oil/hydraulic/etc)',
            ],
            [
                'code' => 'rpm',
                'name' => 'RPM',
                'default_unit' => 'rpm',
                'data_type' => 'decimal',
                'description' => 'Revolutions per minute (engine/equipment)',
            ],
            // Electrical
            [
                'code' => 'voltage',
                'name' => 'Voltage',
                'default_unit' => 'V',
                'data_type' => 'decimal',
                'description' => 'Voltage measurement (battery/equipment)',
            ],
            [
                'code' => 'current',
                'name' => 'Current',
                'default_unit' => 'A',
                'data_type' => 'decimal',
                'description' => 'Current measurement (amperes)',
            ],
            [
                'code' => 'power',
                'name' => 'Power',
                'default_unit' => 'kW',
                'data_type' => 'decimal',
                'description' => 'Power output/input',
            ],
            // Consumables
            [
                'code' => 'quantity',
                'name' => 'Quantity',
                'default_unit' => 'pcs',
                'data_type' => 'int',
                'description' => 'Item count for consumables (e.g., PPE, kits, etc.)',
            ],
            // Miscellaneous
            [
                'code' => 'ph',
                'name' => 'pH',
                'default_unit' => '',
                'data_type' => 'decimal',
                'description' => 'pH measurement (water/chemicals)',
            ],
            [
                'code' => 'humidity',
                'name' => 'Humidity',
                'default_unit' => '%',
                'data_type' => 'decimal',
                'description' => 'Humidity percentage',
            ],
            [
                'code' => 'salinity',
                'name' => 'Salinity',
                'default_unit' => 'ppt',
                'data_type' => 'decimal',
                'description' => 'Salinity measurement (parts per thousand)',
            ],
        ];

        foreach ($metrics as $metric) {
            MetricType::updateOrCreate(['code' => $metric['code']], $metric);
        }
    }
}
