<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class TagsSeeder extends Seeder
{
    public function run()
    {
        $tags = [
            // Asset Module
            ['name' => 'Critical Spare', 'module' => 'asset', 'description' => 'Essential spare parts for operations'],
            ['name' => 'Maintenance Required', 'module' => 'asset', 'description' => 'Assets needing immediate maintenance'],
            ['name' => 'Safety Equipment', 'module' => 'asset', 'description' => 'Safety-related assets'],

            // Ship Module
            ['name' => 'Tug Boat', 'module' => 'ship', 'description' => 'Tug boat classification'],
            ['name' => 'Cargo Vessel', 'module' => 'ship', 'description' => 'Cargo vessel classification'],
            ['name' => 'Passenger Ship', 'module' => 'ship', 'description' => 'Passenger vessel classification'],

            // Survey Module
            ['name' => 'Annual Survey', 'module' => 'survey', 'description' => 'Annual certification surveys'],
            ['name' => 'Damage Assessment', 'module' => 'survey', 'description' => 'Surveys for damage evaluation'],

            // Crew Module
            ['name' => 'Certified', 'module' => 'crew', 'description' => 'Crew with valid certifications'],
            ['name' => 'Training Required', 'module' => 'crew', 'description' => 'Crew needing additional training'],

            // Compliance Module
            ['name' => 'SOLAS', 'module' => 'compliance', 'description' => 'Safety of Life at Sea compliance'],
            ['name' => 'ISM', 'module' => 'compliance', 'description' => 'International Safety Management'],

            // General
            ['name' => 'Urgent', 'module' => null, 'description' => 'High priority items'],
            ['name' => 'Archived', 'module' => null, 'description' => 'Inactive records'],
        ];

        DB::table('tags')->insert($tags);
    }
}
