<?php

namespace Database\Seeders;
use App\Models\CrewCertificateType;
use Illuminate\Database\Seeder;

class CrewCertificateTypeSeeder extends Seeder
{
    public function run()
    {
        $types = [
            ['name' => 'Master Certificate', 'code' => 'MC'],
            ['name' => 'Second Officer', 'code' => 'SO'],
            ['name' => 'GMDSS Certificate', 'code' => 'GMDSS'],
            ['name' => 'CDC', 'code' => 'CDC'],
            ['name' => 'Passport', 'code' => 'PASS'],
            ['name' => 'Medical Certificate', 'code' => 'MED'],
            ['name' => 'Basic Safety Training', 'code' => 'BST'],
            // Add all required...
            //php artisan db:seed --class=CrewCertificateTypeSeeder
        ];
        foreach ($types as $t) {
            CrewCertificateType::firstOrCreate($t);
        }
    }
}
