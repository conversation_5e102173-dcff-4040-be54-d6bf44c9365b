<?php
namespace Database\Seeders;
// database/seeders/MasterTypeSeeder.php
use App\Models\MasterEntry;
use App\Models\MasterType;
use Illuminate\Database\Seeder;

class MasterTypeSeeder extends Seeder
{
    public function run()
    {
        $types = [
            // Ship Statuses
            [
                'name' => 'ship_status',
                'entries' => [
                    ['code' => 'active', 'name' => 'Active', 'is_default' => true],
                    ['code' => 'inactive', 'name' => 'Inactive'],
                    ['code' => 'under_repair', 'name' => 'Under Repair'],
                    ['code' => 'scrapped', 'name' => 'Scrapped'],
                ]
            ],

            // Location Types
            [
                'name' => 'location_type',
                'entries' => [
                    ['code' => 'deck', 'name' => 'Deck'],
                    ['code' => 'side', 'name' => 'Side'],
                    ['code' => 'compartment', 'name' => 'Compartment'],
                    ['code' => 'room', 'name' => 'Room'],
                    ['code' => 'engine', 'name' => 'Engine'],
                    ['code' => 'custom', 'name' => 'Custom', 'is_default' => true],
                ]
            ],

            // Asset Types
            [
                'name' => 'asset_type',
                'entries' => [
                    ['code' => 'equipment', 'name' => 'Equipment'],
                    ['code' => 'consumable', 'name' => 'Consumable'],
                ]
            ],

            // Asset Consumable Types
            [
                'name' => 'asset_consumable_type',
                'entries' => [
                    ['code' => 'none', 'name' => 'None', 'is_default' => true],
                    ['code' => 'quantity', 'name' => 'Quantity'],
                    ['code' => 'hours', 'name' => 'Hours'],
                ]
            ],

            // Checklist Run Types
            [
                'name' => 'checklist_run_type',
                'entries' => [
                    ['code' => 'manual', 'name' => 'Manual', 'is_default' => true],
                    ['code' => 'survey', 'name' => 'Survey'],
                ]
            ],

            // Asset Transaction Types
            [
                'name' => 'asset_transaction_type',
                'entries' => [
                    ['code' => 'initial', 'name' => 'Initial'],
                    ['code' => 'consumption', 'name' => 'Consumption'],
                    ['code' => 'addition', 'name' => 'Addition'],
                    ['code' => 'adjustment', 'name' => 'Adjustment'],
                ]
            ],

            // Asset Movement Types
            [
                'name' => 'asset_movement_type',
                'entries' => [
                    ['code' => 'transfer', 'name' => 'Transfer'],
                    ['code' => 'purchase', 'name' => 'Purchase'],
                    ['code' => 'manual_adjustment', 'name' => 'Manual Adjustment'],
                ]
            ],

            // Crew Statuses
            [
                'name' => 'crew_status',
                'entries' => [
                    ['code' => 'active', 'name' => 'Active', 'is_default' => true],
                    ['code' => 'pending', 'name' => 'Pending'],
                    ['code' => 'on_leave', 'name' => 'On Leave'],
                    ['code' => 'inactive', 'name' => 'Inactive'],
                ]
            ],

            // Crew Event Types
            [
                'name' => 'crew_event_type',
                'entries' => [
                    ['code' => 'onboard', 'name' => 'Onboard'],
                    ['code' => 'transfer', 'name' => 'Transfer'],
                    ['code' => 'promotion', 'name' => 'Promotion'],
                    ['code' => 'offboard', 'name' => 'Offboard'],
                    ['code' => 'medical_leave', 'name' => 'Medical Leave'],
                    ['code' => 'leave', 'name' => 'Leave'],
                    ['code' => 'sick_leave', 'name' => 'Sick Leave'],
                    ['code' => 'change_of_command', 'name' => 'Change of Command'],
                ]
            ],

            // Frequency Types
            [
                'name' => 'frequency_type',
                'entries' => [
                    ['code' => 'daily', 'name' => 'Daily'],
                    ['code' => 'weekly', 'name' => 'Weekly'],
                    ['code' => 'monthly', 'name' => 'Monthly'],
                    ['code' => 'quarterly', 'name' => 'Quarterly'],
                    ['code' => 'yearly', 'name' => 'Yearly'],
                    ['code' => 'emergency', 'name' => 'Emergency'],
                ]
            ],

            // Survey Types
            [
                'name' => 'survey_type',
                'entries' => [
                    ['code' => 'class', 'name' => 'Class'],
                    ['code' => 'internal', 'name' => 'Internal', 'is_default' => true],
                ]
            ],

            // Survey Statuses
            [
                'name' => 'survey_status',
                'entries' => [
                    ['code' => 'pending', 'name' => 'Pending', 'is_default' => true],
                    ['code' => 'in_progress', 'name' => 'In Progress'],
                    ['code' => 'completed', 'name' => 'Completed'],
                    ['code' => 'approved', 'name' => 'Approved'],
                    ['code' => 'rejected', 'name' => 'Rejected'],
                ]
            ],

            // Towage Request Statuses
            [
                'name' => 'towage_request_status',
                'entries' => [
                    ['code' => 'pending', 'name' => 'Pending', 'is_default' => true],
                    ['code' => 'approved', 'name' => 'Approved'],
                    ['code' => 'rejected', 'name' => 'Rejected'],
                    ['code' => 'confirmed', 'name' => 'Confirmed'],
                ]
            ],

            // Approval Statuses
            [
                'name' => 'approval_status',
                'entries' => [
                    ['code' => 'pending', 'name' => 'Pending', 'is_default' => true],
                    ['code' => 'approved', 'name' => 'Approved'],
                    ['code' => 'rejected', 'name' => 'Rejected'],
                    ['code' => 'skipped', 'name' => 'Skipped'],
                ]
            ],

            // Input Types
            [
                'name' => 'input_type',
                'entries' => [
                    ['code' => 'boolean', 'name' => 'Boolean', 'is_default' => true],
                    ['code' => 'text', 'name' => 'Text'],
                    ['code' => 'number', 'name' => 'Number'],
                    ['code' => 'select_single', 'name' => 'Select (Single)'],
                    ['code' => 'select_multiple', 'name' => 'Select (Multiple)'],
                    ['code' => 'image_upload', 'name' => 'Image Upload'],
                    ['code' => 'file_upload', 'name' => 'File Upload'],
                    ['code' => 'date', 'name' => 'Date'],
                    ['code' => 'time', 'name' => 'Time'],
                    ['code' => 'datetime', 'name' => 'DateTime'],
                    ['code' => 'textarea', 'name' => 'Textarea'],
                    ['code' => 'select', 'name' => 'Select (Dynamic)', 'is_default' => true],
                ]
            ],
            [
                'name' => 'validity_unit',
                'entries' => [
                    ['code' => 'days', 'name' => 'Days'],
                    ['code' => 'months', 'name' => 'Months'],
                    ['code' => 'years', 'name' => 'Years'],
                ]
            ],

        ];



         foreach ($types as $type) {
            $masterType = MasterType::firstOrCreate(['name' => $type['name']]);

            foreach ($type['entries'] as $entry) {
                MasterEntry::updateOrCreate(
                    [
                        'master_type_id' => $masterType->id,
                        'code' => $entry['code'],
                    ],
                    [
                        'name' => $entry['name'],
                        'is_default' => $entry['is_default'] ?? false,
                    ]
                );
            }
        }
    }
}
