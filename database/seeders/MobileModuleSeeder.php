<?php
namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\MobileModule;
use Spatie\Permission\Models\Permission;
use Illuminate\Support\Str;

class MobileModuleSeeder extends Seeder
{
    public function run(): void
    {
        $modules = [
            'Dashboard',
            'CrewManagement',
            'SurveyDashboard',
            'ShipList',
            'Notifications',
            'TripManagement',
            'PreventiveMaintenanceManagement',
            'AssetLogs',
            'AssetScreen',
            'MockDrillManagement',
            'TrainingManagement',
            'ShipManagement',
            'ShipDocuments',
        ];

        foreach ($modules as $moduleName) {
            $key = Str::slug($moduleName, '_');
            $module = MobileModule::firstOrCreate(
                ['key' => $key],
                ['name' => $moduleName, 'icon' => 'fas fa-cube']
            );

            foreach (['view', 'create', 'update', 'delete'] as $action) {
                $permName = "{$key}.{$action}";

                $permission = Permission::firstOrCreate(['name' => $permName]);

                $module->permissions()->syncWithoutDetaching([$permission->id]);
            }
        }
    }
}
