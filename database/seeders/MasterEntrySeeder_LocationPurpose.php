<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use App\Models\MasterEntry;
use App\Models\MasterType;
use Illuminate\Database\Seeder;

class MasterEntrySeeder_LocationPurpose extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        //
         $locationPurposeType = MasterType::firstOrCreate(['name' => 'location_purpose']);

        // Add common location purposes
        $purposes = [
            ['code' => 'storage', 'name' => 'Storage'],
            ['code' => 'crew_quarters', 'name' => 'Crew Quarters'],
            ['code' => 'engine', 'name' => 'Engine'],
            ['code' => 'galley', 'name' => 'Galley'],
            ['code' => 'navigation', 'name' => 'Navigation'],
            ['code' => 'bridge', 'name' => 'Bridge'],
            ['code' => 'maintenance', 'name' => 'Maintenance'],
            ['code' => 'general', 'name' => 'General', 'is_default' => true],
        ];
        foreach ($purposes as $entry) {
            MasterEntry::firstOrCreate([
                'master_type_id' => $locationPurposeType->id,
                'code' => $entry['code'],
            ], [
                'name' => $entry['name'],
                'is_default' => $entry['is_default'] ?? false,
            ]);
        }
    }
}
