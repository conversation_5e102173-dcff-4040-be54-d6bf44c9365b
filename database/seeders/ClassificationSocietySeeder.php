<?php

namespace Database\Seeders;
use App\Models\ClassificationSociety;
use Illuminate\Database\Seeder;


class ClassificationSocietySeeder extends Seeder
{
    public function run()
    {
        $societies = [
            ['code' => 'ABS', 'name' => 'American Bureau of Shipping', 'website' => 'https://www.eagle.org'],
            ['code' => 'LR', 'name' => 'Lloyd\'s Register', 'website' => 'https://www.lr.org'],
            ['code' => 'DNV', 'name' => 'DNV GL', 'website' => 'https://www.dnvgl.com'],
        ];

        foreach ($societies as $society) {
            ClassificationSociety::create($society);
        }
    }
}

