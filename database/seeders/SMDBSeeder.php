<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Ship;
use App\Models\ShipMark;
use App\Models\Designation;
use App\Models\CrewCertificateType;
use App\Models\ShipCrewPositionRequirement;
use App\Models\CrewCertificateRequirementSet;
use App\Models\CrewCertificateRequirement;
use App\Models\RequirementSetGroup;
use App\Models\PositionRequirementSetGroupAssignment;
use App\Models\ShipType;
use App\Models\ShipClass;
use Carbon\Carbon;

class SMDBSeeder extends Seeder
{
    public function run()
    {
        // 1. SHIP and MARKS
        // Lookup or create the ship type and class as required by your masters
        $shipType = ShipType::firstOrCreate(['name' => 'TUG', 'code' => 'T1']);
        $shipClass = ShipClass::firstOrCreate(['name' => 'Main', 'code' => 'C1']); // change 'Main' to your real class if needed



        // Now create the ship with correct columns
        $ship = Ship::firstOrCreate(
            [
                'imo_number' => '7603356',
                'name' => 'SAGAR I',
            ],
            [
                'official_number' => '3331/AUQU',
                'call_sign' => 'SAGAR1', // Update if you have real value
                'ship_type_id' => $shipType->id,
                'ship_class_id' => $shipClass->id,
                'company_id' => 1, // Set to a real company id as per your DB
                'length_overall' => 0, // Set if you want, else remove
                'breadth' => 0,
                'depth' => 0,
                'draft' => 0,
                'gross_tonnage' => 303,
                'net_tonnage' => null,
                'deadweight' => null,
                'year_built' => null,
                'builder' => null,
                'hull_number' => null,
                'status_id' => 1, // Set if you have statuses
                'commission_date' => Carbon::parse('2024-01-08'), // Example
                'decommission_date' => null,
                'flag_id' => 1, // Set if you have flags
                'port_of_registry' => 'Mumbai',
                'notes' => null,
                // You can add current_mark_id/mark_changed_at if needed, or let them be null
            ]
        );

        $markOffshore = ShipMark::firstOrCreate(
            ['code' => 'indian-offshore-oilfields'],
            [
                'name' => 'INDIAN OFFSHORE OILFIELDS',
                'description' => null,
            ]
        );

        // $markHarbour = ShipMark::firstOrCreate(
        //     ['code' => 'indian-harbour-operations'],
        //     [
        //         'name' => 'INDIAN HARBOUR OPERATIONS',
        //         'description' => null,
        //     ]
        // );

        // $markCoast = ShipMark::firstOrCreate([
        //     ['code' => 'indian-harbour-operations'],
        //     [
        //         'name'    => 'INDIAN COAST (FAIR WEATHER ONLY)'
        //     ]
        // ]);
        // ...and so on for every mark you want

        // 2. DESIGNATIONS (with mapping to all as per document)
        $designationNames = [
            'Master (FG)',
            'Master (NCV)',
            'Mate (FG)',
            'Mate (NCV)',
            'Chief Engineer',
            'Second Engineer',
            'GMDSS Operator',
            'Deck Rating',
            'Cook',
            'Officer in charge of Navigational watch',
            'Rating Forming Part of Navigational Watch',
            'Rating E/R Rating',
            'Rating Forming Part of Engine Room Watch',
        ];
        $designations = [];
        foreach ($designationNames as $name) {
            $designations[$name] = Designation::firstOrCreate(['name' => $name]);
        }

        // 3. CERTIFICATE TYPES (all explicit codes per your doc)
        $certTypes = [
            ['code' => 'II/3',           'name' => 'Master (FG) Certificate'],
            ['code' => 'II/3-NCV',       'name' => 'Master (NCV) Certificate'],
            ['code' => 'I/3',            'name' => 'I/3 Certificate'],
            ['code' => 'II/1',           'name' => 'Officer in charge of Navigational Watch'],
            ['code' => 'IV/2',           'name' => 'GMDSS Operator Certificate'],
            ['code' => 'STCW-BASIC',     'name' => 'Basic STCW Courses'],
            ['code' => 'III/2',          'name' => 'Chief Engineer (MEO Class II)'],
            ['code' => 'III/3',          'name' => 'MEO Class III (NCV)'],
            ['code' => 'SGED',           'name' => 'SGED Certificate'],
            ['code' => 'ENGDR-I',        'name' => '1st Class Engine Driver'],
            ['code' => 'MLC3.2',         'name' => 'Cook Certificate (MLC 3.2)'],
            ['code' => 'II/4',           'name' => 'Rating Forming Part of Navigational Watch'],
            ['code' => 'II/5',           'name' => 'Rating Forming Part of Navigational Watch II/5'],
            ['code' => 'ER-RATING',      'name' => 'E/R Rating'],
            ['code' => 'III/1',          'name' => 'MEO Class IV'],
        ];
        $certs = [];
        foreach ($certTypes as $cert) {
            $certs[$cert['code']] = CrewCertificateType::firstOrCreate(['code' => $cert['code']], ['name' => $cert['name']]);
        }

        // --- OFFSHORE MARK POSITIONS/REQUIREMENTS ---

        // 1. Master (FG) OR Master (NCV) (II/3 OR [II/3-NCV + I/3])
        $prMaster = ShipCrewPositionRequirement::firstOrCreate([
            'ship_id' => $ship->id,
            'ship_mark_id' => $markOffshore->id,
            'designation_id' => $designations['Master (FG)']->id
        ], ['min_required' => 1, 'max_allowed' => 1, 'is_required' => true]);
        $setMasterFG = CrewCertificateRequirementSet::firstOrCreate(['name' => 'Master FG Path', 'logic_type' => 'ALL']);
        CrewCertificateRequirement::firstOrCreate([
            'requirement_set_id' => $setMasterFG->id,
            'certificate_type_id' => $certs['II/3']->id
        ]);
        $setMasterNCV = CrewCertificateRequirementSet::firstOrCreate(['name' => 'Master NCV Path', 'logic_type' => 'ALL']);
        CrewCertificateRequirement::firstOrCreate([
            'requirement_set_id' => $setMasterNCV->id,
            'certificate_type_id' => $certs['II/3-NCV']->id
        ]);
        CrewCertificateRequirement::firstOrCreate([
            'requirement_set_id' => $setMasterNCV->id,
            'certificate_type_id' => $certs['I/3']->id
        ]);
        $groupMaster = RequirementSetGroup::firstOrCreate(['name' => 'Master OR Group', 'description' => 'Master (FG) or Master (NCV + I/3)']);
        $groupMaster->sets()->sync([$setMasterFG->id, $setMasterNCV->id]);
        PositionRequirementSetGroupAssignment::firstOrCreate([
            'ship_crew_position_requirement_id' => $prMaster->id,
            'requirement_set_group_id' => $groupMaster->id
        ]);

        // 2. Mate (FG) OR Mate (NCV)
        $prMate = ShipCrewPositionRequirement::firstOrCreate([
            'ship_id' => $ship->id,
            'ship_mark_id' => $markOffshore->id,
            'designation_id' => $designations['Mate (FG)']->id
        ], ['min_required' => 1, 'max_allowed' => 1, 'is_required' => true]);
        $setMateFG = CrewCertificateRequirementSet::firstOrCreate(['name' => 'Mate FG Path', 'logic_type' => 'ALL']);
        CrewCertificateRequirement::firstOrCreate([
            'requirement_set_id' => $setMateFG->id,
            'certificate_type_id' => $certs['II/3']->id
        ]);
        $setMateNCV = CrewCertificateRequirementSet::firstOrCreate(['name' => 'Mate NCV Path', 'logic_type' => 'ALL']);
        CrewCertificateRequirement::firstOrCreate([
            'requirement_set_id' => $setMateNCV->id,
            'certificate_type_id' => $certs['II/3-NCV']->id
        ]);
        CrewCertificateRequirement::firstOrCreate([
            'requirement_set_id' => $setMateNCV->id,
            'certificate_type_id' => $certs['I/3']->id
        ]);
        $groupMate = RequirementSetGroup::firstOrCreate(['name' => 'Mate OR Group', 'description' => 'Mate (FG) or Mate (NCV + I/3)']);
        $groupMate->sets()->sync([$setMateFG->id, $setMateNCV->id]);
        PositionRequirementSetGroupAssignment::firstOrCreate([
            'ship_crew_position_requirement_id' => $prMate->id,
            'requirement_set_group_id' => $groupMate->id
        ]);

        // 3. Officer in charge of Navigational watch (NCV): II/1 Read with I/3
        $prOICNav = ShipCrewPositionRequirement::firstOrCreate([
            'ship_id' => $ship->id,
            'ship_mark_id' => $markOffshore->id,
            'designation_id' => $designations['Officer in charge of Navigational watch']->id
        ], ['min_required' => 1, 'max_allowed' => 1, 'is_required' => true]);
        $setOICNav = CrewCertificateRequirementSet::firstOrCreate(['name' => 'OIC Nav Path', 'logic_type' => 'ALL']);
        CrewCertificateRequirement::firstOrCreate([
            'requirement_set_id' => $setOICNav->id,
            'certificate_type_id' => $certs['II/1']->id
        ]);
        CrewCertificateRequirement::firstOrCreate([
            'requirement_set_id' => $setOICNav->id,
            'certificate_type_id' => $certs['I/3']->id
        ]);
        $groupOICNav = RequirementSetGroup::firstOrCreate(['name' => 'OIC Nav Group', 'description' => 'OIC Nav II/1 + I/3']);
        $groupOICNav->sets()->sync([$setOICNav->id]);
        PositionRequirementSetGroupAssignment::firstOrCreate([
            'ship_crew_position_requirement_id' => $prOICNav->id,
            'requirement_set_group_id' => $groupOICNav->id
        ]);

        // 4. GMDSS Operator: IV/2
        $prGMDSS = ShipCrewPositionRequirement::firstOrCreate([
            'ship_id' => $ship->id,
            'ship_mark_id' => $markOffshore->id,
            'designation_id' => $designations['GMDSS Operator']->id
        ], ['min_required' => 1, 'max_allowed' => 1, 'is_required' => true]);
        $setGMDSS = CrewCertificateRequirementSet::firstOrCreate(['name' => 'GMDSS Path', 'logic_type' => 'ALL']);
        CrewCertificateRequirement::firstOrCreate([
            'requirement_set_id' => $setGMDSS->id,
            'certificate_type_id' => $certs['IV/2']->id
        ]);
        $groupGMDSS = RequirementSetGroup::firstOrCreate(['name' => 'GMDSS Group', 'description' => 'GMDSS Operator IV/2']);
        $groupGMDSS->sets()->sync([$setGMDSS->id]);
        PositionRequirementSetGroupAssignment::firstOrCreate([
            'ship_crew_position_requirement_id' => $prGMDSS->id,
            'requirement_set_group_id' => $groupGMDSS->id
        ]);

        // 5. Rating Deck: STCW-BASIC
        $prDeckRating = ShipCrewPositionRequirement::firstOrCreate([
            'ship_id' => $ship->id,
            'ship_mark_id' => $markOffshore->id,
            'designation_id' => $designations['Deck Rating']->id
        ], ['min_required' => 1, 'max_allowed' => 1, 'is_required' => true]);
        $setDeck = CrewCertificateRequirementSet::firstOrCreate(['name' => 'Deck Rating Path', 'logic_type' => 'ALL']);
        CrewCertificateRequirement::firstOrCreate([
            'requirement_set_id' => $setDeck->id,
            'certificate_type_id' => $certs['STCW-BASIC']->id
        ]);
        $groupDeck = RequirementSetGroup::firstOrCreate(['name' => 'Deck Rating Group', 'description' => 'Basic STCW Courses']);
        $groupDeck->sets()->sync([$setDeck->id]);
        PositionRequirementSetGroupAssignment::firstOrCreate([
            'ship_crew_position_requirement_id' => $prDeckRating->id,
            'requirement_set_group_id' => $groupDeck->id
        ]);

        // 6. Cook: MLC 3.2
        $prCook = ShipCrewPositionRequirement::firstOrCreate([
            'ship_id' => $ship->id,
            'ship_mark_id' => $markOffshore->id,
            'designation_id' => $designations['Cook']->id
        ], ['min_required' => 1, 'max_allowed' => 1, 'is_required' => true]);
        $setCook = CrewCertificateRequirementSet::firstOrCreate(['name' => 'Cook Path', 'logic_type' => 'ALL']);
        CrewCertificateRequirement::firstOrCreate([
            'requirement_set_id' => $setCook->id,
            'certificate_type_id' => $certs['MLC3.2']->id
        ]);
        $groupCook = RequirementSetGroup::firstOrCreate(['name' => 'Cook Group', 'description' => 'Cook MLC 3.2']);
        $groupCook->sets()->sync([$setCook->id]);
        PositionRequirementSetGroupAssignment::firstOrCreate([
            'ship_crew_position_requirement_id' => $prCook->id,
            'requirement_set_group_id' => $groupCook->id
        ]);

        // 7. Chief Engineer: III/2 OR [III/3 + I/3]
        $prChiefEng = ShipCrewPositionRequirement::firstOrCreate([
            'ship_id' => $ship->id,
            'ship_mark_id' => $markOffshore->id,
            'designation_id' => $designations['Chief Engineer']->id
        ], ['min_required' => 1, 'max_allowed' => 1, 'is_required' => true]);
        $setChiefEngA = CrewCertificateRequirementSet::firstOrCreate(['name' => 'Chief Eng Class II', 'logic_type' => 'ALL']);
        CrewCertificateRequirement::firstOrCreate([
            'requirement_set_id' => $setChiefEngA->id,
            'certificate_type_id' => $certs['III/2']->id
        ]);
        $setChiefEngB = CrewCertificateRequirementSet::firstOrCreate(['name' => 'Chief Eng Class III', 'logic_type' => 'ALL']);
        CrewCertificateRequirement::firstOrCreate([
            'requirement_set_id' => $setChiefEngB->id,
            'certificate_type_id' => $certs['III/3']->id
        ]);
        CrewCertificateRequirement::firstOrCreate([
            'requirement_set_id' => $setChiefEngB->id,
            'certificate_type_id' => $certs['I/3']->id
        ]);
        $groupChiefEng = RequirementSetGroup::firstOrCreate(['name' => 'Chief Eng OR Group', 'description' => 'III/2 or (III/3 + I/3)']);
        $groupChiefEng->sets()->sync([$setChiefEngA->id, $setChiefEngB->id]);
        PositionRequirementSetGroupAssignment::firstOrCreate([
            'ship_crew_position_requirement_id' => $prChiefEng->id,
            'requirement_set_group_id' => $groupChiefEng->id
        ]);

        // 8. Second Engineer: III/1 OR [III/3 + I/3]
        $prSecondEng = ShipCrewPositionRequirement::firstOrCreate([
            'ship_id' => $ship->id,
            'ship_mark_id' => $markOffshore->id,
            'designation_id' => $designations['Second Engineer']->id
        ], ['min_required' => 1, 'max_allowed' => 1, 'is_required' => true]);
        $setSecondEngA = CrewCertificateRequirementSet::firstOrCreate(['name' => 'Second Eng III/1', 'logic_type' => 'ALL']);
        CrewCertificateRequirement::firstOrCreate([
            'requirement_set_id' => $setSecondEngA->id,
            'certificate_type_id' => $certs['III/1']->id
        ]);
        $setSecondEngB = CrewCertificateRequirementSet::firstOrCreate(['name' => 'Second Eng III/3 + I/3', 'logic_type' => 'ALL']);
        CrewCertificateRequirement::firstOrCreate([
            'requirement_set_id' => $setSecondEngB->id,
            'certificate_type_id' => $certs['III/3']->id
        ]);
        CrewCertificateRequirement::firstOrCreate([
            'requirement_set_id' => $setSecondEngB->id,
            'certificate_type_id' => $certs['I/3']->id
        ]);
        $groupSecondEng = RequirementSetGroup::firstOrCreate(['name' => 'Second Eng OR Group', 'description' => 'III/1 or (III/3 + I/3)']);
        $groupSecondEng->sets()->sync([$setSecondEngA->id, $setSecondEngB->id]);
        PositionRequirementSetGroupAssignment::firstOrCreate([
            'ship_crew_position_requirement_id' => $prSecondEng->id,
            'requirement_set_group_id' => $groupSecondEng->id
        ]);

        // ... continued from above ...

        // 9. Rating Forming Part of Navigational Watch: II/4 or II/5 (min_required: 2)
        $prRFPNW = ShipCrewPositionRequirement::firstOrCreate([
            'ship_id' => $ship->id,
            'ship_mark_id' => $markOffshore->id,
            'designation_id' => $designations['Rating Forming Part of Navigational Watch']->id
        ], ['min_required' => 2, 'max_allowed' => 2, 'is_required' => true]);
        $setRFPNW1 = CrewCertificateRequirementSet::firstOrCreate(['name' => 'RFPNW II/4', 'logic_type' => 'ALL']);
        CrewCertificateRequirement::firstOrCreate([
            'requirement_set_id' => $setRFPNW1->id,
            'certificate_type_id' => $certs['II/4']->id
        ]);
        $setRFPNW2 = CrewCertificateRequirementSet::firstOrCreate(['name' => 'RFPNW II/5', 'logic_type' => 'ALL']);
        CrewCertificateRequirement::firstOrCreate([
            'requirement_set_id' => $setRFPNW2->id,
            'certificate_type_id' => $certs['II/5']->id
        ]);
        $groupRFPNW = RequirementSetGroup::firstOrCreate(['name' => 'RFPNW OR Group', 'description' => 'II/4 or II/5']);
        $groupRFPNW->sets()->sync([$setRFPNW1->id, $setRFPNW2->id]);
        PositionRequirementSetGroupAssignment::firstOrCreate([
            'ship_crew_position_requirement_id' => $prRFPNW->id,
            'requirement_set_group_id' => $groupRFPNW->id
        ]);

        // 10. Rating E/R Rating: ER-RATING (min_required: 2)
        $prERRating = ShipCrewPositionRequirement::firstOrCreate([
            'ship_id' => $ship->id,
            'ship_mark_id' => $markOffshore->id,
            'designation_id' => $designations['Rating E/R Rating']->id
        ], ['min_required' => 2, 'max_allowed' => 2, 'is_required' => true]);
        $setERRating = CrewCertificateRequirementSet::firstOrCreate(['name' => 'E/R Rating Path', 'logic_type' => 'ALL']);
        CrewCertificateRequirement::firstOrCreate([
            'requirement_set_id' => $setERRating->id,
            'certificate_type_id' => $certs['ER-RATING']->id
        ]);
        $groupERRating = RequirementSetGroup::firstOrCreate(['name' => 'E/R Rating Group', 'description' => 'E/R Rating Cert']);
        $groupERRating->sets()->sync([$setERRating->id]);
        PositionRequirementSetGroupAssignment::firstOrCreate([
            'ship_crew_position_requirement_id' => $prERRating->id,
            'requirement_set_group_id' => $groupERRating->id
        ]);

        // 11. Rating Forming Part of Engine Room Watch: II/4 or II/5 (min_required: 1)
        $prERWatch = ShipCrewPositionRequirement::firstOrCreate([
            'ship_id' => $ship->id,
            'ship_mark_id' => $markOffshore->id,
            'designation_id' => $designations['Rating Forming Part of Engine Room Watch']->id
        ], ['min_required' => 1, 'max_allowed' => 1, 'is_required' => true]);
        $setERWatch1 = CrewCertificateRequirementSet::firstOrCreate(['name' => 'ER Watch II/4', 'logic_type' => 'ALL']);
        CrewCertificateRequirement::firstOrCreate([
            'requirement_set_id' => $setERWatch1->id,
            'certificate_type_id' => $certs['II/4']->id
        ]);
        $setERWatch2 = CrewCertificateRequirementSet::firstOrCreate(['name' => 'ER Watch II/5', 'logic_type' => 'ALL']);
        CrewCertificateRequirement::firstOrCreate([
            'requirement_set_id' => $setERWatch2->id,
            'certificate_type_id' => $certs['II/5']->id
        ]);
        $groupERWatch = RequirementSetGroup::firstOrCreate(['name' => 'ER Watch OR Group', 'description' => 'II/4 or II/5 (Engine Room Watch)']);
        $groupERWatch->sets()->sync([$setERWatch1->id, $setERWatch2->id]);
        PositionRequirementSetGroupAssignment::firstOrCreate([
            'ship_crew_position_requirement_id' => $prERWatch->id,
            'requirement_set_group_id' => $groupERWatch->id
        ]);

        // 12. Officer in charge of Navigational watch (non-NCV): II/1
        $prOICNavNonNCV = ShipCrewPositionRequirement::firstOrCreate([
            'ship_id' => $ship->id,
            'ship_mark_id' => $markOffshore->id,
            'designation_id' => $designations['Officer in charge of Navigational watch']->id
        ], ['min_required' => 1, 'max_allowed' => 1, 'is_required' => true]);
        $setOICNavNonNCV = CrewCertificateRequirementSet::firstOrCreate(['name' => 'OIC Nav II/1', 'logic_type' => 'ALL']);
        CrewCertificateRequirement::firstOrCreate([
            'requirement_set_id' => $setOICNavNonNCV->id,
            'certificate_type_id' => $certs['II/1']->id
        ]);
        $groupOICNavNonNCV = RequirementSetGroup::firstOrCreate(['name' => 'OIC Nav II/1 Group', 'description' => 'Officer in charge of Navigational Watch II/1']);
        $groupOICNavNonNCV->sets()->sync([$setOICNavNonNCV->id]);
        PositionRequirementSetGroupAssignment::firstOrCreate([
            'ship_crew_position_requirement_id' => $prOICNavNonNCV->id,
            'requirement_set_group_id' => $groupOICNavNonNCV->id
        ]);

        // 13. Add more positions from your tables as needed (use same structure).

        // ----- Special/extra requirements? -----
        // For things like experience, endorsements, etc.:
        // - You can add special certificate types (e.g., "NCV Endorsement", "1 Year Experience") and add them to the requirement set as above.
        // - Or add as a "notes" column on the CrewCertificateRequirement or ShipCrewPositionRequirement if validation is manual.

        // DONE!


        // --- INDIAN HARBOUR OPERATIONS MARK ---
        $markHarbour = ShipMark::firstOrCreate(
            ['code' => 'indian-harbour-operations'],
            ['name' => 'INDIAN HARBOUR OPERATIONS', 'description' => null]
        );

        // 1. Master (NCV) (First Class IV Master): II/3 + I/3
        $prMasterHarb = ShipCrewPositionRequirement::firstOrCreate([
            'ship_id' => $ship->id, 'ship_mark_id' => $markHarbour->id,
            'designation_id' => $designations['Master (NCV)']->id
        ], ['min_required' => 1, 'max_allowed' => 1, 'is_required' => true]);
        $setMasterHarb = CrewCertificateRequirementSet::firstOrCreate(['name' => 'Master NCV Path (Harbour)', 'logic_type' => 'ALL']);
        CrewCertificateRequirement::firstOrCreate(['requirement_set_id' => $setMasterHarb->id, 'certificate_type_id' => $certs['II/3-NCV']->id]);
        CrewCertificateRequirement::firstOrCreate(['requirement_set_id' => $setMasterHarb->id, 'certificate_type_id' => $certs['I/3']->id]);
        $groupMasterHarb = RequirementSetGroup::firstOrCreate(['name' => 'Master NCV Group (Harbour)', 'description' => 'II/3 + I/3']);
        $groupMasterHarb->sets()->sync([$setMasterHarb->id]);
        PositionRequirementSetGroupAssignment::firstOrCreate([
            'ship_crew_position_requirement_id' => $prMasterHarb->id,
            'requirement_set_group_id' => $groupMasterHarb->id
        ]);

        // 2. Mate (NCV) (Second Class IV Chief Mate): I/3
        $prMateHarb = ShipCrewPositionRequirement::firstOrCreate([
            'ship_id' => $ship->id, 'ship_mark_id' => $markHarbour->id,
            'designation_id' => $designations['Mate (NCV)']->id
        ], ['min_required' => 1, 'max_allowed' => 1, 'is_required' => true]);
        $setMateHarb = CrewCertificateRequirementSet::firstOrCreate(['name' => 'Mate NCV Path (Harbour)', 'logic_type' => 'ALL']);
        CrewCertificateRequirement::firstOrCreate(['requirement_set_id' => $setMateHarb->id, 'certificate_type_id' => $certs['I/3']->id]);
        $groupMateHarb = RequirementSetGroup::firstOrCreate(['name' => 'Mate NCV Group (Harbour)', 'description' => 'I/3']);
        $groupMateHarb->sets()->sync([$setMateHarb->id]);
        PositionRequirementSetGroupAssignment::firstOrCreate([
            'ship_crew_position_requirement_id' => $prMateHarb->id,
            'requirement_set_group_id' => $groupMateHarb->id
        ]);

        // 3. GMDSS Operator: IV/2
        $prGMDSSHarb = ShipCrewPositionRequirement::firstOrCreate([
            'ship_id' => $ship->id, 'ship_mark_id' => $markHarbour->id,
            'designation_id' => $designations['GMDSS Operator']->id
        ], ['min_required' => 1, 'max_allowed' => 1, 'is_required' => true]);
        $setGMDSSHarb = CrewCertificateRequirementSet::firstOrCreate(['name' => 'GMDSS Path (Harbour)', 'logic_type' => 'ALL']);
        CrewCertificateRequirement::firstOrCreate(['requirement_set_id' => $setGMDSSHarb->id, 'certificate_type_id' => $certs['IV/2']->id]);
        $groupGMDSSHarb = RequirementSetGroup::firstOrCreate(['name' => 'GMDSS Group (Harbour)', 'description' => 'IV/2']);
        $groupGMDSSHarb->sets()->sync([$setGMDSSHarb->id]);
        PositionRequirementSetGroupAssignment::firstOrCreate([
            'ship_crew_position_requirement_id' => $prGMDSSHarb->id,
            'requirement_set_group_id' => $groupGMDSSHarb->id
        ]);

        // 4. Deck Rating: STCW-BASIC
        $prDeckHarb = ShipCrewPositionRequirement::firstOrCreate([
            'ship_id' => $ship->id, 'ship_mark_id' => $markHarbour->id,
            'designation_id' => $designations['Deck Rating']->id
        ], ['min_required' => 1, 'max_allowed' => 1, 'is_required' => true]);
        $setDeckHarb = CrewCertificateRequirementSet::firstOrCreate(['name' => 'Deck Rating Path (Harbour)', 'logic_type' => 'ALL']);
        CrewCertificateRequirement::firstOrCreate(['requirement_set_id' => $setDeckHarb->id, 'certificate_type_id' => $certs['STCW-BASIC']->id]);
        $groupDeckHarb = RequirementSetGroup::firstOrCreate(['name' => 'Deck Rating Group (Harbour)', 'description' => 'Basic STCW Courses']);
        $groupDeckHarb->sets()->sync([$setDeckHarb->id]);
        PositionRequirementSetGroupAssignment::firstOrCreate([
            'ship_crew_position_requirement_id' => $prDeckHarb->id,
            'requirement_set_group_id' => $groupDeckHarb->id
        ]);

        // 5. Chief Engineer: III/3 + I/3
        $prChiefEngHarb = ShipCrewPositionRequirement::firstOrCreate([
            'ship_id' => $ship->id, 'ship_mark_id' => $markHarbour->id,
            'designation_id' => $designations['Chief Engineer']->id
        ], ['min_required' => 1, 'max_allowed' => 1, 'is_required' => true]);
        $setChiefEngHarb = CrewCertificateRequirementSet::firstOrCreate(['name' => 'Chief Engineer Path (Harbour)', 'logic_type' => 'ALL']);
        CrewCertificateRequirement::firstOrCreate(['requirement_set_id' => $setChiefEngHarb->id, 'certificate_type_id' => $certs['III/3']->id]);
        CrewCertificateRequirement::firstOrCreate(['requirement_set_id' => $setChiefEngHarb->id, 'certificate_type_id' => $certs['I/3']->id]);
        $groupChiefEngHarb = RequirementSetGroup::firstOrCreate(['name' => 'Chief Eng Group (Harbour)', 'description' => 'III/3 + I/3']);
        $groupChiefEngHarb->sets()->sync([$setChiefEngHarb->id]);
        PositionRequirementSetGroupAssignment::firstOrCreate([
            'ship_crew_position_requirement_id' => $prChiefEngHarb->id,
            'requirement_set_group_id' => $groupChiefEngHarb->id
        ]);

        // 6. Second Engineer: I/3
        $prSecondEngHarb = ShipCrewPositionRequirement::firstOrCreate([
            'ship_id' => $ship->id, 'ship_mark_id' => $markHarbour->id,
            'designation_id' => $designations['Second Engineer']->id
        ], ['min_required' => 1, 'max_allowed' => 1, 'is_required' => true]);
        $setSecondEngHarb = CrewCertificateRequirementSet::firstOrCreate(['name' => 'Second Engineer Path (Harbour)', 'logic_type' => 'ALL']);
        CrewCertificateRequirement::firstOrCreate(['requirement_set_id' => $setSecondEngHarb->id, 'certificate_type_id' => $certs['I/3']->id]);
        $groupSecondEngHarb = RequirementSetGroup::firstOrCreate(['name' => 'Second Eng Group (Harbour)', 'description' => 'I/3']);
        $groupSecondEngHarb->sets()->sync([$setSecondEngHarb->id]);
        PositionRequirementSetGroupAssignment::firstOrCreate([
            'ship_crew_position_requirement_id' => $prSecondEngHarb->id,
            'requirement_set_group_id' => $groupSecondEngHarb->id
        ]);

        // 7. Rating Forming Part of Engine Room Watch: II/4 or II/5
        $prEngineWatchHarb = ShipCrewPositionRequirement::firstOrCreate([
            'ship_id' => $ship->id, 'ship_mark_id' => $markHarbour->id,
            'designation_id' => $designations['Rating Forming Part of Engine Room Watch']->id
        ], ['min_required' => 1, 'max_allowed' => 1, 'is_required' => true]);
        $setERWatchHarb1 = CrewCertificateRequirementSet::firstOrCreate(['name' => 'Engine Watch II/4 (Harbour)', 'logic_type' => 'ALL']);
        CrewCertificateRequirement::firstOrCreate(['requirement_set_id' => $setERWatchHarb1->id, 'certificate_type_id' => $certs['II/4']->id]);
        $setERWatchHarb2 = CrewCertificateRequirementSet::firstOrCreate(['name' => 'Engine Watch II/5 (Harbour)', 'logic_type' => 'ALL']);
        CrewCertificateRequirement::firstOrCreate(['requirement_set_id' => $setERWatchHarb2->id, 'certificate_type_id' => $certs['II/5']->id]);
        $groupERWatchHarb = RequirementSetGroup::firstOrCreate(['name' => 'Engine Watch OR Group (Harbour)', 'description' => 'II/4 or II/5']);
        $groupERWatchHarb->sets()->sync([$setERWatchHarb1->id, $setERWatchHarb2->id]);
        PositionRequirementSetGroupAssignment::firstOrCreate([
            'ship_crew_position_requirement_id' => $prEngineWatchHarb->id,
            'requirement_set_group_id' => $groupERWatchHarb->id
        ]);

        // 8. Rating E/R Rating: STCW-BASIC
        $prERRatingHarb = ShipCrewPositionRequirement::firstOrCreate([
            'ship_id' => $ship->id, 'ship_mark_id' => $markHarbour->id,
            'designation_id' => $designations['Rating E/R Rating']->id
        ], ['min_required' => 1, 'max_allowed' => 1, 'is_required' => true]);
        $setERRatingHarb = CrewCertificateRequirementSet::firstOrCreate(['name' => 'E/R Rating Path (Harbour)', 'logic_type' => 'ALL']);
        CrewCertificateRequirement::firstOrCreate(['requirement_set_id' => $setERRatingHarb->id, 'certificate_type_id' => $certs['STCW-BASIC']->id]);
        $groupERRatingHarb = RequirementSetGroup::firstOrCreate(['name' => 'E/R Rating Group (Harbour)', 'description' => 'Basic STCW Courses']);
        $groupERRatingHarb->sets()->sync([$setERRatingHarb->id]);
        PositionRequirementSetGroupAssignment::firstOrCreate([
            'ship_crew_position_requirement_id' => $prERRatingHarb->id,
            'requirement_set_group_id' => $groupERRatingHarb->id
        ]);



        // --- INDIAN COAST (FAIR WEATHER ONLY) MARK ---
        $markCoast = ShipMark::firstOrCreate(
            ['code' => 'indian-coast-fair-weather-only'],
            ['name' => 'INDIAN COAST (FAIR WEATHER ONLY)', 'description' => null]
        );

        // 1. Master (FG): II/2
        $prMasterFGCoast = ShipCrewPositionRequirement::firstOrCreate([
            'ship_id' => $ship->id, 'ship_mark_id' => $markCoast->id,
            'designation_id' => $designations['Master (FG)']->id
        ], ['min_required' => 1, 'max_allowed' => 1, 'is_required' => true]);
        $setMasterFGCoast = CrewCertificateRequirementSet::firstOrCreate(['name' => 'Master FG Path (Coast)', 'logic_type' => 'ALL']);
        CrewCertificateRequirement::firstOrCreate(['requirement_set_id' => $setMasterFGCoast->id, 'certificate_type_id' => $certs['II/3']->id]);
        $groupMasterFGCoast = RequirementSetGroup::firstOrCreate(['name' => 'Master FG Group (Coast)', 'description' => 'II/3']);
        $groupMasterFGCoast->sets()->sync([$setMasterFGCoast->id]);
        PositionRequirementSetGroupAssignment::firstOrCreate([
            'ship_crew_position_requirement_id' => $prMasterFGCoast->id,
            'requirement_set_group_id' => $groupMasterFGCoast->id
        ]);

        // 2. Master (NCV): II/3 + I/3
        $prMasterNCVCoast = ShipCrewPositionRequirement::firstOrCreate([
            'ship_id' => $ship->id, 'ship_mark_id' => $markCoast->id,
            'designation_id' => $designations['Master (NCV)']->id
        ], ['min_required' => 1, 'max_allowed' => 1, 'is_required' => true]);
        $setMasterNCVCoast = CrewCertificateRequirementSet::firstOrCreate(['name' => 'Master NCV Path (Coast)', 'logic_type' => 'ALL']);
        CrewCertificateRequirement::firstOrCreate(['requirement_set_id' => $setMasterNCVCoast->id, 'certificate_type_id' => $certs['II/3-NCV']->id]);
        CrewCertificateRequirement::firstOrCreate(['requirement_set_id' => $setMasterNCVCoast->id, 'certificate_type_id' => $certs['I/3']->id]);
        $groupMasterNCVCoast = RequirementSetGroup::firstOrCreate(['name' => 'Master NCV Group (Coast)', 'description' => 'II/3-NCV + I/3']);
        $groupMasterNCVCoast->sets()->sync([$setMasterNCVCoast->id]);
        PositionRequirementSetGroupAssignment::firstOrCreate([
            'ship_crew_position_requirement_id' => $prMasterNCVCoast->id,
            'requirement_set_group_id' => $groupMasterNCVCoast->id
        ]);

        // 3. Officer In-Charge of Navigational Watch (FG): II/2
        $prOICNavFGCoast = ShipCrewPositionRequirement::firstOrCreate([
            'ship_id' => $ship->id, 'ship_mark_id' => $markCoast->id,
            'designation_id' => $designations['Officer in charge of Navigational watch']->id
        ], ['min_required' => 1, 'max_allowed' => 1, 'is_required' => true]);
        $setOICNavFGCoast = CrewCertificateRequirementSet::firstOrCreate(['name' => 'OIC Nav FG Path (Coast)', 'logic_type' => 'ALL']);
        CrewCertificateRequirement::firstOrCreate(['requirement_set_id' => $setOICNavFGCoast->id, 'certificate_type_id' => $certs['II/1']->id]);
        $groupOICNavFGCoast = RequirementSetGroup::firstOrCreate(['name' => 'OIC Nav FG Group (Coast)', 'description' => 'II/1']);
        $groupOICNavFGCoast->sets()->sync([$setOICNavFGCoast->id]);
        PositionRequirementSetGroupAssignment::firstOrCreate([
            'ship_crew_position_requirement_id' => $prOICNavFGCoast->id,
            'requirement_set_group_id' => $groupOICNavFGCoast->id
        ]);

        // 4. Officer In-Charge of Navigational Watch (NCV): II/1 + I/3
        $prOICNavNCVCoast = ShipCrewPositionRequirement::firstOrCreate([
            'ship_id' => $ship->id, 'ship_mark_id' => $markCoast->id,
            'designation_id' => $designations['Officer in charge of Navigational watch']->id
        ], ['min_required' => 1, 'max_allowed' => 1, 'is_required' => true]);
        $setOICNavNCVCoast = CrewCertificateRequirementSet::firstOrCreate(['name' => 'OIC Nav NCV Path (Coast)', 'logic_type' => 'ALL']);
        CrewCertificateRequirement::firstOrCreate(['requirement_set_id' => $setOICNavNCVCoast->id, 'certificate_type_id' => $certs['II/1']->id]);
        CrewCertificateRequirement::firstOrCreate(['requirement_set_id' => $setOICNavNCVCoast->id, 'certificate_type_id' => $certs['I/3']->id]);
        $groupOICNavNCVCoast = RequirementSetGroup::firstOrCreate(['name' => 'OIC Nav NCV Group (Coast)', 'description' => 'II/1 + I/3']);
        $groupOICNavNCVCoast->sets()->sync([$setOICNavNCVCoast->id]);
        PositionRequirementSetGroupAssignment::firstOrCreate([
            'ship_crew_position_requirement_id' => $prOICNavNCVCoast->id,
            'requirement_set_group_id' => $groupOICNavNCVCoast->id
        ]);

        // 5. Deck Rating: STCW-BASIC (min_required: 2)
        $prDeckCoast = ShipCrewPositionRequirement::firstOrCreate([
            'ship_id' => $ship->id, 'ship_mark_id' => $markCoast->id,
            'designation_id' => $designations['Deck Rating']->id
        ], ['min_required' => 2, 'max_allowed' => 2, 'is_required' => true]);
        $setDeckCoast = CrewCertificateRequirementSet::firstOrCreate(['name' => 'Deck Rating Path (Coast)', 'logic_type' => 'ALL']);
        CrewCertificateRequirement::firstOrCreate(['requirement_set_id' => $setDeckCoast->id, 'certificate_type_id' => $certs['STCW-BASIC']->id]);
        $groupDeckCoast = RequirementSetGroup::firstOrCreate(['name' => 'Deck Rating Group (Coast)', 'description' => 'Basic STCW Courses']);
        $groupDeckCoast->sets()->sync([$setDeckCoast->id]);
        PositionRequirementSetGroupAssignment::firstOrCreate([
            'ship_crew_position_requirement_id' => $prDeckCoast->id,
            'requirement_set_group_id' => $groupDeckCoast->id
        ]);

        // 6. Cook: MLC 3.2
        $prCookCoast = ShipCrewPositionRequirement::firstOrCreate([
            'ship_id' => $ship->id, 'ship_mark_id' => $markCoast->id,
            'designation_id' => $designations['Cook']->id
        ], ['min_required' => 2, 'max_allowed' => 2, 'is_required' => true]);
        $setCookCoast = CrewCertificateRequirementSet::firstOrCreate(['name' => 'Cook Path (Coast)', 'logic_type' => 'ALL']);
        CrewCertificateRequirement::firstOrCreate(['requirement_set_id' => $setCookCoast->id, 'certificate_type_id' => $certs['MLC3.2']->id]);
        $groupCookCoast = RequirementSetGroup::firstOrCreate(['name' => 'Cook Group (Coast)', 'description' => 'Cook MLC 3.2']);
        $groupCookCoast->sets()->sync([$setCookCoast->id]);
        PositionRequirementSetGroupAssignment::firstOrCreate([
            'ship_crew_position_requirement_id' => $prCookCoast->id,
            'requirement_set_group_id' => $groupCookCoast->id
        ]);

        // 7. Chief Engineer: III/2 or (III/3 + I/3)
        $prChiefEngCoast = ShipCrewPositionRequirement::firstOrCreate([
            'ship_id' => $ship->id, 'ship_mark_id' => $markCoast->id,
            'designation_id' => $designations['Chief Engineer']->id
        ], ['min_required' => 1, 'max_allowed' => 1, 'is_required' => true]);
        $setChiefEngCoastA = CrewCertificateRequirementSet::firstOrCreate(['name' => 'Chief Eng Class II (Coast)', 'logic_type' => 'ALL']);
        CrewCertificateRequirement::firstOrCreate(['requirement_set_id' => $setChiefEngCoastA->id, 'certificate_type_id' => $certs['III/2']->id]);
        $setChiefEngCoastB = CrewCertificateRequirementSet::firstOrCreate(['name' => 'Chief Eng Class III (Coast)', 'logic_type' => 'ALL']);
        CrewCertificateRequirement::firstOrCreate(['requirement_set_id' => $setChiefEngCoastB->id, 'certificate_type_id' => $certs['III/3']->id]);
        CrewCertificateRequirement::firstOrCreate(['requirement_set_id' => $setChiefEngCoastB->id, 'certificate_type_id' => $certs['I/3']->id]);
        $groupChiefEngCoast = RequirementSetGroup::firstOrCreate(['name' => 'Chief Eng OR Group (Coast)', 'description' => 'III/2 or (III/3 + I/3)']);
        $groupChiefEngCoast->sets()->sync([$setChiefEngCoastA->id, $setChiefEngCoastB->id]);
        PositionRequirementSetGroupAssignment::firstOrCreate([
            'ship_crew_position_requirement_id' => $prChiefEngCoast->id,
            'requirement_set_group_id' => $groupChiefEngCoast->id
        ]);

        // 8. Second Engineer: III/1 or (III/3 + I/3)
        $prSecondEngCoast = ShipCrewPositionRequirement::firstOrCreate([
            'ship_id' => $ship->id, 'ship_mark_id' => $markCoast->id,
            'designation_id' => $designations['Second Engineer']->id
        ], ['min_required' => 1, 'max_allowed' => 1, 'is_required' => true]);
        $setSecondEngCoastA = CrewCertificateRequirementSet::firstOrCreate(['name' => 'Second Eng III/1 (Coast)', 'logic_type' => 'ALL']);
        CrewCertificateRequirement::firstOrCreate(['requirement_set_id' => $setSecondEngCoastA->id, 'certificate_type_id' => $certs['III/1']->id]);
        $setSecondEngCoastB = CrewCertificateRequirementSet::firstOrCreate(['name' => 'Second Eng III/3 + I/3 (Coast)', 'logic_type' => 'ALL']);
        CrewCertificateRequirement::firstOrCreate(['requirement_set_id' => $setSecondEngCoastB->id, 'certificate_type_id' => $certs['III/3']->id]);
        CrewCertificateRequirement::firstOrCreate(['requirement_set_id' => $setSecondEngCoastB->id, 'certificate_type_id' => $certs['I/3']->id]);
        $groupSecondEngCoast = RequirementSetGroup::firstOrCreate(['name' => 'Second Eng OR Group (Coast)', 'description' => 'III/1 or (III/3 + I/3)']);
        $groupSecondEngCoast->sets()->sync([$setSecondEngCoastA->id, $setSecondEngCoastB->id]);
        PositionRequirementSetGroupAssignment::firstOrCreate([
            'ship_crew_position_requirement_id' => $prSecondEngCoast->id,
            'requirement_set_group_id' => $groupSecondEngCoast->id
        ]);

        // 9. Rating Forming Part of Engine Room Watch: II/4 or II/5
        $prEngineWatchCoast = ShipCrewPositionRequirement::firstOrCreate([
            'ship_id' => $ship->id, 'ship_mark_id' => $markCoast->id,
            'designation_id' => $designations['Rating Forming Part of Engine Room Watch']->id
        ], ['min_required' => 1, 'max_allowed' => 1, 'is_required' => true]);
        $setERWatchCoast1 = CrewCertificateRequirementSet::firstOrCreate(['name' => 'Engine Watch II/4 (Coast)', 'logic_type' => 'ALL']);
        CrewCertificateRequirement::firstOrCreate(['requirement_set_id' => $setERWatchCoast1->id, 'certificate_type_id' => $certs['II/4']->id]);
        $setERWatchCoast2 = CrewCertificateRequirementSet::firstOrCreate(['name' => 'Engine Watch II/5 (Coast)', 'logic_type' => 'ALL']);
        CrewCertificateRequirement::firstOrCreate(['requirement_set_id' => $setERWatchCoast2->id, 'certificate_type_id' => $certs['II/5']->id]);
        $groupERWatchCoast = RequirementSetGroup::firstOrCreate(['name' => 'Engine Watch OR Group (Coast)', 'description' => 'II/4 or II/5']);
        $groupERWatchCoast->sets()->sync([$setERWatchCoast1->id, $setERWatchCoast2->id]);
        PositionRequirementSetGroupAssignment::firstOrCreate([
            'ship_crew_position_requirement_id' => $prEngineWatchCoast->id,
            'requirement_set_group_id' => $groupERWatchCoast->id
        ]);

        // 10. Rating E/R Rating: STCW-BASIC (if required)
        $prERRatingCoast = ShipCrewPositionRequirement::firstOrCreate([
            'ship_id' => $ship->id, 'ship_mark_id' => $markCoast->id,
            'designation_id' => $designations['Rating E/R Rating']->id
        ], ['min_required' => 1, 'max_allowed' => 1, 'is_required' => true]);
        $setERRatingCoast = CrewCertificateRequirementSet::firstOrCreate(['name' => 'E/R Rating Path (Coast)', 'logic_type' => 'ALL']);
        CrewCertificateRequirement::firstOrCreate(['requirement_set_id' => $setERRatingCoast->id, 'certificate_type_id' => $certs['STCW-BASIC']->id]);
        $groupERRatingCoast = RequirementSetGroup::firstOrCreate(['name' => 'E/R Rating Group (Coast)', 'description' => 'Basic STCW Courses']);
        $groupERRatingCoast->sets()->sync([$setERRatingCoast->id]);
        PositionRequirementSetGroupAssignment::firstOrCreate([
            'ship_crew_position_requirement_id' => $prERRatingCoast->id,
            'requirement_set_group_id' => $groupERRatingCoast->id
        ]);

        $this->command->info('SMDBSeeder: ALL SAGAR I / INDIAN OFFSHORE OILFIELDS positions seeded with certificate requirement sets & groups.');

    }
}
