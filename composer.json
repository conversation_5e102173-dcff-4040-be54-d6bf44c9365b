{"$schema": "https://getcomposer.org/schema.json", "name": "siubie/kaido-kit", "type": "project", "description": "Filament Admin Panel Starter Kit with pre-configured packages and settings", "keywords": ["laravel", "framework", "filament", "admin-panel", "starter-kit"], "license": "MIT", "require": {"php": "^8.2", "amendozaaguiar/filament-route-statistics": "^4.2", "bezhansalleh/filament-shield": "^3.3", "bilfeldt/laravel-route-statistics": "^4.2", "dedoc/scramble": "^0.12.10", "dutchcodingcompany/filament-socialite": "^2.3", "filament/filament": "^3.2", "filament/spatie-laravel-media-library-plugin": "^3.2", "filament/spatie-laravel-settings-plugin": "^3.2", "hasnayeen/themes": "*", "jeffgreco13/filament-breezy": "^2.4", "laravel/framework": "^12.0", "laravel/sanctum": "^4.0", "laravel/tinker": "^2.9", "owenvoke/blade-fontawesome": "^2.8", "pxlrbt/filament-environment-indicator": "*", "pxlrbt/filament-excel": "^2.3", "resend/resend-laravel": "^0.16.1", "rupadana/filament-api-service": "^3.4.4", "socialiteproviders/google": "^4.1", "stechstudio/filament-impersonate": "^3.15"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.14", "fakerphp/faker": "^1.23", "laravel-shift/blueprint": "^2.10", "laravel/pail": "^1.1", "laravel/pint": "^1.13", "laravel/sail": "^1.26", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.1", "pestphp/pest": "^3.6", "pestphp/pest-plugin-laravel": "^3.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan filament:upgrade"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi", "@php artisan storage:link"], "dev": ["Composer\\Config::disableProcessTimeout", "npx concurrently -c \"#93c5fd,#c4b5fd,#fb7185,#fdba74\" \"php artisan serve\" \"php artisan queue:listen --tries=1\" \"php artisan pail --timeout=0\" \"npm run dev\" --names=server,queue,logs,vite"], "setup": ["@php artisan migrate:fresh --seed", "@php artisan shield:generate --all --panel=admin", "@php artisan shield:super-admin --user=11"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}